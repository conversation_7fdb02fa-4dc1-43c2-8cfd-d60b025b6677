package scenarios

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestNetworkFailureScenarios 测试网络故障场景
func TestNetworkFailureScenarios(t *testing.T) {
	// 代理服务器端口
	proxyPorts := []int{18081, 18082, 18083, 18084}
	
	t.Run("模拟单个代理故障", func(t *testing.T) {
		// 设置第一个代理为不健康
		err := setProxyHealth(proxyPorts[0], false)
		assert.NoError(t, err, "设置代理健康状态应该成功")
		
		// 验证代理状态
		healthy := checkProxyHealth(proxyPorts[0])
		assert.False(t, healthy, "代理应该是不健康的")
		
		// 测试其他代理仍然工作
		for i := 1; i < len(proxyPorts); i++ {
			healthy := checkProxyHealth(proxyPorts[i])
			assert.True(t, healthy, "其他代理应该仍然健康")
		}
		
		// 恢复代理
		err = setProxyHealth(proxyPorts[0], true)
		assert.NoError(t, err, "恢复代理健康状态应该成功")
		
		t.<PERSON>g("✓ 单个代理故障和恢复测试完成")
	})
	
	t.Run("模拟多个代理故障", func(t *testing.T) {
		// 设置多个代理为不健康
		failedProxies := proxyPorts[:2] // 前两个代理
		
		for _, port := range failedProxies {
			err := setProxyHealth(port, false)
			assert.NoError(t, err, "设置代理 %d 为不健康应该成功", port)
		}
		
		// 验证故障代理
		for _, port := range failedProxies {
			healthy := checkProxyHealth(port)
			assert.False(t, healthy, "代理 %d 应该是不健康的", port)
		}
		
		// 验证剩余代理仍然工作
		workingProxies := proxyPorts[2:]
		for _, port := range workingProxies {
			healthy := checkProxyHealth(port)
			assert.True(t, healthy, "代理 %d 应该仍然健康", port)
		}
		
		// 逐步恢复代理
		for _, port := range failedProxies {
			err := setProxyHealth(port, true)
			assert.NoError(t, err, "恢复代理 %d 应该成功", port)
			
			// 验证恢复
			healthy := checkProxyHealth(port)
			assert.True(t, healthy, "代理 %d 应该已恢复", port)
		}
		
		t.Log("✓ 多个代理故障和恢复测试完成")
	})
	
	t.Run("模拟代理响应延迟", func(t *testing.T) {
		// 设置不同的响应延迟
		delays := []int{100, 500, 1000, 2000} // 毫秒
		
		for i, port := range proxyPorts {
			if i < len(delays) {
				err := setProxyDelay(port, delays[i])
				assert.NoError(t, err, "设置代理 %d 延迟为 %dms 应该成功", port, delays[i])
			}
		}
		
		// 测试响应时间
		for i, port := range proxyPorts {
			if i < len(delays) {
				start := time.Now()
				response := sendProxyRequest(port)
				duration := time.Since(start)
				
				expectedDelay := time.Duration(delays[i]) * time.Millisecond
				assert.GreaterOrEqual(t, duration, expectedDelay, 
					"代理 %d 的响应时间应该至少为 %v", port, expectedDelay)
				assert.NotNil(t, response, "应该收到响应")
				
				t.Logf("✓ 代理 %d 延迟测试: 设置=%dms, 实际=%v", port, delays[i], duration)
			}
		}
		
		// 重置延迟
		for _, port := range proxyPorts {
			err := setProxyDelay(port, 50) // 重置为50ms
			assert.NoError(t, err, "重置代理 %d 延迟应该成功", port)
		}
		
		t.Log("✓ 代理响应延迟测试完成")
	})
	
	t.Run("模拟代理失败率", func(t *testing.T) {
		// 设置不同的失败率
		failureRates := []float64{0.1, 0.2, 0.5} // 10%, 20%, 50%
		
		for i, port := range proxyPorts[:len(failureRates)] {
			err := setProxyFailureRate(port, failureRates[i])
			assert.NoError(t, err, "设置代理 %d 失败率为 %.1f 应该成功", port, failureRates[i])
		}
		
		// 发送多个请求测试失败率
		requestCount := 20
		for i, port := range proxyPorts[:len(failureRates)] {
			successCount := 0
			
			for j := 0; j < requestCount; j++ {
				response := sendProxyRequest(port)
				if response != nil {
					successCount++
				}
			}
			
			successRate := float64(successCount) / float64(requestCount)
			expectedSuccessRate := 1.0 - failureRates[i]
			
			// 允许一定的误差范围
			tolerance := 0.3
			assert.InDelta(t, expectedSuccessRate, successRate, tolerance,
				"代理 %d 的成功率应该接近 %.1f", port, expectedSuccessRate)
			
			t.Logf("✓ 代理 %d 失败率测试: 期望成功率=%.1f, 实际成功率=%.1f", 
				port, expectedSuccessRate, successRate)
		}
		
		// 重置失败率
		for _, port := range proxyPorts {
			err := setProxyFailureRate(port, 0.0)
			assert.NoError(t, err, "重置代理 %d 失败率应该成功", port)
		}
		
		t.Log("✓ 代理失败率测试完成")
	})
}

// TestLoadBalancing 测试负载均衡
func TestLoadBalancing(t *testing.T) {
	proxyPorts := []int{18081, 18082, 18083, 18084}
	
	t.Run("测试轮询负载均衡", func(t *testing.T) {
		// 重置所有代理的统计信息
		for _, port := range proxyPorts {
			stats := getProxyStats(port)
			t.Logf("代理 %d 初始统计: %+v", port, stats)
		}
		
		// 发送多个请求
		requestCount := 20
		responses := make([]map[string]interface{}, 0)
		
		for i := 0; i < requestCount; i++ {
			// 模拟轮询选择代理
			port := proxyPorts[i%len(proxyPorts)]
			response := sendProxyRequest(port)
			if response != nil {
				responses = append(responses, response)
			}
		}
		
		assert.Greater(t, len(responses), requestCount/2, 
			"应该收到大部分请求的响应")
		
		// 检查负载分布
		for _, port := range proxyPorts {
			stats := getProxyStats(port)
			t.Logf("代理 %d 最终统计: %+v", port, stats)
		}
		
		t.Log("✓ 轮询负载均衡测试完成")
	})
	
	t.Run("测试基于质量的负载均衡", func(t *testing.T) {
		// 设置不同的代理质量（通过延迟和失败率）
		// 高质量代理：低延迟，低失败率
		setProxyDelay(proxyPorts[0], 50)
		setProxyFailureRate(proxyPorts[0], 0.01)
		
		// 中等质量代理
		setProxyDelay(proxyPorts[1], 200)
		setProxyFailureRate(proxyPorts[1], 0.05)
		
		// 低质量代理
		setProxyDelay(proxyPorts[2], 1000)
		setProxyFailureRate(proxyPorts[2], 0.2)
		
		// 发送请求并测量性能
		requestCount := 10
		results := make(map[int][]time.Duration)
		
		for _, port := range proxyPorts[:3] {
			results[port] = make([]time.Duration, 0)
			
			for i := 0; i < requestCount; i++ {
				start := time.Now()
				response := sendProxyRequest(port)
				duration := time.Since(start)
				
				if response != nil {
					results[port] = append(results[port], duration)
				}
			}
		}
		
		// 验证高质量代理的响应时间更短
		if len(results[proxyPorts[0]]) > 0 && len(results[proxyPorts[2]]) > 0 {
			avgHighQuality := averageDuration(results[proxyPorts[0]])
			avgLowQuality := averageDuration(results[proxyPorts[2]])
			
			assert.Less(t, avgHighQuality, avgLowQuality,
				"高质量代理的平均响应时间应该更短")
			
			t.Logf("✓ 质量对比: 高质量代理平均=%v, 低质量代理平均=%v", 
				avgHighQuality, avgLowQuality)
		}
		
		// 重置代理配置
		for _, port := range proxyPorts {
			setProxyDelay(port, 50)
			setProxyFailureRate(port, 0.0)
		}
		
		t.Log("✓ 基于质量的负载均衡测试完成")
	})
}

// TestCooldownMechanism 测试冷却机制
func TestCooldownMechanism(t *testing.T) {
	proxyPort := 18081
	cooldownTime := 5 * time.Second
	
	t.Run("测试代理冷却机制", func(t *testing.T) {
		// 设置代理为不健康状态
		err := setProxyHealth(proxyPort, false)
		assert.NoError(t, err, "设置代理为不健康应该成功")
		
		// 记录故障时间
		failureTime := time.Now()
		
		// 验证代理不可用
		response := sendProxyRequest(proxyPort)
		assert.Nil(t, response, "不健康的代理不应该返回成功响应")
		
		// 恢复代理健康状态
		err = setProxyHealth(proxyPort, true)
		assert.NoError(t, err, "恢复代理健康状态应该成功")
		
		// 模拟冷却期内的请求
		if time.Since(failureTime) < cooldownTime {
			t.Logf("✓ 在冷却期内，代理应该仍然被避免使用")
		}
		
		// 等待冷却期结束
		remainingCooldown := cooldownTime - time.Since(failureTime)
		if remainingCooldown > 0 {
			t.Logf("等待冷却期结束: %v", remainingCooldown)
			time.Sleep(remainingCooldown)
		}
		
		// 冷却期后应该可以正常使用
		response = sendProxyRequest(proxyPort)
		assert.NotNil(t, response, "冷却期后代理应该可以正常使用")
		
		t.Log("✓ 代理冷却机制测试完成")
	})
}

// 辅助函数

func setProxyHealth(port int, healthy bool) error {
	url := fmt.Sprintf("http://127.0.0.1:%d/control/health?healthy=%v", port, healthy)
	resp, err := http.Post(url, "application/json", nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	return nil
}

func checkProxyHealth(port int) bool {
	url := fmt.Sprintf("http://127.0.0.1:%d/health", port)
	resp, err := http.Get(url)
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	return resp.StatusCode == http.StatusOK
}

func setProxyDelay(port int, delayMs int) error {
	url := fmt.Sprintf("http://127.0.0.1:%d/control/delay?delay=%d", port, delayMs)
	resp, err := http.Post(url, "application/json", nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	return nil
}

func setProxyFailureRate(port int, rate float64) error {
	url := fmt.Sprintf("http://127.0.0.1:%d/control/failure?rate=%.2f", port, rate)
	resp, err := http.Post(url, "application/json", nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	return nil
}

func sendProxyRequest(port int) map[string]interface{} {
	url := fmt.Sprintf("http://127.0.0.1:%d/test", port)
	resp, err := http.Get(url)
	if err != nil {
		return nil
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil
	}
	
	return map[string]interface{}{
		"status": resp.StatusCode,
		"port":   port,
	}
}

func getProxyStats(port int) map[string]interface{} {
	url := fmt.Sprintf("http://127.0.0.1:%d/stats", port)
	resp, err := http.Get(url)
	if err != nil {
		return nil
	}
	defer resp.Body.Close()
	
	return map[string]interface{}{
		"port":   port,
		"status": resp.StatusCode,
	}
}

func averageDuration(durations []time.Duration) time.Duration {
	if len(durations) == 0 {
		return 0
	}
	
	var total time.Duration
	for _, d := range durations {
		total += d
	}
	
	return total / time.Duration(len(durations))
}
