package scenarios

import (
	"net"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// BanSystemConfig 封禁系统配置
type BanSystemConfig struct {
	GlobalBannedIPs []BannedIP     `yaml:"global_banned_ips"`
	BannedDomains   []BannedDomain `yaml:"banned_domains"`
	BlockedIPs      []string       `yaml:"blocked_ips"`
	TrustedIPs      []string       `yaml:"trusted_ips"`
}

type BannedIP struct {
	IP       string      `yaml:"ip"`
	Duration interface{} `yaml:"duration"`
}

type BannedDomain struct {
	Domain   string      `yaml:"domain"`
	Duration interface{} `yaml:"duration"`
}

// TestIPBanSystem 测试IP封禁系统
func TestIPBanSystem(t *testing.T) {
	config := BanSystemConfig{
		GlobalBannedIPs: []BannedIP{
			{IP: "***********", Duration: 300},
			{IP: "***********", Duration: "10m"},
			{IP: "***********", Duration: "reboot"},
		},
		BlockedIPs: []string{
			"***********/24",
			"************/24",
			"*********/24",
		},
		TrustedIPs: []string{
			"127.0.0.1",
			"::1",
			"***********/24",
			"10.0.0.0/8",
		},
	}

	t.Run("测试单个IP封禁", func(t *testing.T) {
		bannedIPs := []string{"***********", "***********", "***********"}
		
		for _, ip := range bannedIPs {
			isBanned := isIPBanned(ip, config.GlobalBannedIPs)
			assert.True(t, isBanned, "IP %s 应该被封禁", ip)
			t.Logf("✓ IP %s 已被封禁", ip)
		}
	})

	t.Run("测试网段封禁", func(t *testing.T) {
		testIPs := []string{
			"***********00",  // 在封禁网段内
			"*************",  // 在封禁网段内
			"***********",    // 在封禁网段内
			"***********00",  // 不在封禁网段内
		}
		
		expectedResults := []bool{true, true, true, false}
		
		for i, ip := range testIPs {
			isBlocked := isIPInBlockedRange(ip, config.BlockedIPs)
			assert.Equal(t, expectedResults[i], isBlocked, 
				"IP %s 的封禁状态应为 %v", ip, expectedResults[i])
			
			if expectedResults[i] {
				t.Logf("✓ IP %s 在封禁网段内", ip)
			} else {
				t.Logf("✓ IP %s 不在封禁网段内", ip)
			}
		}
	})

	t.Run("测试信任IP", func(t *testing.T) {
		trustedIPs := []string{
			"127.0.0.1",      // 本地回环
			"::1",            // IPv6本地回环
			"************",   // 在信任网段内
			"**********",     // 在信任网段内
		}
		
		for _, ip := range trustedIPs {
			isTrusted := isIPTrusted(ip, config.TrustedIPs)
			assert.True(t, isTrusted, "IP %s 应该是信任的", ip)
			t.Logf("✓ IP %s 是信任IP", ip)
		}
	})

	t.Run("测试封禁优先级", func(t *testing.T) {
		// 测试当IP既在封禁列表又在信任列表时的处理
		conflictIP := "***********"
		
		// 假设信任IP优先级更高
		isTrusted := isIPTrusted(conflictIP, config.TrustedIPs)
		isBlocked := isIPInBlockedRange(conflictIP, config.BlockedIPs)
		
		if isTrusted && isBlocked {
			t.Logf("✓ IP %s 既在信任列表又可能被封禁，信任优先", conflictIP)
		}
	})
}

// TestDomainBanSystem 测试域名封禁系统
func TestDomainBanSystem(t *testing.T) {
	config := BanSystemConfig{
		BannedDomains: []BannedDomain{
			{Domain: "malicious-site.com", Duration: 600},
			{Domain: "spam-domain.net", Duration: "1h"},
			{Domain: "phishing-site.org", Duration: "reboot"},
		},
	}

	t.Run("测试域名封禁", func(t *testing.T) {
		bannedDomains := []string{
			"malicious-site.com",
			"spam-domain.net", 
			"phishing-site.org",
		}
		
		for _, domain := range bannedDomains {
			isBanned := isDomainBanned(domain, config.BannedDomains)
			assert.True(t, isBanned, "域名 %s 应该被封禁", domain)
			t.Logf("✓ 域名 %s 已被封禁", domain)
		}
	})

	t.Run("测试子域名封禁", func(t *testing.T) {
		// 测试子域名是否也被封禁
		subdomains := []string{
			"www.malicious-site.com",
			"api.spam-domain.net",
			"login.phishing-site.org",
		}
		
		for _, subdomain := range subdomains {
			// 简单的子域名检查
			isBanned := isDomainOrSubdomainBanned(subdomain, config.BannedDomains)
			assert.True(t, isBanned, "子域名 %s 应该被封禁", subdomain)
			t.Logf("✓ 子域名 %s 已被封禁", subdomain)
		}
	})

	t.Run("测试正常域名", func(t *testing.T) {
		normalDomains := []string{
			"google.com",
			"github.com",
			"stackoverflow.com",
		}
		
		for _, domain := range normalDomains {
			isBanned := isDomainBanned(domain, config.BannedDomains)
			assert.False(t, isBanned, "域名 %s 不应该被封禁", domain)
			t.Logf("✓ 域名 %s 未被封禁", domain)
		}
	})
}

// TestExclusionPatterns 测试排除规则和模式匹配
func TestExclusionPatterns(t *testing.T) {
	excludedPatterns := []string{
		"*.local",
		"localhost:*",
		"127.0.0.1:*",
		"*.test.internal",
		"*.integration.test",
	}

	t.Run("测试通配符模式匹配", func(t *testing.T) {
		testCases := []struct {
			url      string
			excluded bool
		}{
			{"http://api.local", true},
			{"https://service.local:8080", true},
			{"http://localhost:3000", true},
			{"https://127.0.0.1:8080", true},
			{"http://db.test.internal", true},
			{"https://api.integration.test", true},
			{"http://google.com", false},
			{"https://github.com:443", false},
		}

		for _, tc := range testCases {
			isExcluded := matchesExclusionPattern(tc.url, excludedPatterns)
			assert.Equal(t, tc.excluded, isExcluded, 
				"URL %s 的排除状态应为 %v", tc.url, tc.excluded)
			
			if tc.excluded {
				t.Logf("✓ URL %s 匹配排除模式", tc.url)
			} else {
				t.Logf("✓ URL %s 不匹配排除模式", tc.url)
			}
		}
	})

	t.Run("测试端口通配符", func(t *testing.T) {
		portPatterns := []string{"localhost:*", "127.0.0.1:*"}
		testURLs := []string{
			"localhost:3000",
			"localhost:8080",
			"127.0.0.1:9000",
			"127.0.0.1:443",
		}

		for _, url := range testURLs {
			isExcluded := matchesExclusionPattern(url, portPatterns)
			assert.True(t, isExcluded, "URL %s 应该匹配端口通配符", url)
			t.Logf("✓ URL %s 匹配端口通配符", url)
		}
	})
}

// TestProxyPoolManagement 测试代理池管理
func TestProxyPoolManagement(t *testing.T) {
	poolConfig := struct {
		MinProxyPoolSize      int `yaml:"min_proxy_pool_size"`
		MaxProxyFetchAttempts int `yaml:"max_proxy_fetch_attempts"`
	}{
		MinProxyPoolSize:      5,
		MaxProxyFetchAttempts: 3,
	}

	t.Run("测试最小代理池大小", func(t *testing.T) {
		currentPoolSize := 3
		minRequired := poolConfig.MinProxyPoolSize
		
		needsMoreProxies := currentPoolSize < minRequired
		assert.True(t, needsMoreProxies, "当前代理池大小 %d 小于最小要求 %d", 
			currentPoolSize, minRequired)
		
		t.Logf("✓ 代理池大小检查: 当前=%d, 最小要求=%d, 需要补充=%v", 
			currentPoolSize, minRequired, needsMoreProxies)
	})

	t.Run("测试代理获取重试", func(t *testing.T) {
		maxAttempts := poolConfig.MaxProxyFetchAttempts
		
		for attempt := 1; attempt <= maxAttempts+1; attempt++ {
			shouldRetry := attempt <= maxAttempts
			
			if shouldRetry {
				t.Logf("✓ 第 %d 次尝试获取代理 (最大 %d 次)", attempt, maxAttempts)
			} else {
				t.Logf("✓ 已达到最大重试次数 %d，停止尝试", maxAttempts)
				break
			}
		}
		
		assert.Equal(t, 3, maxAttempts, "最大重试次数应为3")
	})
}

// TestDNSConfiguration 测试DNS配置
func TestDNSConfiguration(t *testing.T) {
	dnsConfig := struct {
		DNSCacheTTL         int    `yaml:"dns_cache_ttl"`
		DNSNoCache          bool   `yaml:"dns_no_cache"`
		IPVersionPriority   string `yaml:"ip_version_priority"`
		DefaultDNSTimeout   int    `yaml:"default_dns_timeout"`
	}{
		DNSCacheTTL:       60,
		DNSNoCache:        false,
		IPVersionPriority: "ipv4",
		DefaultDNSTimeout: 3000,
	}

	t.Run("测试DNS缓存TTL", func(t *testing.T) {
		ttl := time.Duration(dnsConfig.DNSCacheTTL) * time.Second
		assert.Equal(t, 60*time.Second, ttl, "DNS缓存TTL应为60秒")
		
		// 模拟缓存过期检查
		cacheTime := time.Now()
		isExpired := time.Since(cacheTime) > ttl
		assert.False(t, isExpired, "刚创建的缓存不应该过期")
		
		t.Logf("✓ DNS缓存TTL设置为 %v", ttl)
	})

	t.Run("测试IP版本优先级", func(t *testing.T) {
		priority := dnsConfig.IPVersionPriority
		assert.Equal(t, "ipv4", priority, "IP版本优先级应为IPv4")
		
		// 模拟IP版本选择
		ipv4Available := true
		ipv6Available := true
		
		var selectedVersion string
		if priority == "ipv4" && ipv4Available {
			selectedVersion = "ipv4"
		} else if priority == "ipv6" && ipv6Available {
			selectedVersion = "ipv6"
		} else if ipv4Available {
			selectedVersion = "ipv4"
		} else if ipv6Available {
			selectedVersion = "ipv6"
		}
		
		assert.Equal(t, "ipv4", selectedVersion, "应该选择IPv4")
		t.Logf("✓ 选择IP版本: %s (优先级: %s)", selectedVersion, priority)
	})

	t.Run("测试DNS超时设置", func(t *testing.T) {
		timeout := time.Duration(dnsConfig.DefaultDNSTimeout) * time.Millisecond
		assert.Equal(t, 3*time.Second, timeout, "DNS超时应为3秒")
		
		// 模拟DNS查询超时
		start := time.Now()
		// 这里应该是实际的DNS查询，我们模拟一个快速响应
		elapsed := time.Since(start)
		
		assert.Less(t, elapsed, timeout, "DNS查询应该在超时时间内完成")
		t.Logf("✓ DNS超时设置: %v, 实际耗时: %v", timeout, elapsed)
	})
}

// 辅助函数

func isIPBanned(ip string, bannedIPs []BannedIP) bool {
	for _, banned := range bannedIPs {
		if banned.IP == ip {
			return true
		}
	}
	return false
}

func isIPInBlockedRange(ip string, blockedRanges []string) bool {
	testIP := net.ParseIP(ip)
	if testIP == nil {
		return false
	}

	for _, cidr := range blockedRanges {
		_, network, err := net.ParseCIDR(cidr)
		if err != nil {
			continue
		}
		if network.Contains(testIP) {
			return true
		}
	}
	return false
}

func isIPTrusted(ip string, trustedRanges []string) bool {
	testIP := net.ParseIP(ip)
	if testIP == nil {
		return false
	}

	for _, trusted := range trustedRanges {
		// 处理单个IP
		if trustedIP := net.ParseIP(trusted); trustedIP != nil {
			if trustedIP.Equal(testIP) {
				return true
			}
			continue
		}
		
		// 处理CIDR网段
		_, network, err := net.ParseCIDR(trusted)
		if err != nil {
			continue
		}
		if network.Contains(testIP) {
			return true
		}
	}
	return false
}

func isDomainBanned(domain string, bannedDomains []BannedDomain) bool {
	for _, banned := range bannedDomains {
		if banned.Domain == domain {
			return true
		}
	}
	return false
}

func isDomainOrSubdomainBanned(domain string, bannedDomains []BannedDomain) bool {
	for _, banned := range bannedDomains {
		if domain == banned.Domain || strings.HasSuffix(domain, "."+banned.Domain) {
			return true
		}
	}
	return false
}

func matchesExclusionPattern(url string, patterns []string) bool {
	for _, pattern := range patterns {
		if matchPattern(url, pattern) {
			return true
		}
	}
	return false
}

func matchPattern(text, pattern string) bool {
	// 简单的通配符匹配实现
	if pattern == "*" {
		return true
	}

	// 提取URL中的主机部分进行匹配
	host := extractHost(text)

	// 移除端口号进行域名匹配
	if strings.Contains(pattern, "*") && !strings.Contains(pattern, ":") {
		// 对于域名模式，移除端口号
		if idx := strings.Index(host, ":"); idx != -1 {
			host = host[:idx]
		}
	}

	if strings.Contains(pattern, "*") {
		parts := strings.Split(pattern, "*")
		if len(parts) == 2 {
			prefix := parts[0]
			suffix := parts[1]
			return strings.HasPrefix(host, prefix) && strings.HasSuffix(host, suffix)
		}
	}

	return host == pattern
}

func extractHost(url string) string {
	// 移除协议前缀
	if strings.HasPrefix(url, "http://") {
		url = url[7:]
	} else if strings.HasPrefix(url, "https://") {
		url = url[8:]
	}

	// 移除路径部分
	if idx := strings.Index(url, "/"); idx != -1 {
		url = url[:idx]
	}

	return url
}
