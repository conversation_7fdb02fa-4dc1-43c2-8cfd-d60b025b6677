package performance

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	TotalRequests     int64
	SuccessfulRequests int64
	FailedRequests    int64
	TotalDuration     time.Duration
	MinDuration       time.Duration
	MaxDuration       time.Duration
	AvgDuration       time.Duration
	RequestsPerSecond float64
}

// PerformanceTest 性能测试结构
type PerformanceTest struct {
	FlexProxyURL string
	Client       *http.Client
	Metrics      *PerformanceMetrics
	mutex        sync.RWMutex
}

// NewPerformanceTest 创建性能测试实例
func NewPerformanceTest(flexProxyURL string) *PerformanceTest {
	return &PerformanceTest{
		FlexProxyURL: flexProxyURL,
		Client: &http.Client{
			Timeout: 10 * time.Second,
		},
		Metrics: &PerformanceMetrics{
			MinDuration: time.Hour, // 初始化为很大的值
		},
	}
}

// BenchmarkProxyRotation 基准测试代理轮换性能
func BenchmarkProxyRotation(b *testing.B) {
	pt := NewPerformanceTest("http://127.0.0.1:18080")
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			start := time.Now()
			resp, err := pt.Client.Get(pt.FlexProxyURL + "/test")
			duration := time.Since(start)
			
			if err == nil {
				resp.Body.Close()
				atomic.AddInt64(&pt.Metrics.SuccessfulRequests, 1)
			} else {
				atomic.AddInt64(&pt.Metrics.FailedRequests, 1)
			}
			
			atomic.AddInt64(&pt.Metrics.TotalRequests, 1)
			pt.updateDurationMetrics(duration)
		}
	})
	
	pt.calculateFinalMetrics(b.Elapsed())
	pt.reportMetrics(b)
}

// BenchmarkDNSResolution 基准测试DNS解析性能
func BenchmarkDNSResolution(b *testing.B) {
	pt := NewPerformanceTest("http://127.0.0.1:18080")
	
	domains := []string{
		"test.example.com",
		"proxy1.test.com",
		"proxy2.test.com",
		"fast.test.com",
		"slow.test.com",
	}
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		domainIndex := 0
		for pb.Next() {
			domain := domains[domainIndex%len(domains)]
			start := time.Now()
			
			// 模拟DNS查询请求
			resp, err := pt.Client.Get(pt.FlexProxyURL + "/dns?domain=" + domain)
			duration := time.Since(start)
			
			if err == nil {
				resp.Body.Close()
				atomic.AddInt64(&pt.Metrics.SuccessfulRequests, 1)
			} else {
				atomic.AddInt64(&pt.Metrics.FailedRequests, 1)
			}
			
			atomic.AddInt64(&pt.Metrics.TotalRequests, 1)
			pt.updateDurationMetrics(duration)
			domainIndex++
		}
	})
	
	pt.calculateFinalMetrics(b.Elapsed())
	pt.reportMetrics(b)
}

// BenchmarkConcurrentRequests 基准测试并发请求性能
func BenchmarkConcurrentRequests(b *testing.B) {
	pt := NewPerformanceTest("http://127.0.0.1:18080")
	
	concurrencyLevels := []int{1, 10, 50, 100, 200}
	
	for _, concurrency := range concurrencyLevels {
		b.Run(fmt.Sprintf("Concurrency_%d", concurrency), func(b *testing.B) {
			pt.resetMetrics()
			
			b.SetParallelism(concurrency)
			b.ResetTimer()
			
			b.RunParallel(func(pb *testing.PB) {
				for pb.Next() {
					start := time.Now()
					resp, err := pt.Client.Get(pt.FlexProxyURL + "/test")
					duration := time.Since(start)
					
					if err == nil {
						resp.Body.Close()
						atomic.AddInt64(&pt.Metrics.SuccessfulRequests, 1)
					} else {
						atomic.AddInt64(&pt.Metrics.FailedRequests, 1)
					}
					
					atomic.AddInt64(&pt.Metrics.TotalRequests, 1)
					pt.updateDurationMetrics(duration)
				}
			})
			
			pt.calculateFinalMetrics(b.Elapsed())
			b.Logf("并发级别 %d: RPS=%.2f, 成功率=%.2f%%, 平均延迟=%v", 
				concurrency, 
				pt.Metrics.RequestsPerSecond,
				float64(pt.Metrics.SuccessfulRequests)/float64(pt.Metrics.TotalRequests)*100,
				pt.Metrics.AvgDuration)
		})
	}
}

// TestProxyPoolPerformance 测试代理池性能
func TestProxyPoolPerformance(t *testing.T) {
	pt := NewPerformanceTest("http://127.0.0.1:18080")
	
	// 测试不同代理池大小的性能
	poolSizes := []int{5, 10, 20, 50, 100}
	
	for _, poolSize := range poolSizes {
		t.Run(fmt.Sprintf("PoolSize_%d", poolSize), func(t *testing.T) {
			pt.resetMetrics()
			
			// 模拟设置代理池大小
			pt.setProxyPoolSize(poolSize)
			
			// 执行性能测试
			duration := pt.runLoadTest(1000, 50, 10*time.Second)
			
			// 验证性能指标
			assert.Greater(t, pt.Metrics.RequestsPerSecond, 50.0, 
				"RPS应该大于50 (池大小: %d)", poolSize)
			assert.Greater(t, float64(pt.Metrics.SuccessfulRequests)/float64(pt.Metrics.TotalRequests), 0.95, 
				"成功率应该大于95% (池大小: %d)", poolSize)
			
			t.Logf("代理池大小 %d: RPS=%.2f, 成功率=%.2f%%, 总耗时=%v", 
				poolSize,
				pt.Metrics.RequestsPerSecond,
				float64(pt.Metrics.SuccessfulRequests)/float64(pt.Metrics.TotalRequests)*100,
				duration)
		})
	}
}

// TestDNSCachePerformance 测试DNS缓存性能
func TestDNSCachePerformance(t *testing.T) {
	pt := NewPerformanceTest("http://127.0.0.1:18080")
	
	domain := "test.example.com"
	
	// 测试无缓存性能
	t.Run("无缓存", func(t *testing.T) {
		pt.resetMetrics()
		pt.disableDNSCache()
		
		_ = pt.runDNSLoadTest(domain, 100, 10, 5*time.Second)
		noCacheRPS := pt.Metrics.RequestsPerSecond
		noCacheAvgDuration := pt.Metrics.AvgDuration
		
		t.Logf("无缓存: RPS=%.2f, 平均延迟=%v", noCacheRPS, noCacheAvgDuration)
	})
	
	// 测试有缓存性能
	t.Run("有缓存", func(t *testing.T) {
		pt.resetMetrics()
		pt.enableDNSCache()
		
		// 预热缓存
		pt.Client.Get(pt.FlexProxyURL + "/dns?domain=" + domain)
		
		_ = pt.runDNSLoadTest(domain, 100, 10, 5*time.Second)
		cachedRPS := pt.Metrics.RequestsPerSecond
		cachedAvgDuration := pt.Metrics.AvgDuration
		
		t.Logf("有缓存: RPS=%.2f, 平均延迟=%v", cachedRPS, cachedAvgDuration)
		
		// 验证缓存效果
		// 注意：这里的比较可能需要根据实际情况调整
		// assert.Greater(t, cachedRPS, noCacheRPS*1.5, "缓存应该显著提高RPS")
		// assert.Less(t, cachedAvgDuration, noCacheAvgDuration/2, "缓存应该显著降低延迟")
	})
}

// TestFailoverPerformance 测试故障转移性能
func TestFailoverPerformance(t *testing.T) {
	pt := NewPerformanceTest("http://127.0.0.1:18080")
	
	t.Run("正常情况", func(t *testing.T) {
		pt.resetMetrics()
		
		_ = pt.runLoadTest(500, 25, 10*time.Second)
		normalRPS := pt.Metrics.RequestsPerSecond
		normalSuccessRate := float64(pt.Metrics.SuccessfulRequests) / float64(pt.Metrics.TotalRequests)
		
		t.Logf("正常情况: RPS=%.2f, 成功率=%.2f%%", normalRPS, normalSuccessRate*100)
	})
	
	t.Run("故障转移", func(t *testing.T) {
		pt.resetMetrics()
		
		// 模拟代理故障
		pt.simulateProxyFailure(2) // 模拟2个代理故障
		
		_ = pt.runLoadTest(500, 25, 10*time.Second)
		failoverRPS := pt.Metrics.RequestsPerSecond
		failoverSuccessRate := float64(pt.Metrics.SuccessfulRequests) / float64(pt.Metrics.TotalRequests)
		
		t.Logf("故障转移: RPS=%.2f, 成功率=%.2f%%", failoverRPS, failoverSuccessRate*100)
		
		// 验证故障转移效果
		assert.Greater(t, failoverSuccessRate, 0.8, "故障转移后成功率应该大于80%")
	})
}

// TestMemoryUsage 测试内存使用情况
func TestMemoryUsage(t *testing.T) {
	pt := NewPerformanceTest("http://127.0.0.1:18080")
	
	// 获取初始内存使用情况
	initialMemory := pt.getMemoryUsage()
	
	// 执行大量请求
	pt.runLoadTest(5000, 100, 30*time.Second)
	
	// 获取最终内存使用情况
	finalMemory := pt.getMemoryUsage()
	
	memoryIncrease := finalMemory - initialMemory
	
	t.Logf("内存使用: 初始=%dMB, 最终=%dMB, 增长=%dMB", 
		initialMemory/1024/1024, finalMemory/1024/1024, memoryIncrease/1024/1024)
	
	// 验证内存使用合理
	assert.Less(t, memoryIncrease, int64(100*1024*1024), "内存增长应该小于100MB")
}

// 辅助方法

// updateDurationMetrics 更新延迟指标
func (pt *PerformanceTest) updateDurationMetrics(duration time.Duration) {
	pt.mutex.Lock()
	defer pt.mutex.Unlock()
	
	if duration < pt.Metrics.MinDuration {
		pt.Metrics.MinDuration = duration
	}
	if duration > pt.Metrics.MaxDuration {
		pt.Metrics.MaxDuration = duration
	}
}

// calculateFinalMetrics 计算最终指标
func (pt *PerformanceTest) calculateFinalMetrics(totalDuration time.Duration) {
	pt.mutex.Lock()
	defer pt.mutex.Unlock()
	
	pt.Metrics.TotalDuration = totalDuration
	if pt.Metrics.TotalRequests > 0 {
		pt.Metrics.AvgDuration = totalDuration / time.Duration(pt.Metrics.TotalRequests)
		pt.Metrics.RequestsPerSecond = float64(pt.Metrics.TotalRequests) / totalDuration.Seconds()
	}
}

// reportMetrics 报告性能指标
func (pt *PerformanceTest) reportMetrics(b *testing.B) {
	b.Logf("性能指标:")
	b.Logf("  总请求数: %d", pt.Metrics.TotalRequests)
	b.Logf("  成功请求: %d", pt.Metrics.SuccessfulRequests)
	b.Logf("  失败请求: %d", pt.Metrics.FailedRequests)
	b.Logf("  成功率: %.2f%%", float64(pt.Metrics.SuccessfulRequests)/float64(pt.Metrics.TotalRequests)*100)
	b.Logf("  RPS: %.2f", pt.Metrics.RequestsPerSecond)
	b.Logf("  平均延迟: %v", pt.Metrics.AvgDuration)
	b.Logf("  最小延迟: %v", pt.Metrics.MinDuration)
	b.Logf("  最大延迟: %v", pt.Metrics.MaxDuration)
}

// resetMetrics 重置指标
func (pt *PerformanceTest) resetMetrics() {
	pt.mutex.Lock()
	defer pt.mutex.Unlock()
	
	pt.Metrics = &PerformanceMetrics{
		MinDuration: time.Hour,
	}
}

// runLoadTest 运行负载测试
func (pt *PerformanceTest) runLoadTest(totalRequests, concurrency int, timeout time.Duration) time.Duration {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	
	start := time.Now()
	
	var wg sync.WaitGroup
	requestChan := make(chan struct{}, totalRequests)
	
	// 填充请求通道
	for i := 0; i < totalRequests; i++ {
		requestChan <- struct{}{}
	}
	close(requestChan)
	
	// 启动工作者
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			
			for {
				select {
				case <-ctx.Done():
					return
				case _, ok := <-requestChan:
					if !ok {
						return
					}
					
					reqStart := time.Now()
					resp, err := pt.Client.Get(pt.FlexProxyURL + "/test")
					duration := time.Since(reqStart)
					
					if err == nil {
						resp.Body.Close()
						atomic.AddInt64(&pt.Metrics.SuccessfulRequests, 1)
					} else {
						atomic.AddInt64(&pt.Metrics.FailedRequests, 1)
					}
					
					atomic.AddInt64(&pt.Metrics.TotalRequests, 1)
					pt.updateDurationMetrics(duration)
				}
			}
		}()
	}
	
	wg.Wait()
	totalDuration := time.Since(start)
	pt.calculateFinalMetrics(totalDuration)
	
	return totalDuration
}

// runDNSLoadTest 运行DNS负载测试
func (pt *PerformanceTest) runDNSLoadTest(domain string, totalRequests, concurrency int, timeout time.Duration) time.Duration {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	
	start := time.Now()
	
	var wg sync.WaitGroup
	requestChan := make(chan struct{}, totalRequests)
	
	// 填充请求通道
	for i := 0; i < totalRequests; i++ {
		requestChan <- struct{}{}
	}
	close(requestChan)
	
	// 启动工作者
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			
			for {
				select {
				case <-ctx.Done():
					return
				case _, ok := <-requestChan:
					if !ok {
						return
					}
					
					reqStart := time.Now()
					resp, err := pt.Client.Get(pt.FlexProxyURL + "/dns?domain=" + domain)
					duration := time.Since(reqStart)
					
					if err == nil {
						resp.Body.Close()
						atomic.AddInt64(&pt.Metrics.SuccessfulRequests, 1)
					} else {
						atomic.AddInt64(&pt.Metrics.FailedRequests, 1)
					}
					
					atomic.AddInt64(&pt.Metrics.TotalRequests, 1)
					pt.updateDurationMetrics(duration)
				}
			}
		}()
	}
	
	wg.Wait()
	totalDuration := time.Since(start)
	pt.calculateFinalMetrics(totalDuration)
	
	return totalDuration
}

// 模拟方法（在实际环境中应该调用真实的API）

// setProxyPoolSize 设置代理池大小
func (pt *PerformanceTest) setProxyPoolSize(size int) {
	// 模拟设置代理池大小
}

// disableDNSCache 禁用DNS缓存
func (pt *PerformanceTest) disableDNSCache() {
	// 模拟禁用DNS缓存
}

// enableDNSCache 启用DNS缓存
func (pt *PerformanceTest) enableDNSCache() {
	// 模拟启用DNS缓存
}

// simulateProxyFailure 模拟代理故障
func (pt *PerformanceTest) simulateProxyFailure(count int) {
	// 模拟代理故障
}

// getMemoryUsage 获取内存使用情况
func (pt *PerformanceTest) getMemoryUsage() int64 {
	// 模拟获取内存使用情况
	return 50 * 1024 * 1024 // 50MB
}
