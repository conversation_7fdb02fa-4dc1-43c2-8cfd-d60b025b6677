package main

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"net"
	"net/http"
	"os"
	"strconv"
	"sync"
	"time"
)

// ProxyServer 模拟代理服务器
type ProxyServer struct {
	Port           int
	Name           string
	HealthStatus   bool
	ResponseDelay  time.Duration
	FailureRate    float64
	RequestCount   int64
	SuccessCount   int64
	FailureCount   int64
	mutex          sync.RWMutex
	server         *http.Server
}

// NewProxyServer 创建新的模拟代理服务器
func NewProxyServer(port int, name string) *ProxyServer {
	return &ProxyServer{
		Port:         port,
		Name:         name,
		HealthStatus: true,
		ResponseDelay: 100 * time.Millisecond,
		FailureRate:  0.0,
	}
}

// SetHealthStatus 设置健康状态
func (ps *ProxyServer) SetHealthStatus(healthy bool) {
	ps.mutex.Lock()
	defer ps.mutex.Unlock()
	ps.HealthStatus = healthy
}

// SetResponseDelay 设置响应延迟
func (ps *ProxyServer) SetResponseDelay(delay time.Duration) {
	ps.mutex.Lock()
	defer ps.mutex.Unlock()
	ps.ResponseDelay = delay
}

// SetFailureRate 设置失败率 (0.0-1.0)
func (ps *ProxyServer) SetFailureRate(rate float64) {
	ps.mutex.Lock()
	defer ps.mutex.Unlock()
	ps.FailureRate = rate
}

// GetStats 获取统计信息
func (ps *ProxyServer) GetStats() (int64, int64, int64) {
	ps.mutex.RLock()
	defer ps.mutex.RUnlock()
	return ps.RequestCount, ps.SuccessCount, ps.FailureCount
}

// Start 启动代理服务器
func (ps *ProxyServer) Start() error {
	mux := http.NewServeMux()
	
	// 健康检查端点
	mux.HandleFunc("/health", ps.healthHandler)
	
	// 统计信息端点
	mux.HandleFunc("/stats", ps.statsHandler)
	
	// 控制端点
	mux.HandleFunc("/control/health", ps.controlHealthHandler)
	mux.HandleFunc("/control/delay", ps.controlDelayHandler)
	mux.HandleFunc("/control/failure", ps.controlFailureHandler)
	
	// 代理处理器
	mux.HandleFunc("/", ps.proxyHandler)
	
	ps.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", ps.Port),
		Handler: mux,
	}
	
	log.Printf("启动模拟代理服务器 %s 在端口 %d", ps.Name, ps.Port)
	return ps.server.ListenAndServe()
}

// Stop 停止代理服务器
func (ps *ProxyServer) Stop() error {
	if ps.server != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		return ps.server.Shutdown(ctx)
	}
	return nil
}

// healthHandler 健康检查处理器
func (ps *ProxyServer) healthHandler(w http.ResponseWriter, r *http.Request) {
	ps.mutex.RLock()
	healthy := ps.HealthStatus
	ps.mutex.RUnlock()
	
	if healthy {
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{"status":"healthy","server":"%s","port":%d}`, ps.Name, ps.Port)
	} else {
		w.WriteHeader(http.StatusServiceUnavailable)
		fmt.Fprintf(w, `{"status":"unhealthy","server":"%s","port":%d}`, ps.Name, ps.Port)
	}
}

// statsHandler 统计信息处理器
func (ps *ProxyServer) statsHandler(w http.ResponseWriter, r *http.Request) {
	requests, success, failure := ps.GetStats()
	
	w.Header().Set("Content-Type", "application/json")
	fmt.Fprintf(w, `{
		"server": "%s",
		"port": %d,
		"requests": %d,
		"success": %d,
		"failure": %d,
		"success_rate": %.2f
	}`, ps.Name, ps.Port, requests, success, failure, 
		func() float64 {
			if requests > 0 {
				return float64(success) / float64(requests) * 100
			}
			return 0.0
		}())
}

// controlHealthHandler 控制健康状态
func (ps *ProxyServer) controlHealthHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		w.WriteHeader(http.StatusMethodNotAllowed)
		return
	}
	
	healthy := r.URL.Query().Get("healthy") == "true"
	ps.SetHealthStatus(healthy)
	
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"message":"健康状态已设置为 %v"}`, healthy)
}

// controlDelayHandler 控制响应延迟
func (ps *ProxyServer) controlDelayHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		w.WriteHeader(http.StatusMethodNotAllowed)
		return
	}
	
	delayStr := r.URL.Query().Get("delay")
	if delayStr == "" {
		w.WriteHeader(http.StatusBadRequest)
		fmt.Fprint(w, `{"error":"缺少delay参数"}`)
		return
	}
	
	delayMs, err := strconv.Atoi(delayStr)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		fmt.Fprint(w, `{"error":"无效的delay值"}`)
		return
	}
	
	ps.SetResponseDelay(time.Duration(delayMs) * time.Millisecond)
	
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"message":"响应延迟已设置为 %dms"}`, delayMs)
}

// controlFailureHandler 控制失败率
func (ps *ProxyServer) controlFailureHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		w.WriteHeader(http.StatusMethodNotAllowed)
		return
	}
	
	rateStr := r.URL.Query().Get("rate")
	if rateStr == "" {
		w.WriteHeader(http.StatusBadRequest)
		fmt.Fprint(w, `{"error":"缺少rate参数"}`)
		return
	}
	
	rate, err := strconv.ParseFloat(rateStr, 64)
	if err != nil || rate < 0 || rate > 1 {
		w.WriteHeader(http.StatusBadRequest)
		fmt.Fprint(w, `{"error":"无效的rate值，应在0-1之间"}`)
		return
	}
	
	ps.SetFailureRate(rate)
	
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"message":"失败率已设置为 %.2f"}`, rate)
}

// proxyHandler 代理请求处理器
func (ps *ProxyServer) proxyHandler(w http.ResponseWriter, r *http.Request) {
	ps.mutex.Lock()
	ps.RequestCount++
	healthy := ps.HealthStatus
	delay := ps.ResponseDelay
	failureRate := ps.FailureRate
	ps.mutex.Unlock()
	
	// 模拟响应延迟
	if delay > 0 {
		time.Sleep(delay)
	}
	
	// 检查健康状态
	if !healthy {
		ps.mutex.Lock()
		ps.FailureCount++
		ps.mutex.Unlock()
		
		w.WriteHeader(http.StatusBadGateway)
		fmt.Fprintf(w, "代理服务器 %s 不健康", ps.Name)
		return
	}
	
	// 模拟随机失败
	if failureRate > 0 && rand.Float64() < failureRate {
		ps.mutex.Lock()
		ps.FailureCount++
		ps.mutex.Unlock()
		
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprintf(w, "代理服务器 %s 模拟失败", ps.Name)
		return
	}
	
	// 成功处理请求
	ps.mutex.Lock()
	ps.SuccessCount++
	ps.mutex.Unlock()
	
	// 添加代理标识头
	w.Header().Set("X-Proxy-Server", ps.Name)
	w.Header().Set("X-Proxy-Port", strconv.Itoa(ps.Port))
	
	// 如果是CONNECT方法，处理HTTPS代理
	if r.Method == http.MethodConnect {
		ps.handleConnect(w, r)
		return
	}
	
	// 处理HTTP代理
	ps.handleHTTP(w, r)
}

// handleConnect 处理HTTPS CONNECT请求
func (ps *ProxyServer) handleConnect(w http.ResponseWriter, r *http.Request) {
	// 模拟CONNECT响应
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, "HTTP/1.1 200 Connection established\r\n\r\n")
}

// handleHTTP 处理HTTP代理请求
func (ps *ProxyServer) handleHTTP(w http.ResponseWriter, r *http.Request) {
	// 模拟HTTP代理响应
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	
	response := map[string]interface{}{
		"message":     "请求已通过代理服务器处理",
		"proxy":       ps.Name,
		"port":        ps.Port,
		"method":      r.Method,
		"url":         r.URL.String(),
		"headers":     r.Header,
		"timestamp":   time.Now().Unix(),
		"client_ip":   getClientIP(r),
	}
	
	// 简单的JSON编码
	fmt.Fprintf(w, `{
		"message": "%s",
		"proxy": "%s",
		"port": %d,
		"method": "%s",
		"url": "%s",
		"timestamp": %d,
		"client_ip": "%s"
	}`, response["message"], response["proxy"], response["port"], 
		response["method"], response["url"], response["timestamp"], response["client_ip"])
}

// getClientIP 获取客户端IP
func getClientIP(r *http.Request) string {
	// 检查X-Forwarded-For头
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		return xff
	}
	
	// 检查X-Real-IP头
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}
	
	// 使用RemoteAddr
	host, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		return r.RemoteAddr
	}
	
	return host
}

// main 主函数
func main() {
	if len(os.Args) < 3 {
		log.Fatal("用法: go run proxy_server.go <端口> <服务器名称>")
	}
	
	port, err := strconv.Atoi(os.Args[1])
	if err != nil {
		log.Fatal("无效的端口号:", os.Args[1])
	}
	
	name := os.Args[2]
	
	server := NewProxyServer(port, name)
	
	// 设置随机种子
	rand.Seed(time.Now().UnixNano())
	
	log.Fatal(server.Start())
}
