package main

import (
	"fmt"
	"log"
	"net"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"
)

// DNSRecord DNS记录
type DNSRecord struct {
	Name string
	Type string
	IP   string
	TTL  uint32
}

// DNSServer 模拟DNS服务器
type DNSServer struct {
	Port     int
	Records  map[string][]DNSRecord
	Cache    map[string]CacheEntry
	mutex    sync.RWMutex
	conn     *net.UDPConn
	running  bool
}

// CacheEntry 缓存条目
type CacheEntry struct {
	Record    DNSRecord
	ExpiresAt time.Time
}

// NewDNSServer 创建新的DNS服务器
func NewDNSServer(port int) *DNSServer {
	return &DNSServer{
		Port:    port,
		Records: make(map[string][]DNSRecord),
		Cache:   make(map[string]CacheEntry),
	}
}

// AddRecord 添加DNS记录
func (ds *DNSServer) AddRecord(name, recordType, ip string, ttl uint32) {
	ds.mutex.Lock()
	defer ds.mutex.Unlock()
	
	key := strings.ToLower(name)
	record := DNSRecord{
		Name: name,
		Type: recordType,
		IP:   ip,
		TTL:  ttl,
	}
	
	ds.Records[key] = append(ds.Records[key], record)
	log.Printf("添加DNS记录: %s %s %s (TTL: %d)", name, recordType, ip, ttl)
}

// LoadDefaultRecords 加载默认DNS记录
func (ds *DNSServer) LoadDefaultRecords() {
	// 添加一些测试记录
	ds.AddRecord("test.example.com", "A", "*************", 300)
	ds.AddRecord("proxy1.test.com", "A", "127.0.0.1", 60)
	ds.AddRecord("proxy2.test.com", "A", "127.0.0.1", 60)
	ds.AddRecord("proxy3.test.com", "A", "127.0.0.1", 60)
	ds.AddRecord("slow.test.com", "A", "*************", 3600)
	ds.AddRecord("fast.test.com", "A", "*************", 30)
	
	// IPv6记录
	ds.AddRecord("ipv6.test.com", "AAAA", "2001:db8::1", 300)
	
	// 反向DNS记录
	ds.AddRecord("*******", "PTR", "one.one.one.one", 300)
	ds.AddRecord("*******", "PTR", "dns.google", 300)
	ds.AddRecord("127.0.0.1", "PTR", "localhost", 300)
}

// Start 启动DNS服务器
func (ds *DNSServer) Start() error {
	addr, err := net.ResolveUDPAddr("udp", fmt.Sprintf(":%d", ds.Port))
	if err != nil {
		return fmt.Errorf("解析UDP地址失败: %v", err)
	}
	
	ds.conn, err = net.ListenUDP("udp", addr)
	if err != nil {
		return fmt.Errorf("监听UDP端口失败: %v", err)
	}
	
	ds.running = true
	log.Printf("DNS服务器启动在端口 %d", ds.Port)
	
	// 启动缓存清理协程
	go ds.cleanupCache()
	
	// 处理DNS请求
	buffer := make([]byte, 512)
	for ds.running {
		n, clientAddr, err := ds.conn.ReadFromUDP(buffer)
		if err != nil {
			if ds.running {
				log.Printf("读取UDP数据失败: %v", err)
			}
			continue
		}
		
		go ds.handleDNSQuery(buffer[:n], clientAddr)
	}
	
	return nil
}

// Stop 停止DNS服务器
func (ds *DNSServer) Stop() error {
	ds.running = false
	if ds.conn != nil {
		return ds.conn.Close()
	}
	return nil
}

// handleDNSQuery 处理DNS查询
func (ds *DNSServer) handleDNSQuery(data []byte, clientAddr *net.UDPAddr) {
	// 简化的DNS查询解析
	if len(data) < 12 {
		return
	}
	
	// 提取查询名称（简化版本）
	queryName := ds.extractQueryName(data)
	if queryName == "" {
		return
	}
	
	log.Printf("收到DNS查询: %s 来自 %s", queryName, clientAddr)
	
	// 查找记录
	record := ds.lookupRecord(queryName)
	
	// 构造响应
	response := ds.buildDNSResponse(data, record)
	
	// 发送响应
	_, err := ds.conn.WriteToUDP(response, clientAddr)
	if err != nil {
		log.Printf("发送DNS响应失败: %v", err)
	}
}

// extractQueryName 提取查询名称（简化版本）
func (ds *DNSServer) extractQueryName(data []byte) string {
	if len(data) < 13 {
		return ""
	}
	
	// 跳过DNS头部（12字节）
	offset := 12
	var name strings.Builder
	
	for offset < len(data) {
		length := int(data[offset])
		if length == 0 {
			break
		}
		
		offset++
		if offset+length > len(data) {
			break
		}
		
		if name.Len() > 0 {
			name.WriteByte('.')
		}
		
		name.Write(data[offset : offset+length])
		offset += length
	}
	
	return name.String()
}

// lookupRecord 查找DNS记录
func (ds *DNSServer) lookupRecord(name string) *DNSRecord {
	key := strings.ToLower(name)
	
	// 首先检查缓存
	ds.mutex.RLock()
	if entry, exists := ds.Cache[key]; exists && time.Now().Before(entry.ExpiresAt) {
		ds.mutex.RUnlock()
		log.Printf("从缓存返回记录: %s -> %s", name, entry.Record.IP)
		return &entry.Record
	}
	ds.mutex.RUnlock()
	
	// 查找记录
	ds.mutex.RLock()
	records, exists := ds.Records[key]
	ds.mutex.RUnlock()
	
	if !exists || len(records) == 0 {
		log.Printf("未找到记录: %s", name)
		return nil
	}
	
	record := records[0] // 返回第一个记录
	
	// 添加到缓存
	ds.mutex.Lock()
	ds.Cache[key] = CacheEntry{
		Record:    record,
		ExpiresAt: time.Now().Add(time.Duration(record.TTL) * time.Second),
	}
	ds.mutex.Unlock()
	
	log.Printf("返回记录: %s -> %s (TTL: %d)", name, record.IP, record.TTL)
	return &record
}

// buildDNSResponse 构造DNS响应（简化版本）
func (ds *DNSServer) buildDNSResponse(query []byte, record *DNSRecord) []byte {
	if len(query) < 12 {
		return nil
	}
	
	// 复制查询作为响应基础
	response := make([]byte, len(query))
	copy(response, query)
	
	// 设置响应标志
	response[2] |= 0x80 // QR位设置为1（响应）
	
	if record == nil {
		// 设置RCODE为NXDOMAIN (3)
		response[3] = (response[3] & 0xF0) | 0x03
		return response
	}
	
	// 设置RCODE为NOERROR (0)
	response[3] = response[3] & 0xF0
	
	// 设置应答计数为1
	response[6] = 0
	response[7] = 1
	
	// 添加应答记录（简化版本）
	// 这里应该添加完整的DNS应答记录，但为了简化，我们只返回基本响应
	
	return response
}

// cleanupCache 清理过期缓存
func (ds *DNSServer) cleanupCache() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for ds.running {
		select {
		case <-ticker.C:
			ds.mutex.Lock()
			now := time.Now()
			for key, entry := range ds.Cache {
				if now.After(entry.ExpiresAt) {
					delete(ds.Cache, key)
					log.Printf("清理过期缓存: %s", key)
				}
			}
			ds.mutex.Unlock()
		}
	}
}

// GetStats 获取统计信息
func (ds *DNSServer) GetStats() map[string]interface{} {
	ds.mutex.RLock()
	defer ds.mutex.RUnlock()
	
	return map[string]interface{}{
		"port":         ds.Port,
		"records":      len(ds.Records),
		"cache_size":   len(ds.Cache),
		"running":      ds.running,
	}
}

// PrintStats 打印统计信息
func (ds *DNSServer) PrintStats() {
	stats := ds.GetStats()
	log.Printf("DNS服务器统计: 端口=%d, 记录数=%d, 缓存大小=%d, 运行中=%v",
		stats["port"], stats["records"], stats["cache_size"], stats["running"])
}

// main 主函数
func main() {
	port := 5353 // 默认端口
	
	if len(os.Args) > 1 {
		if p, err := strconv.Atoi(os.Args[1]); err == nil {
			port = p
		}
	}
	
	server := NewDNSServer(port)
	server.LoadDefaultRecords()
	
	// 启动统计信息打印
	go func() {
		ticker := time.NewTicker(60 * time.Second)
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				server.PrintStats()
			}
		}
	}()
	
	log.Fatal(server.Start())
}
