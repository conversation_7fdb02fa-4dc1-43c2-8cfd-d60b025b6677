package main

import (
    "fmt"
    "log"
    "net/http"
    "os"
    "strconv"
)

func main() {
    port := 18080
    if len(os.Args) > 1 {
        if p, err := strconv.Atoi(os.Args[1]); err == nil {
            port = p
        }
    }
    
    http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
        w.<PERSON>().Set("Content-Type", "application/json")
        fmt.Fprint(w, `{"status":"healthy","service":"flexproxy-mock"}`)
    })
    
    http.HandleFunc("/stats", func(w http.ResponseWriter, r *http.Request) {
        w.Header().Set("Content-Type", "application/json")
        fmt.Fprint(w, `{"proxy_count":5,"active_proxies":4,"requests_processed":100}`)
    })
    
    http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
        w.<PERSON><PERSON>().Set("Content-Type", "application/json")
        fmt.Fprint(w, `{"message":"Request processed by FlexProxy mock"}`)
    })
    
    log.Printf("FlexProxy模拟服务器启动在端口 %d", port)
    log.Fatal(http.ListenAndServe(fmt.Sprintf(":%d", port), nil))
}
