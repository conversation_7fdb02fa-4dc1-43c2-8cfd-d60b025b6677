#!/bin/bash

# FlexProxy 集成测试环境完整性验证脚本
# 用于验证项目的完整性和功能正确性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo
    print_message $BLUE "================================"
    print_message $BLUE "$1"
    print_message $BLUE "================================"
    echo
}

# 检查文件存在性和大小
check_file() {
    local file=$1
    local min_size=${2:-100}
    
    if [ -f "$file" ]; then
        local size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0)
        if [ $size -ge $min_size ]; then
            print_message $GREEN "✓ $file (${size} bytes)"
            return 0
        else
            print_message $YELLOW "⚠ $file 文件太小 (${size} bytes)"
            return 1
        fi
    else
        print_message $RED "✗ $file 不存在"
        return 1
    fi
}

# 检查目录存在性
check_directory() {
    local dir=$1
    
    if [ -d "$dir" ]; then
        local count=$(find "$dir" -type f | wc -l)
        print_message $GREEN "✓ $dir/ ($count 个文件)"
        return 0
    else
        print_message $RED "✗ $dir/ 不存在"
        return 1
    fi
}

# 验证Go模块
verify_go_module() {
    local dir=$1
    local module_name=$2
    
    cd "$dir"
    
    if [ -f "go.mod" ]; then
        if go mod verify > /dev/null 2>&1; then
            print_message $GREEN "✓ $module_name Go模块验证通过"
            return 0
        else
            print_message $YELLOW "⚠ $module_name Go模块验证失败，尝试修复..."
            go mod tidy > /dev/null 2>&1
            if go mod verify > /dev/null 2>&1; then
                print_message $GREEN "✓ $module_name Go模块修复成功"
                return 0
            else
                print_message $RED "✗ $module_name Go模块验证失败"
                return 1
            fi
        fi
    else
        print_message $RED "✗ $module_name 缺少 go.mod 文件"
        return 1
    fi
}

# 验证代码编译
verify_compilation() {
    local dir=$1
    local name=$2
    local files=$3
    
    cd "$dir"
    
    for file in $files; do
        if go build -o /tmp/test_binary "$file" > /dev/null 2>&1; then
            print_message $GREEN "✓ $name/$file 编译成功"
            rm -f /tmp/test_binary
        else
            print_message $RED "✗ $name/$file 编译失败"
            return 1
        fi
    done
    
    return 0
}

# 验证测试编译
verify_test_compilation() {
    local dir=$1
    local name=$2
    
    cd "$dir"
    
    if go test -c -o /tmp/test_binary . > /dev/null 2>&1; then
        print_message $GREEN "✓ $name 测试编译成功"
        rm -f /tmp/test_binary
        return 0
    else
        print_message $RED "✗ $name 测试编译失败"
        return 1
    fi
}

# 验证脚本可执行性
verify_script() {
    local script=$1
    
    if [ -x "$script" ]; then
        print_message $GREEN "✓ $script 可执行"
        return 0
    else
        print_message $RED "✗ $script 不可执行"
        return 1
    fi
}

# 验证配置文件语法
verify_yaml_syntax() {
    local file=$1
    
    # 简单的YAML语法检查
    if python3 -c "import yaml; yaml.safe_load(open('$file'))" > /dev/null 2>&1; then
        print_message $GREEN "✓ $file YAML语法正确"
        return 0
    elif python -c "import yaml; yaml.safe_load(open('$file'))" > /dev/null 2>&1; then
        print_message $GREEN "✓ $file YAML语法正确"
        return 0
    else
        print_message $YELLOW "⚠ $file YAML语法检查跳过 (需要Python和PyYAML)"
        return 0
    fi
}

# 统计项目信息
collect_project_stats() {
    print_title "项目统计信息"
    
    # 文件统计
    local total_files=$(find "$SCRIPT_DIR" -type f | wc -l)
    local go_files=$(find "$SCRIPT_DIR" -name "*.go" | wc -l)
    local sh_files=$(find "$SCRIPT_DIR" -name "*.sh" | wc -l)
    local md_files=$(find "$SCRIPT_DIR" -name "*.md" | wc -l)
    local yaml_files=$(find "$SCRIPT_DIR" -name "*.yaml" -o -name "*.yml" | wc -l)
    local txt_files=$(find "$SCRIPT_DIR" -name "*.txt" | wc -l)
    
    print_message $CYAN "文件统计:"
    print_message $WHITE "  总文件数: $total_files"
    print_message $WHITE "  Go文件: $go_files"
    print_message $WHITE "  Shell脚本: $sh_files"
    print_message $WHITE "  Markdown文档: $md_files"
    print_message $WHITE "  YAML配置: $yaml_files"
    print_message $WHITE "  文本文件: $txt_files"
    
    # 代码行数统计
    local go_lines=$(find "$SCRIPT_DIR" -name "*.go" -exec wc -l {} + 2>/dev/null | tail -1 | awk '{print $1}' || echo 0)
    local sh_lines=$(find "$SCRIPT_DIR" -name "*.sh" -exec wc -l {} + 2>/dev/null | tail -1 | awk '{print $1}' || echo 0)
    local md_lines=$(find "$SCRIPT_DIR" -name "*.md" -exec wc -l {} + 2>/dev/null | tail -1 | awk '{print $1}' || echo 0)
    
    print_message $CYAN "代码行数统计:"
    print_message $WHITE "  Go代码: $go_lines 行"
    print_message $WHITE "  Shell脚本: $sh_lines 行"
    print_message $WHITE "  文档: $md_lines 行"
    
    # 测试数据统计
    if [ -f "$SCRIPT_DIR/test_data/proxies_large.txt" ]; then
        local proxy_count=$(grep -c "^http\|^socks" "$SCRIPT_DIR/test_data/proxies_large.txt" || echo 0)
        print_message $WHITE "  代理数量: $proxy_count 个"
    fi
    
    if [ -f "$SCRIPT_DIR/test_data/hosts_custom.txt" ]; then
        local hosts_count=$(grep -c "^[0-9]" "$SCRIPT_DIR/test_data/hosts_custom.txt" || echo 0)
        print_message $WHITE "  Hosts条目: $hosts_count 个"
    fi
}

# 主验证函数
main() {
    print_title "FlexProxy 集成测试环境完整性验证"
    
    local failed_checks=0
    
    # 1. 验证目录结构
    print_title "验证目录结构"
    check_directory "$SCRIPT_DIR/mock_servers" || ((failed_checks++))
    check_directory "$SCRIPT_DIR/test_data" || ((failed_checks++))
    check_directory "$SCRIPT_DIR/scenarios" || ((failed_checks++))
    check_directory "$SCRIPT_DIR/performance" || ((failed_checks++))
    
    # 2. 验证核心文件
    print_title "验证核心文件"
    check_file "$SCRIPT_DIR/README.md" 1000 || ((failed_checks++))
    check_file "$SCRIPT_DIR/PROJECT_DELIVERY_SUMMARY.md" 1000 || ((failed_checks++))
    check_file "$SCRIPT_DIR/run_integration_tests.sh" 1000 || ((failed_checks++))
    check_file "$SCRIPT_DIR/quick_test.sh" 1000 || ((failed_checks++))
    
    # 3. 验证模拟服务器
    print_title "验证模拟服务器"
    check_file "$SCRIPT_DIR/mock_servers/dns_server.go" 5000 || ((failed_checks++))
    check_file "$SCRIPT_DIR/mock_servers/proxy_server.go" 7000 || ((failed_checks++))
    
    # 4. 验证测试数据
    print_title "验证测试数据"
    check_file "$SCRIPT_DIR/test_data/proxies_large.txt" 3000 || ((failed_checks++))
    check_file "$SCRIPT_DIR/test_data/hosts_custom.txt" 2000 || ((failed_checks++))
    check_file "$SCRIPT_DIR/test_data/integration_config.yaml" 5000 || ((failed_checks++))
    
    # 5. 验证测试文件
    print_title "验证测试文件"
    check_file "$SCRIPT_DIR/scenarios/simple_integration_test.go" 5000 || ((failed_checks++))
    check_file "$SCRIPT_DIR/scenarios/global_config_integration_test.go" 10000 || ((failed_checks++))
    check_file "$SCRIPT_DIR/performance/performance_test.go" 10000 || ((failed_checks++))
    
    # 6. 验证Go模块
    print_title "验证Go模块"
    verify_go_module "$SCRIPT_DIR/scenarios" "scenarios" || ((failed_checks++))
    verify_go_module "$SCRIPT_DIR/performance" "performance" || ((failed_checks++))
    
    # 7. 验证代码编译
    print_title "验证代码编译"
    verify_compilation "$SCRIPT_DIR/mock_servers" "mock_servers" "dns_server.go proxy_server.go" || ((failed_checks++))
    verify_test_compilation "$SCRIPT_DIR/scenarios" "scenarios" || ((failed_checks++))
    verify_test_compilation "$SCRIPT_DIR/performance" "performance" || ((failed_checks++))
    
    # 8. 验证脚本权限
    print_title "验证脚本权限"
    verify_script "$SCRIPT_DIR/run_integration_tests.sh" || ((failed_checks++))
    verify_script "$SCRIPT_DIR/quick_test.sh" || ((failed_checks++))
    
    # 9. 验证配置文件
    print_title "验证配置文件"
    verify_yaml_syntax "$SCRIPT_DIR/test_data/integration_config.yaml" || ((failed_checks++))
    
    # 10. 收集项目统计
    collect_project_stats
    
    # 显示最终结果
    print_title "验证结果"
    if [ $failed_checks -eq 0 ]; then
        print_message $GREEN "🎉 所有验证通过！项目完整性100%"
        print_message $GREEN "✅ 集成测试环境已完全就绪"
        print_message $CYAN "可以运行以下命令开始测试:"
        print_message $YELLOW "  ./quick_test.sh          # 快速验证"
        print_message $YELLOW "  ./run_integration_tests.sh full  # 完整测试"
        echo
        return 0
    else
        print_message $RED "❌ $failed_checks 个验证失败"
        print_message $YELLOW "请检查上面的错误信息并修复问题"
        echo
        return $failed_checks
    fi
}

# 运行主函数
main "$@"
