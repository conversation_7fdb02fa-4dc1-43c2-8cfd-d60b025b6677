#!/bin/bash

# FlexProxy 集成测试快速验证脚本
# 用于快速验证集成测试环境的基本功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DNS_PORT=15353
PROXY_PORT=18080
FLEXPROXY_PORT=19080

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 等待端口可用
wait_for_port() {
    local port=$1
    local timeout=${2:-10}
    local count=0
    
    while [ $count -lt $timeout ]; do
        if check_port $port; then
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    
    return 1
}

# 测试DNS服务器
test_dns_server() {
    print_message $BLUE "测试DNS服务器..."
    
    cd "$SCRIPT_DIR/mock_servers"
    
    # 启动DNS服务器
    nohup go run dns_server.go $DNS_PORT > ../logs/dns_test.log 2>&1 &
    local dns_pid=$!
    
    # 等待启动
    if wait_for_port $DNS_PORT; then
        print_message $GREEN "✓ DNS服务器启动成功 (端口 $DNS_PORT)"
        
        # 停止DNS服务器
        kill $dns_pid 2>/dev/null || true
        wait $dns_pid 2>/dev/null || true
        
        return 0
    else
        print_message $RED "✗ DNS服务器启动失败"
        kill $dns_pid 2>/dev/null || true
        return 1
    fi
}

# 测试代理服务器
test_proxy_server() {
    print_message $BLUE "测试代理服务器..."
    
    cd "$SCRIPT_DIR/mock_servers"
    
    # 启动代理服务器
    nohup go run proxy_server.go $PROXY_PORT "test-proxy" > ../logs/proxy_test.log 2>&1 &
    local proxy_pid=$!
    
    # 等待启动
    if wait_for_port $PROXY_PORT; then
        print_message $GREEN "✓ 代理服务器启动成功 (端口 $PROXY_PORT)"
        
        # 测试健康检查
        if curl -s "http://127.0.0.1:$PROXY_PORT/health" > /dev/null; then
            print_message $GREEN "✓ 代理服务器健康检查正常"
        else
            print_message $YELLOW "⚠ 代理服务器健康检查失败"
        fi
        
        # 停止代理服务器
        kill $proxy_pid 2>/dev/null || true
        wait $proxy_pid 2>/dev/null || true
        
        return 0
    else
        print_message $RED "✗ 代理服务器启动失败"
        kill $proxy_pid 2>/dev/null || true
        return 1
    fi
}

# 测试配置文件
test_config_files() {
    print_message $BLUE "测试配置文件..."
    
    # 检查代理文件
    if [ -f "$SCRIPT_DIR/test_data/proxies_large.txt" ]; then
        local proxy_count=$(grep -c "^http\|^socks" "$SCRIPT_DIR/test_data/proxies_large.txt" || echo 0)
        print_message $GREEN "✓ 代理文件存在，包含 $proxy_count 个代理"
    else
        print_message $RED "✗ 代理文件不存在"
        return 1
    fi
    
    # 检查hosts文件
    if [ -f "$SCRIPT_DIR/test_data/hosts_custom.txt" ]; then
        local hosts_count=$(grep -c "^[0-9]" "$SCRIPT_DIR/test_data/hosts_custom.txt" || echo 0)
        print_message $GREEN "✓ Hosts文件存在，包含 $hosts_count 个条目"
    else
        print_message $RED "✗ Hosts文件不存在"
        return 1
    fi
    
    # 检查配置文件
    if [ -f "$SCRIPT_DIR/test_data/integration_config.yaml" ]; then
        print_message $GREEN "✓ 集成配置文件存在"
    else
        print_message $RED "✗ 集成配置文件不存在"
        return 1
    fi
    
    return 0
}

# 测试Go模块
test_go_modules() {
    print_message $BLUE "测试Go模块..."
    
    # 测试scenarios模块
    if [ -f "$SCRIPT_DIR/scenarios/go.mod" ]; then
        cd "$SCRIPT_DIR/scenarios"
        if go mod verify > /dev/null 2>&1; then
            print_message $GREEN "✓ Scenarios模块验证通过"
        else
            print_message $YELLOW "⚠ Scenarios模块验证失败，尝试修复..."
            go mod tidy
        fi
    else
        print_message $RED "✗ Scenarios模块不存在"
        return 1
    fi
    
    # 测试performance模块
    if [ -f "$SCRIPT_DIR/performance/go.mod" ]; then
        cd "$SCRIPT_DIR/performance"
        if go mod verify > /dev/null 2>&1; then
            print_message $GREEN "✓ Performance模块验证通过"
        else
            print_message $YELLOW "⚠ Performance模块验证失败，尝试修复..."
            go mod tidy
        fi
    else
        print_message $RED "✗ Performance模块不存在"
        return 1
    fi
    
    return 0
}

# 运行简单的编译测试
test_compilation() {
    print_message $BLUE "测试代码编译..."
    
    # 测试DNS服务器编译
    cd "$SCRIPT_DIR/mock_servers"
    if go build -o /tmp/dns_server_test dns_server.go > /dev/null 2>&1; then
        print_message $GREEN "✓ DNS服务器编译成功"
        rm -f /tmp/dns_server_test
    else
        print_message $RED "✗ DNS服务器编译失败"
        return 1
    fi
    
    # 测试代理服务器编译
    if go build -o /tmp/proxy_server_test proxy_server.go > /dev/null 2>&1; then
        print_message $GREEN "✓ 代理服务器编译成功"
        rm -f /tmp/proxy_server_test
    else
        print_message $RED "✗ 代理服务器编译失败"
        return 1
    fi
    
    # 测试集成测试编译
    cd "$SCRIPT_DIR/scenarios"
    if go test -c -o /tmp/integration_test > /dev/null 2>&1; then
        print_message $GREEN "✓ 集成测试编译成功"
        rm -f /tmp/integration_test
    else
        print_message $RED "✗ 集成测试编译失败"
        return 1
    fi
    
    # 测试性能测试编译
    cd "$SCRIPT_DIR/performance"
    if go test -c -o /tmp/performance_test > /dev/null 2>&1; then
        print_message $GREEN "✓ 性能测试编译成功"
        rm -f /tmp/performance_test
    else
        print_message $RED "✗ 性能测试编译失败"
        return 1
    fi
    
    return 0
}

# 运行快速功能测试
run_quick_functional_test() {
    print_message $BLUE "运行快速功能测试..."
    
    cd "$SCRIPT_DIR/scenarios"
    
    # 运行一个简单的测试
    if timeout 30 go test -v -run TestSimple . > /dev/null 2>&1; then
        print_message $GREEN "✓ 快速功能测试通过"
        return 0
    else
        print_message $YELLOW "⚠ 快速功能测试失败或超时"
        return 1
    fi
}

# 主函数
main() {
    print_message $BLUE "================================"
    print_message $BLUE "FlexProxy 集成测试环境快速验证"
    print_message $BLUE "================================"
    echo
    
    # 创建日志目录
    mkdir -p "$SCRIPT_DIR/logs"
    
    local failed_tests=0
    
    # 运行各项测试
    test_config_files || ((failed_tests++))
    echo
    
    test_go_modules || ((failed_tests++))
    echo
    
    test_compilation || ((failed_tests++))
    echo
    
    test_dns_server || ((failed_tests++))
    echo
    
    test_proxy_server || ((failed_tests++))
    echo
    
    run_quick_functional_test || ((failed_tests++))
    echo
    
    # 显示结果
    print_message $BLUE "================================"
    if [ $failed_tests -eq 0 ]; then
        print_message $GREEN "🎉 所有测试通过！集成测试环境就绪"
        print_message $GREEN "可以运行完整的集成测试："
        print_message $YELLOW "  ./run_integration_tests.sh full"
    else
        print_message $RED "❌ $failed_tests 个测试失败"
        print_message $YELLOW "请检查上面的错误信息并修复问题"
    fi
    print_message $BLUE "================================"
    
    return $failed_tests
}

# 运行主函数
main "$@"
