=== RUN   TestIPBanSystem
=== RUN   TestIPBanSystem/测试单个IP封禁
    ban_system_test.go:57: ✓ IP *********** 已被封禁
    ban_system_test.go:57: ✓ IP *********** 已被封禁
    ban_system_test.go:57: ✓ IP *********** 已被封禁
=== RUN   TestIPBanSystem/测试网段封禁
    ban_system_test.go:77: ✓ IP ***********00 在封禁网段内
    ban_system_test.go:77: ✓ IP ************* 在封禁网段内
    ban_system_test.go:77: ✓ IP *********** 在封禁网段内
    ban_system_test.go:79: ✓ IP ************* 不在封禁网段内
=== RUN   TestIPBanSystem/测试信任IP
    ban_system_test.go:95: ✓ IP 127.0.0.1 是信任IP
    ban_system_test.go:95: ✓ IP ::1 是信任IP
    ban_system_test.go:95: ✓ IP ************ 是信任IP
    ban_system_test.go:95: ✓ IP ********** 是信任IP
=== RUN   TestIPBanSystem/测试封禁优先级
--- PASS: TestIPBanSystem (0.00s)
    --- PASS: TestIPBanSystem/测试单个IP封禁 (0.00s)
    --- PASS: TestIPBanSystem/测试网段封禁 (0.00s)
    --- PASS: TestIPBanSystem/测试信任IP (0.00s)
    --- PASS: TestIPBanSystem/测试封禁优先级 (0.00s)
=== RUN   TestDomainBanSystem
=== RUN   TestDomainBanSystem/测试域名封禁
    ban_system_test.go:133: ✓ 域名 malicious-site.com 已被封禁
    ban_system_test.go:133: ✓ 域名 spam-domain.net 已被封禁
    ban_system_test.go:133: ✓ 域名 phishing-site.org 已被封禁
=== RUN   TestDomainBanSystem/测试子域名封禁
    ban_system_test.go:149: ✓ 子域名 www.malicious-site.com 已被封禁
    ban_system_test.go:149: ✓ 子域名 api.spam-domain.net 已被封禁
    ban_system_test.go:149: ✓ 子域名 login.phishing-site.org 已被封禁
=== RUN   TestDomainBanSystem/测试正常域名
    ban_system_test.go:163: ✓ 域名 google.com 未被封禁
    ban_system_test.go:163: ✓ 域名 github.com 未被封禁
    ban_system_test.go:163: ✓ 域名 stackoverflow.com 未被封禁
--- PASS: TestDomainBanSystem (0.00s)
    --- PASS: TestDomainBanSystem/测试域名封禁 (0.00s)
    --- PASS: TestDomainBanSystem/测试子域名封禁 (0.00s)
    --- PASS: TestDomainBanSystem/测试正常域名 (0.00s)
=== RUN   TestExclusionPatterns
=== RUN   TestExclusionPatterns/测试通配符模式匹配
    ban_system_test.go:199: ✓ URL http://api.local 匹配排除模式
    ban_system_test.go:199: ✓ URL https://service.local:8080 匹配排除模式
    ban_system_test.go:199: ✓ URL http://localhost:3000 匹配排除模式
    ban_system_test.go:199: ✓ URL https://127.0.0.1:8080 匹配排除模式
    ban_system_test.go:199: ✓ URL http://db.test.internal 匹配排除模式
    ban_system_test.go:199: ✓ URL https://api.integration.test 匹配排除模式
    ban_system_test.go:201: ✓ URL http://google.com 不匹配排除模式
    ban_system_test.go:201: ✓ URL https://github.com:443 不匹配排除模式
=== RUN   TestExclusionPatterns/测试端口通配符
    ban_system_test.go:218: ✓ URL localhost:3000 匹配端口通配符
    ban_system_test.go:218: ✓ URL localhost:8080 匹配端口通配符
    ban_system_test.go:218: ✓ URL 127.0.0.1:9000 匹配端口通配符
    ban_system_test.go:218: ✓ URL 127.0.0.1:443 匹配端口通配符
--- PASS: TestExclusionPatterns (0.00s)
    --- PASS: TestExclusionPatterns/测试通配符模式匹配 (0.00s)
    --- PASS: TestExclusionPatterns/测试端口通配符 (0.00s)
=== RUN   TestProxyPoolManagement
=== RUN   TestProxyPoolManagement/测试最小代理池大小
    ban_system_test.go:241: ✓ 代理池大小检查: 当前=3, 最小要求=5, 需要补充=true
=== RUN   TestProxyPoolManagement/测试代理获取重试
    ban_system_test.go:252: ✓ 第 1 次尝试获取代理 (最大 3 次)
    ban_system_test.go:252: ✓ 第 2 次尝试获取代理 (最大 3 次)
    ban_system_test.go:252: ✓ 第 3 次尝试获取代理 (最大 3 次)
    ban_system_test.go:254: ✓ 已达到最大重试次数 3，停止尝试
--- PASS: TestProxyPoolManagement (0.00s)
    --- PASS: TestProxyPoolManagement/测试最小代理池大小 (0.00s)
    --- PASS: TestProxyPoolManagement/测试代理获取重试 (0.00s)
=== RUN   TestDNSConfiguration
=== RUN   TestDNSConfiguration/测试DNS缓存TTL
    ban_system_test.go:286: ✓ DNS缓存TTL设置为 1m0s
=== RUN   TestDNSConfiguration/测试IP版本优先级
    ban_system_test.go:309: ✓ 选择IP版本: ipv4 (优先级: ipv4)
=== RUN   TestDNSConfiguration/测试DNS超时设置
    ban_system_test.go:322: ✓ DNS超时设置: 3s, 实际耗时: 56ns
--- PASS: TestDNSConfiguration (0.00s)
    --- PASS: TestDNSConfiguration/测试DNS缓存TTL (0.00s)
    --- PASS: TestDNSConfiguration/测试IP版本优先级 (0.00s)
    --- PASS: TestDNSConfiguration/测试DNS超时设置 (0.00s)
=== RUN   TestGlobalConfigIntegration
    global_config_integration_test.go:101: DNS服务器已启动在端口 5353
    global_config_integration_test.go:125: 代理服务器 proxy-1 已启动在端口 8080
    global_config_integration_test.go:125: 代理服务器 proxy-2 已启动在端口 8081
    global_config_integration_test.go:125: 代理服务器 proxy-3 已启动在端口 8082
    global_config_integration_test.go:125: 代理服务器 proxy-4 已启动在端口 8083
    global_config_integration_test.go:125: 代理服务器 proxy-5 已启动在端口 8084
    global_config_integration_test.go:173: 模拟FlexProxy已启动在端口 18080
=== RUN   TestGlobalConfigIntegration/TestBanSystem
    global_config_integration_test.go:293: 封禁系统测试完成
=== RUN   TestGlobalConfigIntegration/TestConcurrentRequests
    global_config_integration_test.go:367: 并发测试完成: 500/500 成功 (100.00%), 耗时: 1.020793905s
=== RUN   TestGlobalConfigIntegration/TestDNSCaching
    global_config_integration_test.go:318: DNS缓存测试: 第一次=94ns, 第二次=52ns
=== RUN   TestGlobalConfigIntegration/TestIPRotationModes
=== RUN   TestGlobalConfigIntegration/TestIPRotationModes/模式_random
=== NAME  TestGlobalConfigIntegration/TestIPRotationModes
    global_config_integration_test.go:402: 切换IP轮换模式到: random
=== NAME  TestGlobalConfigIntegration/TestIPRotationModes/模式_random
    global_config_integration_test.go:209: IP轮换模式 random 测试通过
=== RUN   TestGlobalConfigIntegration/TestIPRotationModes/模式_sequential
=== NAME  TestGlobalConfigIntegration/TestIPRotationModes
    global_config_integration_test.go:402: 切换IP轮换模式到: sequential
=== NAME  TestGlobalConfigIntegration/TestIPRotationModes/模式_sequential
    global_config_integration_test.go:209: IP轮换模式 sequential 测试通过
=== RUN   TestGlobalConfigIntegration/TestIPRotationModes/模式_quality
=== NAME  TestGlobalConfigIntegration/TestIPRotationModes
    global_config_integration_test.go:402: 切换IP轮换模式到: quality
=== NAME  TestGlobalConfigIntegration/TestIPRotationModes/模式_quality
    global_config_integration_test.go:209: IP轮换模式 quality 测试通过
=== RUN   TestGlobalConfigIntegration/TestIPRotationModes/模式_smart
=== NAME  TestGlobalConfigIntegration/TestIPRotationModes
    global_config_integration_test.go:402: 切换IP轮换模式到: smart
=== NAME  TestGlobalConfigIntegration/TestIPRotationModes/模式_smart
    global_config_integration_test.go:209: IP轮换模式 smart 测试通过
=== RUN   TestGlobalConfigIntegration/TestProxyFailover
    global_config_integration_test.go:456: 代理端口 8080 健康状态设置为: false
    global_config_integration_test.go:456: 代理端口 8081 健康状态设置为: false
    global_config_integration_test.go:456: 代理端口 8082 健康状态设置为: false
    global_config_integration_test.go:456: 代理端口 8080 健康状态设置为: true
    global_config_integration_test.go:456: 代理端口 8081 健康状态设置为: true
    global_config_integration_test.go:456: 代理端口 8082 健康状态设置为: true
    global_config_integration_test.go:393: 代理故障转移测试完成
=== RUN   TestGlobalConfigIntegration/TestRetryPolicyWithCooldown
    global_config_integration_test.go:456: 代理端口 8080 健康状态设置为: false
    global_config_integration_test.go:456: 代理端口 8080 健康状态设置为: true
    global_config_integration_test.go:271: 重试策略测试完成，初始请求耗时: 101.522215ms
=== RUN   TestGlobalConfigIntegration/TestReverseDNSLookup
=== RUN   TestGlobalConfigIntegration/TestReverseDNSLookup/IP_*******
    global_config_integration_test.go:237: 反向DNS查找 ******* -> one.one.one.one
=== RUN   TestGlobalConfigIntegration/TestReverseDNSLookup/IP_*******
    global_config_integration_test.go:237: 反向DNS查找 ******* -> dns.google
=== RUN   TestGlobalConfigIntegration/TestReverseDNSLookup/IP_127.0.0.1
    global_config_integration_test.go:237: 反向DNS查找 127.0.0.1 -> localhost
=== RUN   TestGlobalConfigIntegration/TestReverseDNSLookup/IP_***********
    global_config_integration_test.go:237: 反向DNS查找 *********** -> unknown
--- PASS: TestGlobalConfigIntegration (52.88s)
    --- PASS: TestGlobalConfigIntegration/TestBanSystem (0.00s)
    --- PASS: TestGlobalConfigIntegration/TestConcurrentRequests (1.02s)
    --- PASS: TestGlobalConfigIntegration/TestDNSCaching (0.00s)
    --- PASS: TestGlobalConfigIntegration/TestIPRotationModes (8.11s)
        --- PASS: TestGlobalConfigIntegration/TestIPRotationModes/模式_random (2.02s)
        --- PASS: TestGlobalConfigIntegration/TestIPRotationModes/模式_sequential (2.02s)
        --- PASS: TestGlobalConfigIntegration/TestIPRotationModes/模式_quality (2.02s)
        --- PASS: TestGlobalConfigIntegration/TestIPRotationModes/模式_smart (2.05s)
    --- PASS: TestGlobalConfigIntegration/TestProxyFailover (3.41s)
    --- PASS: TestGlobalConfigIntegration/TestRetryPolicyWithCooldown (35.31s)
    --- PASS: TestGlobalConfigIntegration/TestReverseDNSLookup (0.00s)
        --- PASS: TestGlobalConfigIntegration/TestReverseDNSLookup/IP_******* (0.00s)
        --- PASS: TestGlobalConfigIntegration/TestReverseDNSLookup/IP_******* (0.00s)
        --- PASS: TestGlobalConfigIntegration/TestReverseDNSLookup/IP_127.0.0.1 (0.00s)
        --- PASS: TestGlobalConfigIntegration/TestReverseDNSLookup/IP_*********** (0.00s)
=== RUN   TestNetworkFailureScenarios
=== RUN   TestNetworkFailureScenarios/模拟单个代理故障
    network_failure_test.go:36: ✓ 单个代理故障和恢复测试完成
=== RUN   TestNetworkFailureScenarios/模拟多个代理故障
    network_failure_test.go:71: ✓ 多个代理故障和恢复测试完成
=== RUN   TestNetworkFailureScenarios/模拟代理响应延迟
    network_failure_test.go:97: ✓ 代理 18081 延迟测试: 设置=100ms, 实际=101.620745ms
    network_failure_test.go:97: ✓ 代理 18082 延迟测试: 设置=500ms, 实际=502.136466ms
    network_failure_test.go:97: ✓ 代理 18083 延迟测试: 设置=1000ms, 实际=1.001797332s
    network_failure_test.go:97: ✓ 代理 18084 延迟测试: 设置=2000ms, 实际=2.001944148s
    network_failure_test.go:107: ✓ 代理响应延迟测试完成
=== RUN   TestNetworkFailureScenarios/模拟代理失败率
    network_failure_test.go:139: ✓ 代理 18081 失败率测试: 期望成功率=0.9, 实际成功率=0.8
    network_failure_test.go:139: ✓ 代理 18082 失败率测试: 期望成功率=0.8, 实际成功率=0.8
    network_failure_test.go:139: ✓ 代理 18083 失败率测试: 期望成功率=0.5, 实际成功率=0.5
    network_failure_test.go:149: ✓ 代理失败率测试完成
--- PASS: TestNetworkFailureScenarios (6.71s)
    --- PASS: TestNetworkFailureScenarios/模拟单个代理故障 (0.00s)
    --- PASS: TestNetworkFailureScenarios/模拟多个代理故障 (0.00s)
    --- PASS: TestNetworkFailureScenarios/模拟代理响应延迟 (3.61s)
    --- PASS: TestNetworkFailureScenarios/模拟代理失败率 (3.09s)
=== RUN   TestLoadBalancing
=== RUN   TestLoadBalancing/测试轮询负载均衡
    network_failure_test.go:161: 代理 18081 初始统计: map[port:18081 status:200]
    network_failure_test.go:161: 代理 18082 初始统计: map[port:18082 status:200]
    network_failure_test.go:161: 代理 18083 初始统计: map[port:18083 status:200]
    network_failure_test.go:161: 代理 18084 初始统计: map[port:18084 status:200]
    network_failure_test.go:183: 代理 18081 最终统计: map[port:18081 status:200]
    network_failure_test.go:183: 代理 18082 最终统计: map[port:18082 status:200]
    network_failure_test.go:183: 代理 18083 最终统计: map[port:18083 status:200]
    network_failure_test.go:183: 代理 18084 最终统计: map[port:18084 status:200]
    network_failure_test.go:186: ✓ 轮询负载均衡测试完成
=== RUN   TestLoadBalancing/测试基于质量的负载均衡
    network_failure_test.go:229: ✓ 质量对比: 高质量代理平均=51.640949ms, 低质量代理平均=1.001532914s
    network_failure_test.go:239: ✓ 基于质量的负载均衡测试完成
--- PASS: TestLoadBalancing (13.59s)
    --- PASS: TestLoadBalancing/测试轮询负载均衡 (1.03s)
    --- PASS: TestLoadBalancing/测试基于质量的负载均衡 (12.56s)
=== RUN   TestCooldownMechanism
=== RUN   TestCooldownMechanism/测试代理冷却机制
    network_failure_test.go:266: ✓ 在冷却期内，代理应该仍然被避免使用
    network_failure_test.go:272: 等待冷却期结束: 4.948402004s
    network_failure_test.go:280: ✓ 代理冷却机制测试完成
--- PASS: TestCooldownMechanism (5.05s)
    --- PASS: TestCooldownMechanism/测试代理冷却机制 (5.05s)
=== RUN   TestSimpleIntegration
    simple_integration_test.go:14: 运行简单集成测试...
    simple_integration_test.go:19: 简单集成测试完成
--- PASS: TestSimpleIntegration (0.00s)
=== RUN   TestHTTPClient
    simple_integration_test.go:31: 跳过网络测试，网络不可用: Get "https://httpbin.org/status/200": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
--- SKIP: TestHTTPClient (5.00s)
=== RUN   TestConfigStructures
    simple_integration_test.go:83: 配置结构测试通过
--- PASS: TestConfigStructures (0.00s)
=== RUN   TestIPRotationModes
=== RUN   TestIPRotationModes/模式_random
    simple_integration_test.go:112: IP轮换模式 random 验证通过
=== RUN   TestIPRotationModes/模式_sequential
    simple_integration_test.go:112: IP轮换模式 sequential 验证通过
=== RUN   TestIPRotationModes/模式_quality
    simple_integration_test.go:112: IP轮换模式 quality 验证通过
=== RUN   TestIPRotationModes/模式_smart
    simple_integration_test.go:112: IP轮换模式 smart 验证通过
--- PASS: TestIPRotationModes (0.00s)
    --- PASS: TestIPRotationModes/模式_random (0.00s)
    --- PASS: TestIPRotationModes/模式_sequential (0.00s)
    --- PASS: TestIPRotationModes/模式_quality (0.00s)
    --- PASS: TestIPRotationModes/模式_smart (0.00s)
=== RUN   TestRetryPolicies
=== RUN   TestRetryPolicies/策略_allow
    simple_integration_test.go:143: 重试策略 allow 验证通过
=== RUN   TestRetryPolicies/策略_deny
    simple_integration_test.go:143: 重试策略 deny 验证通过
=== RUN   TestRetryPolicies/策略_cooldown
    simple_integration_test.go:143: 重试策略 cooldown 验证通过
--- PASS: TestRetryPolicies (0.00s)
    --- PASS: TestRetryPolicies/策略_allow (0.00s)
    --- PASS: TestRetryPolicies/策略_deny (0.00s)
    --- PASS: TestRetryPolicies/策略_cooldown (0.00s)
=== RUN   TestDNSModes
=== RUN   TestDNSModes/DNS模式_system
    simple_integration_test.go:158: DNS模式 system 验证通过
=== RUN   TestDNSModes/DNS模式_custom
    simple_integration_test.go:158: DNS模式 custom 验证通过
=== RUN   TestDNSModes/DNS模式_hybrid
    simple_integration_test.go:158: DNS模式 hybrid 验证通过
--- PASS: TestDNSModes (0.00s)
    --- PASS: TestDNSModes/DNS模式_system (0.00s)
    --- PASS: TestDNSModes/DNS模式_custom (0.00s)
    --- PASS: TestDNSModes/DNS模式_hybrid (0.00s)
=== RUN   TestReverseDNSModes
=== RUN   TestReverseDNSModes/反向DNS模式_no
    simple_integration_test.go:173: 反向DNS模式 no 验证通过
=== RUN   TestReverseDNSModes/反向DNS模式_dns
    simple_integration_test.go:173: 反向DNS模式 dns 验证通过
=== RUN   TestReverseDNSModes/反向DNS模式_file
    simple_integration_test.go:173: 反向DNS模式 file 验证通过
=== RUN   TestReverseDNSModes/反向DNS模式_values
    simple_integration_test.go:173: 反向DNS模式 values 验证通过
--- PASS: TestReverseDNSModes (0.00s)
    --- PASS: TestReverseDNSModes/反向DNS模式_no (0.00s)
    --- PASS: TestReverseDNSModes/反向DNS模式_dns (0.00s)
    --- PASS: TestReverseDNSModes/反向DNS模式_file (0.00s)
    --- PASS: TestReverseDNSModes/反向DNS模式_values (0.00s)
=== RUN   TestConfigValidation
=== RUN   TestConfigValidation/有效配置
=== RUN   TestConfigValidation/禁用配置
=== RUN   TestConfigValidation/空文件路径
--- PASS: TestConfigValidation (0.00s)
    --- PASS: TestConfigValidation/有效配置 (0.00s)
    --- PASS: TestConfigValidation/禁用配置 (0.00s)
    --- PASS: TestConfigValidation/空文件路径 (0.00s)
=== RUN   TestPerformanceBasics
    simple_integration_test.go:215: 基础性能测试完成，耗时: 181.455µs
--- PASS: TestPerformanceBasics (0.00s)
=== RUN   TestConcurrentOperations
    simple_integration_test.go:252: 并发测试完成: 1000个操作，耗时: 117.245371ms
--- PASS: TestConcurrentOperations (0.12s)
PASS
ok  	integration_scenarios	83.697s
