# FlexProxy 集成测试配置文件
# 用于全面测试 global 配置模块的所有功能

# ================================
# 全局配置 - 完整功能测试
# ================================
global:
  enable: true
  proxy_file: "./test_data/proxies_large.txt"
  
  # IP轮换模式测试
  ip_rotation_mode: "smart"  # 将在测试中动态切换: random, sequential, quality, smart
  
  # DNS配置测试
  dns_lookup_mode: "custom"
  reverse_dns_lookup: "file:./test_data/hosts_custom.txt"
  http_proxy_dns: "127.0.0.1:5353"  # 使用模拟DNS服务器
  dns_cache_ttl: 60  # 短TTL用于测试缓存行为
  dns_no_cache: false
  ip_version_priority: "ipv4"
  default_dns_timeout: 3000
  
  # 自定义DNS服务器配置
  custom_dns_servers:
    - server: "127.0.0.1:5353"
      protocol: "udp"
      timeout: 3000
      priority: 1
      tags: ["local", "test"]
    - server: "*******:53"
      protocol: "udp"
      timeout: 5000
      priority: 2
      tags: ["cloudflare", "backup"]
    - server: "*******:53"
      protocol: "udp"
      timeout: 5000
      priority: 3
      tags: ["google", "fallback"]
  
  # 重试策略配置
  retry_proxy_reuse_policy: "cooldown"
  retry_proxy_cooldown_time: 30  # 短冷却时间用于快速测试
  retry_proxy_global_tracking: true
  
  # 代理池配置
  min_proxy_pool_size: 5
  max_proxy_fetch_attempts: 3
  
  # 封禁系统配置
  global_banned_ips:
    - ip: "***********"
      duration: 300  # 5分钟
    - ip: "***********"
      duration: "10m"
    - ip: "***********"
      duration: "reboot"
  
  banned_domains:
    - domain: "malicious-site.com"
      duration: 600  # 10分钟
    - domain: "spam-domain.net"
      duration: "1h"
    - domain: "phishing-site.org"
      duration: "reboot"
  
  blocked_ips:
    - "***********/24"  # 整个测试网段
    - "************/24"
    - "*********/24"
  
  trusted_ips:
    - "127.0.0.1"
    - "::1"
    - "***********/24"
    - "10.0.0.0/8"
  
  # 排除规则配置
  excluded_patterns:
    - "*.local"
    - "localhost:*"
    - "127.0.0.1:*"
    - "*.test.internal"
    - "*.integration.test"
  
  excluded_scope: "all"
  rule_priority: 100
  default_process_stage: "post_header"

# ================================
# 服务器配置 - 集成测试优化
# ================================
server:
  host: "127.0.0.1"
  port: 18080  # 使用不同端口避免冲突
  https_port: 18443
  socks_port: 11080
  read_timeout: "10s"
  write_timeout: "10s"
  idle_timeout: "30s"
  connect_timeout: "5s"
  max_idle_conns: 50
  max_idle_conns_per_host: 5
  max_conns_per_host: 20
  buffer_size: 4096
  max_header_bytes: 1048576
  debounce_delay: "50ms"

# ================================
# 代理配置 - 集成测试
# ================================
proxy:
  enabled: true
  strategy: "quality"  # 将在测试中切换不同策略
  load_balancer: "round_robin"
  max_retries: 2  # 减少重试次数加快测试
  retry_interval: "500ms"
  max_retry_interval: "5s"
  pool_size: 20
  rotation_interval: 30  # 30秒轮换用于测试
  
  # 健康检查配置
  health_check:
    enabled: true
    interval: "10s"  # 频繁检查用于测试
    timeout: "3s"
    path: "/health"
    max_consecutive_failures: 2
    max_consecutive_successes: 1
  
  # 质量评分配置
  quality_score:
    default: 0.5
    success_rate_weight: 0.7
    response_time_weight: 0.3
    max_failure_rate: 0.5  # 宽松的失败率用于测试
    top_proxy_ratio: 0.3
    response_time_baseline: 2000  # 2秒基线
    smoothing_factor: 0.2

# ================================
# 缓存配置 - 测试优化
# ================================
cache:
  enabled: true
  type: "memory"
  ttl: "60s"  # 短TTL用于测试
  size: 100
  cleanup_interval: "30s"
  
  key_prefixes:
    proxy_list: "test:proxy:list"
    proxy_status: "test:proxy:status:"
    user_session: "test:user:session:"
    rate_limit: "test:rate:limit:"
  
  dns:
    ttl: "30s"  # 短DNS缓存TTL
    cleanup_interval: "60s"

# ================================
# 日志配置 - 详细日志用于调试
# ================================
logging:
  enabled: true
  level: "debug"  # 详细日志用于测试
  format: "json"
  file: "./logs/integration_test.log"
  max_size: 10  # 小文件便于管理
  max_backups: 3
  max_age: 1
  time_format: "2006-01-02T15:04:05.000Z07:00"

# ================================
# 监控配置 - 测试统计
# ================================
monitoring:
  enabled: true
  port: 19090  # 避免端口冲突
  path: "/metrics"
  interval: "5s"  # 频繁收集用于测试
  
  metrics:
    enabled: true
    namespace: "flexproxy_integration_test"
    subsystem: "global_config"
  
  labels:
    service: "flexproxy-integration-test"
    version: "test"
    environment: "integration"

# ================================
# 安全配置 - 基础安全
# ================================
security:
  enabled: true
  
  auth:
    type: "none"  # 简化认证用于测试
    token_expiry: "1h"
  
  encryption:
    algorithm: "aes256"
    key_length: 32
  
  tls:
    enabled: false  # 禁用TLS简化测试
    cert_file: ""
    key_file: ""
    min_version: "1.2"
    max_version: "1.3"

# ================================
# 限流配置 - 宽松限制
# ================================
rate_limiting:
  enabled: true
  global_rate: 1000  # 高限制用于测试
  per_ip_rate: 100
  burst_size: 50
  window_size: "1m"
  
  rules:
    - pattern: "/test/*"
      rate: 500
      burst: 25
    - pattern: "/health"
      rate: 1000
      burst: 100

# ================================
# DNS服务配置 - 集成测试
# ================================
dns_service:
  enabled: true
  port: 5354  # 不同端口避免冲突
  timeout: "3s"
  
  servers:
    - name: "local_test"
      address: "127.0.0.1:5353"
      protocol: "udp"
      timeout: "2s"
      priority: 1
    - name: "cloudflare"
      address: "*******:53"
      protocol: "udp"
      timeout: "5s"
      priority: 2
    - name: "google"
      address: "*******:53"
      protocol: "udp"
      timeout: "5s"
      priority: 3
