# FlexProxy 大型代理列表 - 用于性能测试
# 包含100个模拟代理服务器

# 高性能代理组 (端口 8080-8099)
http://proxy-fast-01.test.com:8080
http://proxy-fast-02.test.com:8081
http://proxy-fast-03.test.com:8082
http://proxy-fast-04.test.com:8083
http://proxy-fast-05.test.com:8084
http://proxy-fast-06.test.com:8085
http://proxy-fast-07.test.com:8086
http://proxy-fast-08.test.com:8087
http://proxy-fast-09.test.com:8088
http://proxy-fast-10.test.com:8089
http://proxy-fast-11.test.com:8090
http://proxy-fast-12.test.com:8091
http://proxy-fast-13.test.com:8092
http://proxy-fast-14.test.com:8093
http://proxy-fast-15.test.com:8094
http://proxy-fast-16.test.com:8095
http://proxy-fast-17.test.com:8096
http://proxy-fast-18.test.com:8097
http://proxy-fast-19.test.com:8098
http://proxy-fast-20.test.com:8099

# 中等性能代理组 (端口 8100-8149)
http://proxy-medium-01.test.com:8100
http://proxy-medium-02.test.com:8101
http://proxy-medium-03.test.com:8102
http://proxy-medium-04.test.com:8103
http://proxy-medium-05.test.com:8104
http://proxy-medium-06.test.com:8105
http://proxy-medium-07.test.com:8106
http://proxy-medium-08.test.com:8107
http://proxy-medium-09.test.com:8108
http://proxy-medium-10.test.com:8109
http://proxy-medium-11.test.com:8110
http://proxy-medium-12.test.com:8111
http://proxy-medium-13.test.com:8112
http://proxy-medium-14.test.com:8113
http://proxy-medium-15.test.com:8114
http://proxy-medium-16.test.com:8115
http://proxy-medium-17.test.com:8116
http://proxy-medium-18.test.com:8117
http://proxy-medium-19.test.com:8118
http://proxy-medium-20.test.com:8119
http://proxy-medium-21.test.com:8120
http://proxy-medium-22.test.com:8121
http://proxy-medium-23.test.com:8122
http://proxy-medium-24.test.com:8123
http://proxy-medium-25.test.com:8124
http://proxy-medium-26.test.com:8125
http://proxy-medium-27.test.com:8126
http://proxy-medium-28.test.com:8127
http://proxy-medium-29.test.com:8128
http://proxy-medium-30.test.com:8129

# 慢速代理组 (端口 8150-8179)
http://proxy-slow-01.test.com:8150
http://proxy-slow-02.test.com:8151
http://proxy-slow-03.test.com:8152
http://proxy-slow-04.test.com:8153
http://proxy-slow-05.test.com:8154
http://proxy-slow-06.test.com:8155
http://proxy-slow-07.test.com:8156
http://proxy-slow-08.test.com:8157
http://proxy-slow-09.test.com:8158
http://proxy-slow-10.test.com:8159
http://proxy-slow-11.test.com:8160
http://proxy-slow-12.test.com:8161
http://proxy-slow-13.test.com:8162
http://proxy-slow-14.test.com:8163
http://proxy-slow-15.test.com:8164
http://proxy-slow-16.test.com:8165
http://proxy-slow-17.test.com:8166
http://proxy-slow-18.test.com:8167
http://proxy-slow-19.test.com:8168
http://proxy-slow-20.test.com:8169

# SOCKS5 代理组 (端口 1080-1099)
socks5://socks-01.test.com:1080
socks5://socks-02.test.com:1081
socks5://socks-03.test.com:1082
socks5://socks-04.test.com:1083
socks5://socks-05.test.com:1084
socks5://socks-06.test.com:1085
socks5://socks-07.test.com:1086
socks5://socks-08.test.com:1087
socks5://socks-09.test.com:1088
socks5://socks-10.test.com:1089

# 带认证的代理组 (端口 8200-8219)
http://user1:<EMAIL>:8200
http://user2:<EMAIL>:8201
http://user3:<EMAIL>:8202
http://user4:<EMAIL>:8203
http://user5:<EMAIL>:8204
http://user6:<EMAIL>:8205
http://user7:<EMAIL>:8206
http://user8:<EMAIL>:8207
http://user9:<EMAIL>:8208
http://user10:<EMAIL>:8209

# 不稳定代理组 (端口 8300-8309) - 用于测试故障转移
http://unstable-01.test.com:8300
http://unstable-02.test.com:8301
http://unstable-03.test.com:8302
http://unstable-04.test.com:8303
http://unstable-05.test.com:8304
http://unstable-06.test.com:8305
http://unstable-07.test.com:8306
http://unstable-08.test.com:8307
http://unstable-09.test.com:8308
http://unstable-10.test.com:8309
