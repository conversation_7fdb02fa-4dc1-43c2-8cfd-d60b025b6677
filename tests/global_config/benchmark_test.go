package global_config_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/internal/proxymanager"
	"github.com/flexp/flexp/internal/server"
	"github.com/flexp/flexp/internal/services"
)

// BenchmarkProxyRotation 基准测试代理轮换性能
func BenchmarkProxyRotation(b *testing.B) {
	// 设置测试环境
	cleanup := SetupTestEnvironment(b)
	defer cleanup()

	config := CreateTestConfig()
	pm := proxymanager.NewProxyManager(GetTestProxyList())
	pm.Config = config

	b.ResetTimer()
	b.<PERSON>(func(pb *testing.PB) {
		for pb.Next() {
			proxy, err := pm.RotateProxy()
			if err != nil {
				b.Fatalf("代理轮换失败: %v", err)
			}
			if proxy == "" {
				b.Fatal("返回空代理")
			}
		}
	})
}

// BenchmarkSmartProxyMode 基准测试智能代理模式性能
func BenchmarkSmartProxyMode(b *testing.B) {
	cleanup := SetupTestEnvironment(b)
	defer cleanup()

	config := CreateTestConfig()
	config.Global.IPRotationMode = "smart"
	
	pm := proxymanager.NewProxyManager(GetTestProxyList())
	pm.Config = config

	domains := []string{
		"example.com",
		"google.com",
		"github.com",
		"stackoverflow.com",
		"reddit.com",
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		domainIndex := 0
		for pb.Next() {
			domain := domains[domainIndex%len(domains)]
			proxy, err := pm.GetProxyForDomain("smart", domain)
			if err != nil {
				b.Fatalf("智能模式获取代理失败: %v", err)
			}
			if proxy == "" {
				b.Fatal("返回空代理")
			}
			domainIndex++
		}
	})
}

// BenchmarkDNSResolution 基准测试DNS解析性能
func BenchmarkDNSResolution(b *testing.B) {
	cleanup := SetupTestEnvironment(b)
	defer cleanup()

	config := CreateTestConfig()
	dnsService := services.NewDNSService(config)

	domains := []string{
		"example.com",
		"google.com",
		"github.com",
		"cloudflare.com",
		"stackoverflow.com",
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		ctx := context.Background()
		domainIndex := 0
		for pb.Next() {
			domain := domains[domainIndex%len(domains)]
			_, err := dnsService.Resolve(ctx, domain)
			if err != nil {
				// DNS解析可能失败，这在基准测试中是可接受的
				continue
			}
			domainIndex++
		}
	})
}

// BenchmarkReverseDNSLookup 基准测试反向DNS查找性能
func BenchmarkReverseDNSLookup(b *testing.B) {
	cleanup := SetupTestEnvironment(b)
	defer cleanup()

	// 测试不同的反向DNS模式
	modes := []struct {
		name   string
		mode   string
		config string
	}{
		{"禁用模式", "no", ""},
		{"DNS模式", "dns", ""},
		{"文件模式", "file", "file:./test_hosts.txt"},
	}

	for _, mode := range modes {
		b.Run(mode.name, func(b *testing.B) {
			resolver, err := server.NewReverseDNSResolver(mode.mode, mode.config)
			if err != nil {
				b.Skipf("跳过 %s: %v", mode.name, err)
				return
			}

			ips := []string{
				"*******",
				"*******",
				"*******",
				"**************",
				"***********",
			}

			b.ResetTimer()
			b.RunParallel(func(pb *testing.PB) {
				ipIndex := 0
				for pb.Next() {
					ip := ips[ipIndex%len(ips)]
					_, _ = resolver.Lookup(ip)
					ipIndex++
				}
			})
		})
	}
}

// BenchmarkBanSystemOperations 基准测试封禁系统操作性能
func BenchmarkBanSystemOperations(b *testing.B) {
	cleanup := SetupTestEnvironment(b)
	defer cleanup()

	config := CreateTestConfig()
	pm := proxymanager.NewProxyManager(GetTestProxyList())
	pm.Config = config
	pm.InitBanSystem(config)

	b.Run("BanIP", func(b *testing.B) {
		b.RunParallel(func(pb *testing.PB) {
			ipCounter := 0
			for pb.Next() {
				ip := fmt.Sprintf("192.168.1.%d", ipCounter%254+1)
				_ = pm.BanIP(ip, "300", "global", "benchmark")
				ipCounter++
			}
		})
	})

	b.Run("IsIPBanned", func(b *testing.B) {
		// 预先封禁一些IP
		for i := 1; i <= 100; i++ {
			ip := fmt.Sprintf("10.0.0.%d", i)
			_ = pm.BanIP(ip, "3600", "global", "benchmark")
		}

		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			ipCounter := 0
			for pb.Next() {
				ip := fmt.Sprintf("10.0.0.%d", ipCounter%100+1)
				_ = pm.IsIPBanned(ip, "", "global")
				ipCounter++
			}
		})
	})

	b.Run("BanDomain", func(b *testing.B) {
		b.RunParallel(func(pb *testing.PB) {
			domainCounter := 0
			for pb.Next() {
				domain := fmt.Sprintf("test%d.example.com", domainCounter)
				_ = pm.BanDomain(domain, "300")
				domainCounter++
			}
		})
	})
}

// BenchmarkConfigValidation 基准测试配置验证性能
func BenchmarkConfigValidation(b *testing.B) {
	config := CreateTestConfig()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			err := common.ValidateConfig(config)
			if err != nil {
				b.Fatalf("配置验证失败: %v", err)
			}
		}
	})
}

// BenchmarkProxyPoolOperations 基准测试代理池操作性能
func BenchmarkProxyPoolOperations(b *testing.B) {
	cleanup := SetupTestEnvironment(b)
	defer cleanup()

	config := CreateTestConfig()
	config.Global.MinProxyPoolSize = 50
	config.Global.MaxProxyFetchAttempts = 5

	// 创建大量代理用于测试
	largeProxyList := make([]string, 1000)
	for i := 0; i < 1000; i++ {
		largeProxyList[i] = fmt.Sprintf("http://proxy%d.example.com:8080", i+1)
	}

	pm := proxymanager.NewProxyManager(largeProxyList)
	pm.Config = config

	b.Run("NextProxy", func(b *testing.B) {
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				proxy, err := pm.NextProxy()
				if err != nil {
					b.Fatalf("获取下一个代理失败: %v", err)
				}
				if proxy == "" {
					b.Fatal("返回空代理")
				}
			}
		})
	})

	b.Run("GetRandomProxy", func(b *testing.B) {
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				proxy, err := pm.GetRandomProxy()
				if err != nil {
					b.Fatalf("获取随机代理失败: %v", err)
				}
				if proxy == "" {
					b.Fatal("返回空代理")
				}
			}
		})
	})

	b.Run("GetProxyByQuality", func(b *testing.B) {
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				proxy, err := pm.GetProxyByQuality()
				if err != nil {
					b.Fatalf("按质量获取代理失败: %v", err)
				}
				if proxy == "" {
					b.Fatal("返回空代理")
				}
			}
		})
	})
}

// BenchmarkRetryPolicyOperations 基准测试重试策略操作性能
func BenchmarkRetryPolicyOperations(b *testing.B) {
	cleanup := SetupTestEnvironment(b)
	defer cleanup()

	config := CreateTestConfig()
	config.Global.RetryProxyReusePolicy = "cooldown"
	config.Global.RetryProxyCooldownTime = 60

	proxyService := services.NewProxyService(config, GetTestProxyList())

	b.Run("GetNextProxyWithCooldown", func(b *testing.B) {
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				proxy, err := proxyService.GetNextProxy()
				if err != nil {
					b.Fatalf("获取代理失败: %v", err)
				}
				if proxy == "" {
					b.Fatal("返回空代理")
				}
			}
		})
	})

	b.Run("MarkProxyFailed", func(b *testing.B) {
		proxies := GetTestProxyList()
		b.RunParallel(func(pb *testing.PB) {
			proxyIndex := 0
			for pb.Next() {
				proxy := proxies[proxyIndex%len(proxies)]
				proxyService.MarkProxyFailed(proxy)
				proxyIndex++
			}
		})
	})
}

// BenchmarkMemoryUsage 基准测试内存使用情况
func BenchmarkMemoryUsage(b *testing.B) {
	cleanup := SetupTestEnvironment(b)
	defer cleanup()

	config := CreateTestConfig()

	// 创建大量代理以测试内存使用
	largeProxyList := make([]string, 10000)
	for i := 0; i < 10000; i++ {
		largeProxyList[i] = fmt.Sprintf("http://proxy%d.example.com:8080", i+1)
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		pm := proxymanager.NewProxyManager(largeProxyList)
		pm.Config = config
		pm.InitBanSystem(config)

		// 执行一些操作
		for j := 0; j < 100; j++ {
			proxy, _ := pm.RotateProxy()
			if proxy != "" {
				_ = pm.BanIP(fmt.Sprintf("192.168.1.%d", j%254+1), "300", "global", "test")
			}
		}
	}
}
