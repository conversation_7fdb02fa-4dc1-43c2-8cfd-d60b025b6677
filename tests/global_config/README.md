# FlexProxy 全局配置测试套件

这个测试套件专门用于验证 FlexProxy 项目中 `global` 配置模块的所有功能和特性。

## 📁 文件结构

```
tests/global_config/
├── README.md                 # 本文档
├── global_config_test.go     # 基础单元测试
├── integration_test.go       # 集成测试套件
├── benchmark_test.go         # 性能基准测试
├── test_helper.go           # 测试辅助函数
└── run_tests.sh             # 测试运行脚本
```

## 🎯 测试覆盖范围

### 1. 基础配置测试 (`global_config_test.go`)
- ✅ 基础全局配置验证
- ✅ IP轮换模式测试 (random, sequential, quality, smart)
- ✅ DNS相关配置测试
- ✅ 重试代理复用策略测试 (allow, deny, cooldown)
- ✅ 封禁系统配置测试
- ✅ 代理池设置测试
- ✅ 排除规则配置测试
- ✅ IP版本优先级测试
- ✅ 反向DNS查找功能测试
- ✅ 智能代理模式测试
- ✅ 代理冷却机制测试
- ✅ DNS缓存功能测试
- ✅ 配置验证测试

### 2. 集成测试 (`integration_test.go`)
- ✅ 完整配置流程测试
- ✅ DNS集成功能测试
- ✅ 封禁系统集成测试
- ✅ 重试策略集成测试
- ✅ 配置验证集成测试
- ✅ 性能影响测试
- ✅ 配置重载模拟测试

### 3. 性能基准测试 (`benchmark_test.go`)
- ✅ 代理轮换性能测试
- ✅ 智能代理模式性能测试
- ✅ DNS解析性能测试
- ✅ 反向DNS查找性能测试
- ✅ 封禁系统操作性能测试
- ✅ 配置验证性能测试
- ✅ 代理池操作性能测试
- ✅ 重试策略操作性能测试
- ✅ 内存使用情况测试

## 🚀 快速开始

### 运行所有测试
```bash
cd tests/global_config
./run_tests.sh
```

### 运行特定类型的测试
```bash
# 仅运行单元测试
./run_tests.sh -u

# 仅运行集成测试
./run_tests.sh -i

# 仅运行基准测试
./run_tests.sh -b

# 仅运行覆盖率测试
./run_tests.sh -c

# 仅运行配置验证
./run_tests.sh -v
```

### 手动运行测试
```bash
# 运行单元测试
go test -v -run "TestGlobalConfig" ./tests/global_config/

# 运行集成测试
go test -v -run "TestGlobalConfigIntegration" ./tests/global_config/

# 运行基准测试
go test -bench=. -benchmem -run=^$ ./tests/global_config/

# 运行覆盖率测试
go test -coverprofile=coverage.out ./tests/global_config/
go tool cover -html=coverage.out -o coverage.html
```

## 📊 测试配置

### 测试用的全局配置示例
```yaml
global:
  enable: true
  proxy_file: "./test_proxies.txt"
  ip_rotation_mode: "smart"
  dns_lookup_mode: "custom"
  reverse_dns_lookup: "dns"
  http_proxy_dns: "*******:53"
  dns_cache_ttl: 300
  dns_no_cache: false
  ip_version_priority: "ipv4"
  default_dns_timeout: 5000
  retry_proxy_reuse_policy: "cooldown"
  retry_proxy_cooldown_time: 60
  retry_proxy_global_tracking: true
  min_proxy_pool_size: 10
  max_proxy_fetch_attempts: 3
  custom_dns_servers:
    - server: "*******:53"
      protocol: "udp"
      timeout: 5000
      priority: 1
      tags: ["cloudflare", "fast"]
  global_banned_ips:
    - ip: "*************"
      duration: 3600
  banned_domains:
    - domain: "malicious-site.com"
      duration: 86400
  blocked_ips: ["***********", "************"]
  trusted_ips: ["127.0.0.1", "::1"]
  excluded_patterns: ["*.local", "localhost:*"]
  excluded_scope: "all"
```

## 🔧 测试环境设置

测试套件会自动创建以下测试文件：

### 1. 测试代理文件 (`test_proxies.txt`)
```
http://proxy1.example.com:8080
http://proxy2.example.com:8080
http://proxy3.example.com:8080
socks5://proxy4.example.com:1080
http://user:<EMAIL>:8080
```

### 2. 测试Hosts文件 (`test_hosts.txt`)
```
******* cloudflare.com
******* google-public-dns-a.google.com
******* google-public-dns-b.google.com
************** resolver1.opendns.com
```

### 3. 测试配置文件 (`test_config.yaml`)
包含完整的测试配置，用于验证配置加载和解析功能。

## 📈 性能基准

运行基准测试可以获得以下性能指标：

- **代理轮换性能**: 每次轮换的平均耗时
- **智能模式性能**: 缓存命中率和响应时间
- **DNS解析性能**: 解析延迟和缓存效果
- **封禁系统性能**: 封禁和查询操作的吞吐量
- **内存使用情况**: 不同配置下的内存占用

## 🐛 故障排除

### 常见问题

1. **测试文件创建失败**
   - 确保有写入权限
   - 检查磁盘空间

2. **DNS解析测试失败**
   - 检查网络连接
   - 确认DNS服务器可访问

3. **代理测试失败**
   - 测试使用的是模拟代理，不需要真实连接
   - 检查代理格式是否正确

4. **基准测试结果异常**
   - 确保系统负载较低
   - 多次运行取平均值

### 调试模式

启用详细日志输出：
```bash
go test -v -run "TestGlobalConfig" ./tests/global_config/ -args -debug
```

## 📝 贡献指南

### 添加新测试

1. **单元测试**: 在 `global_config_test.go` 中添加新的测试函数
2. **集成测试**: 在 `integration_test.go` 中添加新的测试方法
3. **基准测试**: 在 `benchmark_test.go` 中添加新的基准函数

### 测试命名规范

- 单元测试: `TestGlobalConfig[功能名称]`
- 集成测试: `Test[功能名称]Integration`
- 基准测试: `Benchmark[功能名称]`

### 测试数据

使用 `test_helper.go` 中的辅助函数创建测试数据，确保测试的一致性和可重复性。

## 📄 许可证

本测试套件遵循 FlexProxy 项目的许可证。
