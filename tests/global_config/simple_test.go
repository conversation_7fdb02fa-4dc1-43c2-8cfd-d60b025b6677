package global_config_test

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestSimpleGlobalConfig 简单的全局配置测试
func TestSimpleGlobalConfig(t *testing.T) {
	// 测试基本的配置结构
	type GlobalConfig struct {
		Enable    bool   `yaml:"enable"`
		ProxyFile string `yaml:"proxy_file"`
	}

	config := GlobalConfig{
		Enable:    true,
		ProxyFile: "./test_proxies.txt",
	}

	// 验证基础配置
	assert.True(t, config.Enable, "全局启用状态应为 true")
	assert.Equal(t, "./test_proxies.txt", config.ProxyFile, "代理文件路径应正确设置")
}

// TestConfigValidation 测试配置验证
func TestConfigValidation(t *testing.T) {
	testCases := []struct {
		name        string
		enable      bool
		proxyFile   string
		expectValid bool
	}{
		{"有效配置", true, "./test_proxies.txt", true},
		{"禁用配置", false, "", true},
		{"空文件路径", true, "", false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 简单的验证逻辑
			isValid := tc.enable == false || (tc.enable == true && tc.proxyFile != "")
			assert.Equal(t, tc.expectValid, isValid, "配置验证结果应正确")
		})
	}
}

// TestIPRotationModes 测试IP轮换模式
func TestIPRotationModes(t *testing.T) {
	validModes := []string{"random", "sequential", "quality", "smart"}
	
	for _, mode := range validModes {
		t.Run("模式_"+mode, func(t *testing.T) {
			// 验证模式名称
			assert.Contains(t, validModes, mode, "应为有效的轮换模式")
			assert.NotEmpty(t, mode, "模式名称不应为空")
		})
	}
}

// TestRetryPolicies 测试重试策略
func TestRetryPolicies(t *testing.T) {
	validPolicies := []string{"allow", "deny", "cooldown"}
	
	for _, policy := range validPolicies {
		t.Run("策略_"+policy, func(t *testing.T) {
			// 验证策略名称
			assert.Contains(t, validPolicies, policy, "应为有效的重试策略")
			assert.NotEmpty(t, policy, "策略名称不应为空")
		})
	}
}

// TestDNSModes 测试DNS模式
func TestDNSModes(t *testing.T) {
	validModes := []string{"system", "custom", "hybrid"}
	
	for _, mode := range validModes {
		t.Run("DNS模式_"+mode, func(t *testing.T) {
			// 验证DNS模式
			assert.Contains(t, validModes, mode, "应为有效的DNS模式")
			assert.NotEmpty(t, mode, "DNS模式名称不应为空")
		})
	}
}

// TestReverseDNSModes 测试反向DNS模式
func TestReverseDNSModes(t *testing.T) {
	validModes := []string{"no", "dns", "file", "values"}
	
	for _, mode := range validModes {
		t.Run("反向DNS模式_"+mode, func(t *testing.T) {
			// 验证反向DNS模式
			assert.Contains(t, validModes, mode, "应为有效的反向DNS模式")
			assert.NotEmpty(t, mode, "反向DNS模式名称不应为空")
		})
	}
}

// TestIPVersionPriority 测试IP版本优先级
func TestIPVersionPriority(t *testing.T) {
	validPriorities := []string{"ipv4", "ipv6", "dual"}
	
	for _, priority := range validPriorities {
		t.Run("IP版本_"+priority, func(t *testing.T) {
			// 验证IP版本优先级
			assert.Contains(t, validPriorities, priority, "应为有效的IP版本优先级")
			assert.NotEmpty(t, priority, "IP版本优先级不应为空")
		})
	}
}

// TestConfigDefaults 测试配置默认值
func TestConfigDefaults(t *testing.T) {
	// 定义默认配置
	defaults := map[string]interface{}{
		"enable":                     true,
		"dns_cache_ttl":             300,
		"default_dns_timeout":       5000,
		"min_proxy_pool_size":       10,
		"max_proxy_fetch_attempts":  3,
		"retry_proxy_cooldown_time": 60,
		"ip_rotation_mode":          "random",
		"retry_proxy_reuse_policy":  "allow",
		"dns_lookup_mode":           "system",
		"reverse_dns_lookup":        "no",
		"ip_version_priority":       "ipv4",
	}

	// 验证默认值
	for key, expectedValue := range defaults {
		t.Run("默认值_"+key, func(t *testing.T) {
			assert.NotNil(t, expectedValue, "默认值不应为nil")
			
			// 根据类型进行验证
			switch v := expectedValue.(type) {
			case bool:
				assert.IsType(t, true, v, "布尔类型默认值")
			case int:
				assert.Greater(t, v, -1, "整数类型默认值应为非负数")
			case string:
				assert.NotEmpty(t, v, "字符串类型默认值不应为空")
			}
		})
	}
}

// TestConfigRanges 测试配置范围验证
func TestConfigRanges(t *testing.T) {
	testCases := []struct {
		name     string
		field    string
		value    int
		minValue int
		maxValue int
		valid    bool
	}{
		{"DNS缓存TTL最小值", "dns_cache_ttl", 60, 60, 86400, true},
		{"DNS缓存TTL最大值", "dns_cache_ttl", 86400, 60, 86400, true},
		{"DNS缓存TTL过小", "dns_cache_ttl", 30, 60, 86400, false},
		{"DNS超时最小值", "default_dns_timeout", 1000, 1000, 30000, true},
		{"DNS超时最大值", "default_dns_timeout", 30000, 1000, 30000, true},
		{"DNS超时过大", "default_dns_timeout", 60000, 1000, 30000, false},
		{"代理池大小最小值", "min_proxy_pool_size", 1, 1, 1000, true},
		{"代理池大小最大值", "min_proxy_pool_size", 1000, 1, 1000, true},
		{"代理池大小为零", "min_proxy_pool_size", 0, 1, 1000, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 验证值是否在有效范围内
			isValid := tc.value >= tc.minValue && tc.value <= tc.maxValue
			assert.Equal(t, tc.valid, isValid, "范围验证结果应正确")
		})
	}
}

// TestProxyFileFormats 测试代理文件格式
func TestProxyFileFormats(t *testing.T) {
	validProxyFormats := []string{
		"http://proxy.example.com:8080",
		"https://proxy.example.com:8443",
		"socks5://proxy.example.com:1080",
		"socks4://proxy.example.com:1080",
		"http://user:<EMAIL>:8080",
		"socks5://user:<EMAIL>:1080",
	}

	for i, proxy := range validProxyFormats {
		t.Run("代理格式_"+string(rune(i+1)), func(t *testing.T) {
			// 简单的格式验证
			assert.NotEmpty(t, proxy, "代理URL不应为空")
			assert.Contains(t, proxy, "://", "代理URL应包含协议")
			assert.Contains(t, proxy, ":", "代理URL应包含端口")
		})
	}
}
