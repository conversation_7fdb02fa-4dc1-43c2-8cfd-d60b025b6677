package global_config_test

import (
	"context"
	"fmt"
	"net"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/internal/proxymanager"
	"github.com/flexp/flexp/internal/server"
	"github.com/flexp/flexp/internal/services"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestGlobalConfigBasic 测试基础全局配置
func TestGlobalConfigBasic(t *testing.T) {
	// 创建测试配置
	config := &common.Config{
		Global: common.GlobalConfig{
			Enable:    true,
			ProxyFile: "./test_proxies.txt",
		},
	}

	// 验证基础配置
	assert.True(t, config.Global.Enable, "全局启用状态应为 true")
	assert.Equal(t, "./test_proxies.txt", config.Global.ProxyFile, "代理文件路径应正确设置")
}

// TestGlobalConfigIPRotationModes 测试IP轮换模式
func TestGlobalConfigIPRotationModes(t *testing.T) {
	testCases := []struct {
		name string
		mode string
	}{
		{"随机模式", "random"},
		{"顺序模式", "sequential"},
		{"质量模式", "quality"},
		{"智能模式", "smart"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			config := &common.Config{
				Global: common.GlobalConfig{
					Enable:         true,
					ProxyFile:      "./test_proxies.txt",
					IPRotationMode: tc.mode,
				},
			}

			// 创建代理管理器
			pm := proxymanager.NewProxyManager([]string{
				"http://proxy1:8080",
				"http://proxy2:8080",
				"http://proxy3:8080",
			})
			pm.Config = config

			// 测试代理轮换
			proxy, err := pm.RotateProxy()
			assert.NoError(t, err, "代理轮换不应出错")
			assert.NotEmpty(t, proxy, "应返回有效代理")

			t.Logf("模式 %s 返回代理: %s", tc.mode, proxy)
		})
	}
}

// TestGlobalConfigDNSSettings 测试DNS相关配置
func TestGlobalConfigDNSSettings(t *testing.T) {
	config := &common.Config{
		Global: common.GlobalConfig{
			Enable:            true,
			ProxyFile:         "./test_proxies.txt",
			DNSLookupMode:     "custom",
			ReverseDNSLookup:  "dns",
			HTTPProxyDNS:      "*******:53",
			DNSCacheTTL:       300,
			DNSNoCache:        false,
			DefaultDNSTimeout: 5000,
			CustomDNSServers: []common.CustomDNSServer{
				{
					Server:   "*******:53",
					Protocol: "udp",
					Timeout:  5000,
					Priority: 1,
				},
			},
		},
	}

	// 验证DNS配置
	assert.Equal(t, "custom", config.Global.DNSLookupMode, "DNS查找模式应为 custom")
	assert.Equal(t, "dns", config.Global.ReverseDNSLookup, "反向DNS查找应为 dns")
	assert.Equal(t, "*******:53", config.Global.HTTPProxyDNS, "HTTP代理DNS应正确设置")
	assert.Equal(t, 300, config.Global.DNSCacheTTL, "DNS缓存TTL应为300秒")
	assert.False(t, config.Global.DNSNoCache, "DNS缓存不应被禁用")
	assert.Equal(t, 5000, config.Global.DefaultDNSTimeout, "默认DNS超时应为5000毫秒")
	assert.Len(t, config.Global.CustomDNSServers, 1, "应有1个自定义DNS服务器")
}

// TestGlobalConfigRetryPolicy 测试重试代理复用策略
func TestGlobalConfigRetryPolicy(t *testing.T) {
	testCases := []struct {
		name         string
		policy       string
		cooldownTime int
		globalTrack  bool
	}{
		{"允许复用", "allow", 0, false},
		{"禁止复用", "deny", 0, false},
		{"冷却复用", "cooldown", 60, true},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			config := &common.Config{
				Global: common.GlobalConfig{
					Enable:                   true,
					ProxyFile:                "./test_proxies.txt",
					RetryProxyReusePolicy:    tc.policy,
					RetryProxyCooldownTime:   tc.cooldownTime,
					RetryProxyGlobalTracking: tc.globalTrack,
				},
			}

			// 验证重试策略配置
			assert.Equal(t, tc.policy, config.Global.RetryProxyReusePolicy, "重试代理复用策略应正确")
			assert.Equal(t, tc.cooldownTime, config.Global.RetryProxyCooldownTime, "重试代理冷却时间应正确")
			assert.Equal(t, tc.globalTrack, config.Global.RetryProxyGlobalTracking, "全局跟踪设置应正确")

			t.Logf("策略: %s, 冷却时间: %d秒, 全局跟踪: %v", tc.policy, tc.cooldownTime, tc.globalTrack)
		})
	}
}

// TestGlobalConfigBanSystem 测试封禁系统配置
func TestGlobalConfigBanSystem(t *testing.T) {
	config := &common.Config{
		Global: common.GlobalConfig{
			Enable:    true,
			ProxyFile: "./test_proxies.txt",
			GlobalBannedIPs: []common.BanIPConfig{
				{IP: "*************", Duration: 3600},
				{IP: "*********", Duration: "1h"},
				{IP: "***********", Duration: "reboot"},
			},
			BannedDomains: []common.BannedDomainConfig{
				{Domain: "malicious-site.com", Duration: 86400},
				{Domain: "spam-domain.net", Duration: "24h"},
			},
			BlockedIPs: []string{"***********", "************"},
			TrustedIPs: []string{"127.0.0.1", "::1"},
		},
	}

	// 验证封禁配置
	assert.Len(t, config.Global.GlobalBannedIPs, 3, "应有3个全局封禁IP")
	assert.Len(t, config.Global.BannedDomains, 2, "应有2个封禁域名")
	assert.Len(t, config.Global.BlockedIPs, 2, "应有2个阻止IP")
	assert.Len(t, config.Global.TrustedIPs, 2, "应有2个信任IP")

	// 创建代理管理器并初始化封禁系统
	pm := proxymanager.NewProxyManager([]string{"http://proxy1:8080"})
	pm.InitBanSystem(config)

	// 验证封禁系统是否正确初始化
	assert.True(t, pm.IsBanSystemInitialized(), "封禁系统应已初始化")
}

// TestGlobalConfigProxyPoolSettings 测试代理池相关配置
func TestGlobalConfigProxyPoolSettings(t *testing.T) {
	config := &common.Config{
		Global: common.GlobalConfig{
			Enable:                true,
			ProxyFile:             "./test_proxies.txt",
			MinProxyPoolSize:      10,
			MaxProxyFetchAttempts: 3,
		},
	}

	// 验证代理池配置
	assert.Equal(t, 10, config.Global.MinProxyPoolSize, "最小代理池大小应为10")
	assert.Equal(t, 3, config.Global.MaxProxyFetchAttempts, "最大代理获取尝试次数应为3")

	// 创建代理管理器测试配置应用
	pm := proxymanager.NewProxyManager([]string{
		"http://proxy1:8080",
		"http://proxy2:8080",
	})
	pm.Config = config

	// 测试最大尝试次数限制
	proxy, err := pm.NextProxy()
	assert.NoError(t, err, "获取代理不应出错")
	assert.NotEmpty(t, proxy, "应返回有效代理")
}

// TestGlobalConfigExclusionRules 测试排除规则配置
func TestGlobalConfigExclusionRules(t *testing.T) {
	config := &common.Config{
		Global: common.GlobalConfig{
			Enable: true,
			ExcludedPatterns: []string{
				"*.local",
				"localhost:*",
				"127.0.0.1:*",
				"*.internal.company.com",
			},
			ExcludedScope: "all",
		},
	}

	// 验证排除规则配置
	assert.Len(t, config.Global.ExcludedPatterns, 4, "应有4个排除模式")
	assert.Equal(t, "all", config.Global.ExcludedScope, "排除范围应为 all")

	// 验证排除模式内容
	expectedPatterns := []string{"*.local", "localhost:*", "127.0.0.1:*", "*.internal.company.com"}
	for i, pattern := range expectedPatterns {
		assert.Equal(t, pattern, config.Global.ExcludedPatterns[i], fmt.Sprintf("排除模式 %d 应正确", i))
	}
}

// TestGlobalConfigIPVersionPriority 测试IP版本优先级配置
func TestGlobalConfigIPVersionPriority(t *testing.T) {
	testCases := []struct {
		name     string
		priority string
	}{
		{"IPv4优先", "ipv4"},
		{"IPv6优先", "ipv6"},
		{"双栈支持", "dual"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			config := &common.Config{
				Global: common.GlobalConfig{
					Enable:            true,
					ProxyFile:         "./test_proxies.txt",
					IPVersionPriority: tc.priority,
				},
			}

			assert.Equal(t, tc.priority, config.Global.IPVersionPriority, "IP版本优先级应正确设置")
			t.Logf("IP版本优先级: %s", tc.priority)
		})
	}
}

// TestGlobalConfigReverseDNSLookup 测试反向DNS查找功能
func TestGlobalConfigReverseDNSLookup(t *testing.T) {
	testCases := []struct {
		name   string
		mode   string
		config string
	}{
		{"禁用反向DNS", "no", ""},
		{"使用系统DNS", "dns", ""},
		{"从文件加载", "file", "file:./test_hosts.txt"},
		{"直接映射", "values", "******* cloudflare.com, ******* google.com"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 为文件模式创建测试文件
			if tc.mode == "file" {
				testFile := "./test_hosts.txt"
				content := "******* cloudflare.com\n******* google.com\n"
				err := os.WriteFile(testFile, []byte(content), 0644)
				require.NoError(t, err, "创建测试hosts文件应成功")
				defer os.Remove(testFile)
			}

			// 创建反向DNS解析器
			resolver, err := server.NewReverseDNSResolver(tc.mode, tc.config)
			if tc.mode == "file" && tc.config == "file:./test_hosts.txt" {
				assert.NoError(t, err, "创建反向DNS解析器应成功")
				assert.NotNil(t, resolver, "反向DNS解析器不应为空")
			} else if tc.mode != "file" {
				assert.NoError(t, err, "创建反向DNS解析器应成功")
				assert.NotNil(t, resolver, "反向DNS解析器不应为空")
			}

			t.Logf("反向DNS模式: %s, 配置: %s", tc.mode, tc.config)
		})
	}
}

// TestGlobalConfigSmartProxyMode 测试智能代理模式的具体行为
func TestGlobalConfigSmartProxyMode(t *testing.T) {
	config := &common.Config{
		Global: common.GlobalConfig{
			Enable:         true,
			ProxyFile:      "./test_proxies.txt",
			IPRotationMode: "smart",
		},
	}

	// 创建代理管理器
	pm := proxymanager.NewProxyManager([]string{
		"http://proxy1:8080",
		"http://proxy2:8080",
		"http://proxy3:8080",
	})
	pm.Config = config

	// 测试智能模式的缓存行为
	proxy1, err := pm.GetProxyForDomain("smart", "example.com")
	assert.NoError(t, err, "第一次获取代理应成功")
	assert.NotEmpty(t, proxy1, "应返回有效代理")

	// 短时间内再次获取，应返回相同代理（缓存行为）
	proxy2, err := pm.GetProxyForDomain("smart", "example.com")
	assert.NoError(t, err, "第二次获取代理应成功")
	assert.Equal(t, proxy1, proxy2, "智能模式应缓存代理")

	t.Logf("智能模式缓存测试: 第一次=%s, 第二次=%s", proxy1, proxy2)
}

// TestGlobalConfigProxyCooldown 测试代理冷却机制
func TestGlobalConfigProxyCooldown(t *testing.T) {
	config := &common.Config{
		Global: common.GlobalConfig{
			Enable:                   true,
			ProxyFile:                "./test_proxies.txt",
			RetryProxyReusePolicy:    "cooldown",
			RetryProxyCooldownTime:   2, // 2秒冷却时间
			RetryProxyGlobalTracking: true,
		},
	}

	// 创建代理服务
	proxyService := services.NewProxyService(config, []string{
		"http://proxy1:8080",
		"http://proxy2:8080",
	})

	// 获取代理
	proxy1, err := proxyService.GetNextProxy()
	assert.NoError(t, err, "获取代理应成功")
	assert.NotEmpty(t, proxy1, "应返回有效代理")

	// 标记代理失败
	proxyService.MarkProxyFailed(proxy1)

	// 立即再次获取，应返回不同代理（因为第一个在冷却期）
	proxy2, err := proxyService.GetNextProxy()
	assert.NoError(t, err, "获取代理应成功")
	assert.NotEqual(t, proxy1, proxy2, "冷却期内应返回不同代理")

	// 等待冷却时间过去
	time.Sleep(3 * time.Second)

	// 现在第一个代理应该可用了
	proxy3, err := proxyService.GetNextProxy()
	assert.NoError(t, err, "冷却后获取代理应成功")

	t.Logf("冷却测试: 原代理=%s, 冷却期代理=%s, 冷却后代理=%s", proxy1, proxy2, proxy3)
}

// TestGlobalConfigDNSCaching 测试DNS缓存功能
func TestGlobalConfigDNSCaching(t *testing.T) {
	config := &common.Config{
		Global: common.GlobalConfig{
			Enable:      true,
			ProxyFile:   "./test_proxies.txt",
			DNSCacheTTL: 300,
			DNSNoCache:  false,
		},
	}

	// 创建DNS服务
	dnsService := services.NewDNSService(config)

	// 测试DNS解析和缓存
	ctx := context.Background()
	ips1, err := dnsService.Resolve(ctx, "example.com")
	if err == nil && len(ips1) > 0 {
		// 再次解析相同域名，应从缓存返回
		start := time.Now()
		ips2, err := dnsService.Resolve(ctx, "example.com")
		duration := time.Since(start)

		assert.NoError(t, err, "缓存DNS解析应成功")
		assert.Equal(t, ips1, ips2, "缓存应返回相同结果")
		assert.Less(t, duration, 10*time.Millisecond, "缓存查询应很快")

		t.Logf("DNS缓存测试: 第一次解析=%v, 缓存查询耗时=%v", ips1, duration)
	} else {
		t.Skip("跳过DNS缓存测试，因为无法解析example.com")
	}
}

// TestGlobalConfigValidation 测试全局配置验证
func TestGlobalConfigValidation(t *testing.T) {
	testCases := []struct {
		name        string
		config      common.GlobalConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "有效配置",
			config: common.GlobalConfig{
				Enable:                true,
				ProxyFile:             "./test_proxies.txt",
				IPRotationMode:        "smart",
				RetryProxyReusePolicy: "cooldown",
				DNSCacheTTL:           300,
				DefaultDNSTimeout:     5000,
			},
			expectError: false,
		},
		{
			name: "无效IP轮换模式",
			config: common.GlobalConfig{
				Enable:         true,
				ProxyFile:      "./test_proxies.txt",
				IPRotationMode: "invalid_mode",
			},
			expectError: true,
			errorMsg:    "IP轮换模式无效",
		},
		{
			name: "无效重试策略",
			config: common.GlobalConfig{
				Enable:                true,
				ProxyFile:             "./test_proxies.txt",
				RetryProxyReusePolicy: "invalid_policy",
			},
			expectError: true,
			errorMsg:    "重试策略无效",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			config := &common.Config{Global: tc.config}

			// 验证配置
			err := common.ValidateConfig(config)

			if tc.expectError {
				assert.Error(t, err, "应该产生验证错误")
				t.Logf("预期错误: %v", err)
			} else {
				assert.NoError(t, err, "不应该产生验证错误")
			}
		})
	}
}
