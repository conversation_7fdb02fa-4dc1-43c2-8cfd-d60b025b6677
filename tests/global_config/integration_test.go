package global_config_test

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/internal/proxymanager"
	"github.com/flexp/flexp/internal/server"
	"github.com/flexp/flexp/internal/services"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// GlobalConfigIntegrationTestSuite 全局配置集成测试套件
type GlobalConfigIntegrationTestSuite struct {
	suite.Suite
	config     *common.Config
	cleanup    func()
	configPath string
}

// SetupSuite 设置测试套件
func (suite *GlobalConfigIntegrationTestSuite) SetupSuite() {
	// 设置测试环境
	suite.cleanup = SetupTestEnvironment(suite.T())
	
	// 创建完整的测试配置
	suite.config = CreateTestConfig()
	
	// 创建测试配置文件
	suite.configPath = CreateTestConfigFile(suite.T(), suite.config)
}

// TearDownSuite 清理测试套件
func (suite *GlobalConfigIntegrationTestSuite) TearDownSuite() {
	if suite.cleanup != nil {
		suite.cleanup()
	}
	CleanupTestConfigFile(suite.configPath)
}

// TestCompleteGlobalConfigFlow 测试完整的全局配置流程
func (suite *GlobalConfigIntegrationTestSuite) TestCompleteGlobalConfigFlow() {
	t := suite.T()
	
	// 1. 验证配置加载
	loadedConfig, err := common.LoadConfigFromYAML(suite.configPath)
	assert.NoError(t, err, "配置加载应成功")
	assert.NotNil(t, loadedConfig, "加载的配置不应为空")
	assert.True(t, loadedConfig.Global.Enable, "全局启用状态应为true")
	
	// 2. 创建代理管理器并初始化
	proxyList := GetTestProxyList()
	pm := proxymanager.NewProxyManager(proxyList)
	pm.Config = suite.config
	
	// 初始化封禁系统
	pm.InitBanSystem(suite.config)
	assert.True(t, pm.IsBanSystemInitialized(), "封禁系统应已初始化")
	
	// 3. 测试代理轮换功能
	proxy1, err := pm.RotateProxy()
	assert.NoError(t, err, "代理轮换应成功")
	assert.NotEmpty(t, proxy1, "应返回有效代理")
	assert.True(t, ValidateTestProxy(proxy1), "返回的代理格式应有效")
	
	// 4. 测试智能模式缓存
	proxy2, err := pm.GetProxyForDomain("smart", "example.com")
	assert.NoError(t, err, "智能模式获取代理应成功")
	
	// 短时间内再次获取应返回缓存的代理
	proxy3, err := pm.GetProxyForDomain("smart", "example.com")
	assert.NoError(t, err, "缓存获取应成功")
	
	t.Logf("代理轮换测试: 轮换=%s, 智能1=%s, 智能2=%s", proxy1, proxy2, proxy3)
}

// TestDNSIntegration 测试DNS集成功能
func (suite *GlobalConfigIntegrationTestSuite) TestDNSIntegration() {
	t := suite.T()
	
	// 创建DNS服务
	dnsService := services.NewDNSService(suite.config)
	assert.NotNil(t, dnsService, "DNS服务应创建成功")
	
	// 测试反向DNS解析器
	resolver, err := server.NewReverseDNSResolver("dns", "")
	assert.NoError(t, err, "创建反向DNS解析器应成功")
	assert.NotNil(t, resolver, "反向DNS解析器不应为空")
	
	// 测试自定义DNS服务器配置
	assert.Len(t, suite.config.Global.CustomDNSServers, 3, "应有3个自定义DNS服务器")
	
	firstDNS := suite.config.Global.CustomDNSServers[0]
	assert.Equal(t, "1.1.1.1:53", firstDNS.Server, "第一个DNS服务器应为Cloudflare")
	assert.Equal(t, "udp", firstDNS.Protocol, "协议应为UDP")
	assert.Equal(t, 1, firstDNS.Priority, "优先级应为1")
	
	t.Logf("DNS配置测试: 服务器数量=%d, 第一个服务器=%s", 
		len(suite.config.Global.CustomDNSServers), firstDNS.Server)
}

// TestBanSystemIntegration 测试封禁系统集成
func (suite *GlobalConfigIntegrationTestSuite) TestBanSystemIntegration() {
	t := suite.T()
	
	// 创建代理管理器
	pm := proxymanager.NewProxyManager(GetTestProxyList())
	pm.Config = suite.config
	pm.InitBanSystem(suite.config)
	
	// 验证全局封禁IP配置
	assert.Len(t, suite.config.Global.GlobalBannedIPs, 3, "应有3个全局封禁IP")
	assert.Len(t, suite.config.Global.BannedDomains, 3, "应有3个封禁域名")
	assert.Len(t, suite.config.Global.BlockedIPs, 3, "应有3个阻止IP")
	assert.Len(t, suite.config.Global.TrustedIPs, 4, "应有4个信任IP")
	
	// 测试IP封禁功能
	testIP := "***************"
	err := pm.BanIP(testIP, "300", "global", "test")
	assert.NoError(t, err, "封禁IP应成功")
	
	// 验证IP是否被封禁
	isBanned := pm.IsIPBanned(testIP, "", "global")
	assert.True(t, isBanned, "IP应被标记为封禁")
	
	// 测试域名封禁功能
	testDomain := "test-malicious.com"
	err = pm.BanDomain(testDomain, "600")
	assert.NoError(t, err, "封禁域名应成功")
	
	t.Logf("封禁系统测试: IP封禁=%v, 域名封禁完成", isBanned)
}

// TestRetryPolicyIntegration 测试重试策略集成
func (suite *GlobalConfigIntegrationTestSuite) TestRetryPolicyIntegration() {
	t := suite.T()
	
	// 验证重试策略配置
	assert.Equal(t, "cooldown", suite.config.Global.RetryProxyReusePolicy, "重试策略应为cooldown")
	assert.Equal(t, 60, suite.config.Global.RetryProxyCooldownTime, "冷却时间应为60秒")
	assert.True(t, suite.config.Global.RetryProxyGlobalTracking, "全局跟踪应启用")
	
	// 创建代理服务测试冷却机制
	proxyService := services.NewProxyService(suite.config, GetTestProxyList())
	
	// 获取代理
	proxy1, err := proxyService.GetNextProxy()
	assert.NoError(t, err, "获取代理应成功")
	assert.NotEmpty(t, proxy1, "应返回有效代理")
	
	// 标记代理失败
	proxyService.MarkProxyFailed(proxy1)
	
	// 立即再次获取，应返回不同代理
	proxy2, err := proxyService.GetNextProxy()
	assert.NoError(t, err, "获取代理应成功")
	
	// 如果有多个代理，应该返回不同的代理
	if len(GetTestProxyList()) > 1 {
		assert.NotEqual(t, proxy1, proxy2, "冷却期内应返回不同代理")
	}
	
	t.Logf("重试策略测试: 原代理=%s, 冷却期代理=%s", proxy1, proxy2)
}

// TestConfigValidationIntegration 测试配置验证集成
func (suite *GlobalConfigIntegrationTestSuite) TestConfigValidationIntegration() {
	t := suite.T()
	
	// 测试有效配置验证
	err := common.ValidateConfig(suite.config)
	assert.NoError(t, err, "有效配置应通过验证")
	
	// 测试无效配置
	invalidConfig := CreateTestConfig()
	invalidConfig.Global.IPRotationMode = "invalid_mode"
	
	err = common.ValidateConfig(invalidConfig)
	assert.Error(t, err, "无效配置应产生验证错误")
	
	// 测试缺少必需字段
	incompleteConfig := &common.Config{
		Global: common.GlobalConfig{
			Enable: true,
			// 缺少 ProxyFile
		},
	}
	
	err = common.ValidateConfig(incompleteConfig)
	assert.Error(t, err, "不完整配置应产生验证错误")
	
	t.Logf("配置验证测试: 有效配置通过，无效配置被拒绝")
}

// TestPerformanceWithGlobalConfig 测试全局配置的性能影响
func (suite *GlobalConfigIntegrationTestSuite) TestPerformanceWithGlobalConfig() {
	t := suite.T()
	
	// 创建代理管理器
	pm := proxymanager.NewProxyManager(GetTestProxyList())
	pm.Config = suite.config
	pm.InitBanSystem(suite.config)
	
	// 性能测试：大量代理轮换
	iterations := 1000
	start := time.Now()
	
	for i := 0; i < iterations; i++ {
		proxy, err := pm.RotateProxy()
		assert.NoError(t, err, "代理轮换不应出错")
		assert.NotEmpty(t, proxy, "应返回有效代理")
	}
	
	duration := time.Since(start)
	avgTime := duration / time.Duration(iterations)
	
	// 验证性能指标
	assert.Less(t, avgTime, 1*time.Millisecond, "平均代理轮换时间应小于1毫秒")
	
	t.Logf("性能测试: %d次轮换耗时=%v, 平均=%v", iterations, duration, avgTime)
}

// TestConfigReloadSimulation 测试配置重载模拟
func (suite *GlobalConfigIntegrationTestSuite) TestConfigReloadSimulation() {
	t := suite.T()
	
	// 创建初始配置
	pm := proxymanager.NewProxyManager(GetTestProxyList())
	pm.Config = suite.config
	pm.InitBanSystem(suite.config)
	
	// 模拟配置更改
	newConfig := CreateTestConfig()
	newConfig.Global.IPRotationMode = "random"
	newConfig.Global.RetryProxyCooldownTime = 30
	
	// 重置封禁系统以模拟重载
	pm.ResetBanSystem()
	assert.False(t, pm.IsBanSystemInitialized(), "重置后封禁系统应未初始化")
	
	// 应用新配置
	pm.Config = newConfig
	pm.InitBanSystem(newConfig)
	assert.True(t, pm.IsBanSystemInitialized(), "重新初始化后封禁系统应已初始化")
	
	// 验证新配置生效
	assert.Equal(t, "random", pm.Config.Global.IPRotationMode, "新的IP轮换模式应生效")
	assert.Equal(t, 30, pm.Config.Global.RetryProxyCooldownTime, "新的冷却时间应生效")
	
	t.Logf("配置重载测试: 新模式=%s, 新冷却时间=%d", 
		pm.Config.Global.IPRotationMode, pm.Config.Global.RetryProxyCooldownTime)
}

// TestGlobalConfigIntegration 运行全局配置集成测试套件
func TestGlobalConfigIntegration(t *testing.T) {
	suite.Run(t, new(GlobalConfigIntegrationTestSuite))
}
