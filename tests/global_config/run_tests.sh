#!/bin/bash

# FlexProxy 全局配置测试运行脚本
# 用于运行所有全局配置相关的测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo
    print_message $BLUE "================================"
    print_message $BLUE "$1"
    print_message $BLUE "================================"
    echo
}

# 检查Go环境
check_go_environment() {
    print_title "检查Go环境"
    
    if ! command -v go &> /dev/null; then
        print_message $RED "错误: 未找到Go命令，请确保Go已正确安装"
        exit 1
    fi
    
    go_version=$(go version)
    print_message $GREEN "Go版本: $go_version"
    
    # 检查是否在正确的目录
    if [ ! -f "../../go.mod" ]; then
        print_message $RED "错误: 请在FlexProxy项目根目录下运行此脚本"
        exit 1
    fi
    
    print_message $GREEN "环境检查通过"
}

# 运行单元测试
run_unit_tests() {
    print_title "运行全局配置单元测试"
    
    print_message $YELLOW "运行基础配置测试..."
    go test -v -run "TestGlobalConfig" ./tests/global_config/ || {
        print_message $RED "单元测试失败"
        return 1
    }
    
    print_message $GREEN "单元测试通过"
}

# 运行集成测试
run_integration_tests() {
    print_title "运行全局配置集成测试"
    
    print_message $YELLOW "运行集成测试套件..."
    go test -v -run "TestGlobalConfigIntegration" ./tests/global_config/ || {
        print_message $RED "集成测试失败"
        return 1
    }
    
    print_message $GREEN "集成测试通过"
}

# 运行基准测试
run_benchmark_tests() {
    print_title "运行性能基准测试"
    
    print_message $YELLOW "运行基准测试..."
    go test -bench=. -benchmem -run=^$ ./tests/global_config/ || {
        print_message $RED "基准测试失败"
        return 1
    }
    
    print_message $GREEN "基准测试完成"
}

# 运行覆盖率测试
run_coverage_tests() {
    print_title "运行测试覆盖率分析"
    
    print_message $YELLOW "生成覆盖率报告..."
    go test -coverprofile=coverage.out ./tests/global_config/ || {
        print_message $RED "覆盖率测试失败"
        return 1
    }
    
    # 显示覆盖率
    coverage=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
    print_message $GREEN "测试覆盖率: $coverage"
    
    # 生成HTML报告
    if command -v go &> /dev/null; then
        go tool cover -html=coverage.out -o coverage.html
        print_message $GREEN "HTML覆盖率报告已生成: coverage.html"
    fi
}

# 运行配置验证测试
run_config_validation() {
    print_title "运行配置文件验证"
    
    print_message $YELLOW "验证主配置文件..."
    if [ -f "../../config.yaml" ]; then
        go run ../../tools/validate_config.go ../../config.yaml || {
            print_message $RED "主配置文件验证失败"
            return 1
        }
        print_message $GREEN "主配置文件验证通过"
    else
        print_message $YELLOW "警告: 未找到主配置文件 config.yaml"
    fi
    
    print_message $YELLOW "运行配置诊断..."
    if [ -f "../../tools/config_doctor.go" ]; then
        go run ../../tools/config_doctor.go ../../config.yaml || {
            print_message $YELLOW "配置诊断发现一些问题，请检查输出"
        }
    fi
}

# 清理测试文件
cleanup_test_files() {
    print_title "清理测试文件"
    
    print_message $YELLOW "清理临时文件..."
    rm -f coverage.out coverage.html
    rm -f test_*.txt test_*.yaml
    
    print_message $GREEN "清理完成"
}

# 显示帮助信息
show_help() {
    echo "FlexProxy 全局配置测试运行脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -u, --unit          仅运行单元测试"
    echo "  -i, --integration   仅运行集成测试"
    echo "  -b, --benchmark     仅运行基准测试"
    echo "  -c, --coverage      仅运行覆盖率测试"
    echo "  -v, --validate      仅运行配置验证"
    echo "  -a, --all           运行所有测试 (默认)"
    echo "  --no-cleanup        不清理测试文件"
    echo
    echo "示例:"
    echo "  $0                  # 运行所有测试"
    echo "  $0 -u               # 仅运行单元测试"
    echo "  $0 -b               # 仅运行基准测试"
    echo "  $0 -c               # 仅运行覆盖率测试"
}

# 主函数
main() {
    local run_unit=false
    local run_integration=false
    local run_benchmark=false
    local run_coverage=false
    local run_validation=false
    local run_all=true
    local cleanup=true
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -u|--unit)
                run_unit=true
                run_all=false
                ;;
            -i|--integration)
                run_integration=true
                run_all=false
                ;;
            -b|--benchmark)
                run_benchmark=true
                run_all=false
                ;;
            -c|--coverage)
                run_coverage=true
                run_all=false
                ;;
            -v|--validate)
                run_validation=true
                run_all=false
                ;;
            -a|--all)
                run_all=true
                ;;
            --no-cleanup)
                cleanup=false
                ;;
            *)
                print_message $RED "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
        shift
    done
    
    # 检查环境
    check_go_environment
    
    # 运行测试
    local test_failed=false
    
    if [ "$run_all" = true ] || [ "$run_validation" = true ]; then
        run_config_validation || test_failed=true
    fi
    
    if [ "$run_all" = true ] || [ "$run_unit" = true ]; then
        run_unit_tests || test_failed=true
    fi
    
    if [ "$run_all" = true ] || [ "$run_integration" = true ]; then
        run_integration_tests || test_failed=true
    fi
    
    if [ "$run_all" = true ] || [ "$run_coverage" = true ]; then
        run_coverage_tests || test_failed=true
    fi
    
    if [ "$run_benchmark" = true ]; then
        run_benchmark_tests || test_failed=true
    fi
    
    # 清理
    if [ "$cleanup" = true ]; then
        cleanup_test_files
    fi
    
    # 显示最终结果
    print_title "测试结果"
    if [ "$test_failed" = true ]; then
        print_message $RED "❌ 部分测试失败，请检查上面的错误信息"
        exit 1
    else
        print_message $GREEN "✅ 所有测试通过！"
        exit 0
    fi
}

# 运行主函数
main "$@"
