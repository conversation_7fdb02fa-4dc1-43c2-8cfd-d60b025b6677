package global_config_test

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/flexp/flexp/common"
	"github.com/stretchr/testify/require"
)

// SetupTestEnvironment 设置测试环境
func SetupTestEnvironment(t *testing.T) func() {
	// 创建测试代理文件
	proxyContent := `http://proxy1.example.com:8080
http://proxy2.example.com:8080
http://proxy3.example.com:8080
socks5://proxy4.example.com:1080
http://user:<EMAIL>:8080
`
	err := os.WriteFile("./test_proxies.txt", []byte(proxyContent), 0644)
	require.NoError(t, err, "创建测试代理文件应成功")

	// 创建测试hosts文件
	hostsContent := `******* cloudflare.com
******* google-public-dns-a.google.com
******* google-public-dns-b.google.com
************** resolver1.opendns.com
`
	err = os.WriteFile("./test_hosts.txt", []byte(hostsContent), 0644)
	require.NoError(t, err, "创建测试hosts文件应成功")

	// 返回清理函数
	return func() {
		os.Remove("./test_proxies.txt")
		os.Remove("./test_hosts.txt")
	}
}

// CreateTestConfig 创建测试配置
func CreateTestConfig() *common.Config {
	return &common.Config{
		Global: common.GlobalConfig{
			Enable:    true,
			ProxyFile: "./test_proxies.txt",
			GlobalBannedIPs: []common.BanIPConfig{
				{IP: "*************", Duration: 3600},
				{IP: "*********", Duration: "1h30m"},
				{IP: "***********", Duration: "reboot"},
			},
			BannedDomains: []common.BannedDomainConfig{
				{Domain: "malicious-site.com", Duration: 86400},
				{Domain: "spam-domain.net", Duration: "24h"},
				{Domain: "blocked-forever.com", Duration: "reboot"},
			},
			BlockedIPs: []string{
				"***********",
				"************",
				"*********",
			},
			TrustedIPs: []string{
				"127.0.0.1",
				"::1",
				"***********",
				"********",
			},
			ExcludedPatterns: []string{
				"*.local",
				"localhost:*",
				"127.0.0.1:*",
				"*.internal.company.com",
			},
			ExcludedScope:        "all",
			RulePriority:         50,
			DefaultProcessStage:  "post_header",
			DNSLookupMode:        "custom",
			ReverseDNSLookup:     "dns",
			HTTPProxyDNS:         "*******:53",
			IPRotationMode:       "smart",
			MinProxyPoolSize:     10,
			MaxProxyFetchAttempts: 3,
			DNSCacheTTL:          300,
			DNSNoCache:           false,
			IPVersionPriority:    "ipv4",
			DefaultDNSTimeout:    5000,
			RetryProxyReusePolicy:    "cooldown",
			RetryProxyCooldownTime:   60,
			RetryProxyGlobalTracking: true,
			CustomDNSServers: []common.CustomDNSServer{
				{
					Server:   "*******:53",
					Protocol: "udp",
					Timeout:  5000,
					Priority: 1,
					Tags:     []string{"cloudflare", "fast"},
				},
				{
					Server:   "*******:53",
					Protocol: "udp",
					Timeout:  5000,
					Priority: 2,
					Tags:     []string{"google", "reliable"},
				},
				{
					Server:   "https://*******/dns-query",
					Protocol: "doh",
					Timeout:  10000,
					Priority: 3,
					Tags:     []string{"cloudflare", "secure"},
				},
			},
		},
	}
}

// CreateMinimalTestConfig 创建最小测试配置
func CreateMinimalTestConfig() *common.Config {
	return &common.Config{
		Global: common.GlobalConfig{
			Enable:    true,
			ProxyFile: "./test_proxies.txt",
		},
	}
}

// CreateAdvancedTestConfig 创建高级测试配置
func CreateAdvancedTestConfig() *common.Config {
	config := CreateTestConfig()
	
	// 添加更多高级配置
	config.Global.DNSLookupMode = "hybrid"
	config.Global.ReverseDNSLookup = "file:./test_hosts.txt"
	config.Global.IPRotationMode = "quality"
	config.Global.RetryProxyReusePolicy = "deny"
	config.Global.DNSNoCache = true
	
	return config
}

// ValidateTestProxy 验证测试代理格式
func ValidateTestProxy(proxy string) bool {
	if proxy == "" {
		return false
	}
	
	// 简单验证代理URL格式
	validPrefixes := []string{"http://", "https://", "socks5://", "socks4://"}
	for _, prefix := range validPrefixes {
		if len(proxy) > len(prefix) && proxy[:len(prefix)] == prefix {
			return true
		}
	}
	
	return false
}

// GetTestProxyList 获取测试代理列表
func GetTestProxyList() []string {
	return []string{
		"http://proxy1.example.com:8080",
		"http://proxy2.example.com:8080",
		"http://proxy3.example.com:8080",
		"socks5://proxy4.example.com:1080",
		"http://user:<EMAIL>:8080",
	}
}

// CreateTestConfigFile 创建测试配置文件
func CreateTestConfigFile(t *testing.T, config *common.Config) string {
	// 这里可以添加将配置序列化为YAML文件的逻辑
	// 暂时返回一个固定的测试配置文件路径
	configPath := "./test_config.yaml"
	
	// 创建一个简单的测试配置文件内容
	configContent := `global:
  enable: true
  proxy_file: "./test_proxies.txt"
  ip_rotation_mode: "smart"
  dns_cache_ttl: 300
  retry_proxy_reuse_policy: "cooldown"
  retry_proxy_cooldown_time: 60
`
	
	err := os.WriteFile(configPath, []byte(configContent), 0644)
	require.NoError(t, err, "创建测试配置文件应成功")
	
	return configPath
}

// CleanupTestConfigFile 清理测试配置文件
func CleanupTestConfigFile(configPath string) {
	if configPath != "" {
		os.Remove(configPath)
	}
}

// TestDataDir 获取测试数据目录
func TestDataDir() string {
	return filepath.Join(".", "testdata")
}

// EnsureTestDataDir 确保测试数据目录存在
func EnsureTestDataDir(t *testing.T) {
	dir := TestDataDir()
	err := os.MkdirAll(dir, 0755)
	require.NoError(t, err, "创建测试数据目录应成功")
}

// CreateTestProxyFile 在指定路径创建测试代理文件
func CreateTestProxyFile(t *testing.T, path string) {
	content := `# FlexProxy 测试代理列表
# 格式: protocol://[username:password@]host:port

# HTTP 代理
http://proxy1.example.com:8080
http://proxy2.example.com:8080
http://proxy3.example.com:8080

# 带认证的HTTP代理
http://user:<EMAIL>:8080
http://testuser:<EMAIL>:8080

# SOCKS5 代理
socks5://socks1.example.com:1080
socks5://socks2.example.com:1080

# 带认证的SOCKS5代理
socks5://user:<EMAIL>:1080

# HTTPS 代理
https://secure-proxy.example.com:8443
`
	
	err := os.WriteFile(path, []byte(content), 0644)
	require.NoError(t, err, "创建测试代理文件应成功")
}

// CreateTestHostsFile 创建测试hosts文件
func CreateTestHostsFile(t *testing.T, path string) {
	content := `# 测试反向DNS映射文件
# 格式: IP地址 域名

# 公共DNS服务器
******* one.one.one.one
******* dns.google
******* dns.google
************** resolver1.opendns.com
************** resolver2.opendns.com

# 测试IP
*********** gateway.local
******** router.local
********** switch.local

# CDN节点
************** cloudflare.com
*************** google.com
`
	
	err := os.WriteFile(path, []byte(content), 0644)
	require.NoError(t, err, "创建测试hosts文件应成功")
}
