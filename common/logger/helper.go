package logger

// ModuleLogger 模块级别的logger封装
// 为每个模块提供便捷的logger访问，复用manager.go中的单例LoggerManager
type ModuleLogger struct {
	logger Logger
	module string
}

// NewModuleLogger 创建模块logger实例
// 复用manager.go中已有的GetLogger函数，避免重复的单例实现
func NewModuleLogger(module string) *ModuleLogger {
	return &ModuleLogger{
		logger: GetLogger(module), // 复用manager.go中的单例LoggerManager
		module: module,
	}
}

// Debug 记录调试信息
func (ml *ModuleLogger) Debug(msg string) {
	ml.logger.Debug(msg)
}

// Info 记录信息
func (ml *ModuleLogger) Info(msg string) {
	ml.logger.Info(msg)
}

// Warn 记录警告
func (ml *ModuleLogger) Warn(msg string) {
	ml.logger.Warn(msg)
}

// Error 记录错误
func (ml *ModuleLogger) Error(msg string) {
	ml.logger.Error(msg)
}

// Fatal 记录致命错误
func (ml *ModuleLogger) Fatal(msg string) {
	ml.logger.Fatal(msg)
}

// GetRawLogger 获取原始logger实例（用于需要格式化输出的场景）
func (ml *ModuleLogger) GetRawLogger() Logger {
	return ml.logger
}

// 便捷函数：获取各模块的logger实例
// 直接复用manager.go中的单例LoggerManager，避免重复的单例实现

// GetTriggerLogger 获取Trigger模块的logger
func GetTriggerLogger() *ModuleLogger {
	return NewModuleLogger(ModuleTrigger)
}

// GetActionLogger 获取Action模块的logger
func GetActionLogger() *ModuleLogger {
	return NewModuleLogger(ModuleAction)
}

// GetHandlerLogger 获取Handler模块的logger
func GetHandlerLogger() *ModuleLogger {
	return NewModuleLogger(ModuleHandler)
}

// GetTransportLogger 获取Transport模块的logger
func GetTransportLogger() *ModuleLogger {
	return NewModuleLogger(ModuleTransport)
}

// GetMainLogger 获取Main模块的logger
func GetMainLogger() *ModuleLogger {
	return NewModuleLogger(ModuleMain)
}

// GetRunnerLogger 获取Runner模块的logger
func GetRunnerLogger() *ModuleLogger {
	return NewModuleLogger(ModuleRunner)
}

// GetDaemonLogger 获取Daemon模块的logger
func GetDaemonLogger() *ModuleLogger {
	return NewModuleLogger(ModuleDaemon)
}

// GetErrorLogger 获取Error模块的logger
func GetErrorLogger() *ModuleLogger {
	return NewModuleLogger(ModuleError)
}

// GetProxyManagerLogger 获取ProxyManager模块的logger
func GetProxyManagerLogger() *ModuleLogger {
	return NewModuleLogger(ModuleProxyManager)
}

// GetServerLogger 获取Server模块的logger
func GetServerLogger() *ModuleLogger {
	return NewModuleLogger(ModuleServer)
}

// GetProxyServiceLogger 获取ProxyService模块的logger
func GetProxyServiceLogger() *ModuleLogger {
	return NewModuleLogger(ModuleProxyService)
}

// GetCacheServiceLogger 获取CacheService模块的logger
func GetCacheServiceLogger() *ModuleLogger {
	return NewModuleLogger(ModuleCacheService)
}

// GetConfigServiceLogger 获取ConfigService模块的logger
func GetConfigServiceLogger() *ModuleLogger {
	return NewModuleLogger(ModuleConfigService)
}

// GetDNSServiceLogger 获取DNSService模块的logger
func GetDNSServiceLogger() *ModuleLogger {
	return NewModuleLogger(ModuleDNSService)
}

// GetTriggerServiceLogger 获取TriggerService模块的logger
func GetTriggerServiceLogger() *ModuleLogger {
	return NewModuleLogger(ModuleTriggerService)
}

// GetActionServiceLogger 获取ActionService模块的logger
func GetActionServiceLogger() *ModuleLogger {
	return NewModuleLogger(ModuleActionService)
}

// GetLogServiceLogger 获取LogService模块的logger
func GetLogServiceLogger() *ModuleLogger {
	return NewModuleLogger(ModuleLogService)
}

// GetRateLimitingServiceLogger 获取限流服务logger
func GetRateLimitingServiceLogger() *ModuleLogger {
	return NewModuleLogger(ModuleRateLimitingService)
}

// GetSecurityServiceLogger 获取安全服务logger
func GetSecurityServiceLogger() *ModuleLogger {
	return NewModuleLogger(ModuleSecurityService)
}

// GetPluginServiceLogger 获取插件服务logger
func GetPluginServiceLogger() *ModuleLogger {
	return NewModuleLogger(ModulePluginService)
}

// GetErrorChainLogger 获取ErrorChain模块的logger
func GetErrorChainLogger() *ModuleLogger {
	return NewModuleLogger(ModuleErrorChain)
}
