package common

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/flexp/flexp/common/errors"
	"gopkg.in/yaml.v3"
)

// LoadConfigFromYAML 从指定的 YAML 文件路径加载配置
// 它返回一个指向 Config 结构体的指针，如果发生错误则返回 error。
func LoadConfigFromYAML(configPath string) (*Config, error) {
	if configPath == "" {
		return nil, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "配置文件路径不能为空")
	}

	absPath, err := filepath.Abs(configPath)
	if err != nil {
		return nil, errors.WrapErrorWithDetails(err, errors.ErrTypeFile, errors.ErrCodeFilePathInvalid, "无法获取配置文件的绝对路径", "配置路径: "+configPath)
	}

	yamlFile, err := os.ReadFile(absPath)
	if err != nil {
		return nil, errors.WrapErrorWithDetails(err, errors.ErrTypeFile, errors.ErrCodeFileReadFailed, "无法读取YAML配置文件", "文件路径: "+absPath)
	}

	var cfg Config
	err = yaml.Unmarshal(yamlFile, &cfg)
	if err != nil {
		return nil, errors.WrapErrorWithDetails(err, errors.ErrTypeConfig, errors.ErrCodeConfigParseFailed, "无法解析YAML配置文件", "文件路径: "+absPath)
	}

	// 执行配置验证
	validator := NewConfigValidator()
	if err := validator.ValidateConfig(&cfg); err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeConfig, errors.ErrCodeConfigValidationFailed, "配置验证失败")
	}

	return &cfg, nil
}

// LoadConfigFromYAMLWithoutValidation 从YAML文件加载配置但不进行验证
// 主要用于测试或特殊场景
func LoadConfigFromYAMLWithoutValidation(configPath string) (*Config, error) {
	if configPath == "" {
		return nil, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "配置文件路径不能为空")
	}

	absPath, err := filepath.Abs(configPath)
	if err != nil {
		return nil, errors.WrapErrorWithDetails(err, errors.ErrTypeFile, errors.ErrCodeFilePathInvalid, "无法获取配置文件的绝对路径", "配置路径: "+configPath)
	}

	yamlFile, err := os.ReadFile(absPath)
	if err != nil {
		return nil, errors.WrapErrorWithDetails(err, errors.ErrTypeFile, errors.ErrCodeFileReadFailed, "无法读取YAML配置文件", "文件路径: "+absPath)
	}

	var cfg Config
	err = yaml.Unmarshal(yamlFile, &cfg)
	if err != nil {
		return nil, errors.WrapErrorWithDetails(err, errors.ErrTypeConfig, errors.ErrCodeConfigParseFailed, "无法解析YAML配置文件", "文件路径: "+absPath)
	}

	return &cfg, nil
}

// ValidateConfig 验证已加载的配置
func ValidateConfig(cfg *Config) error {
	validator := NewConfigValidator()
	return validator.ValidateConfig(cfg)
}

// ValidateConfigFile 验证配置文件（用于程序启动时的早期验证）
func ValidateConfigFile(configPath string) error {
	if configPath == "" {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
			errors.ErrConfigFieldRequired.Message, "配置文件路径不能为空")
	}

	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return errors.NewErrorWithDetails(errors.ErrTypeFile, errors.ErrCodeFileReadFailed,
			"配置文件不存在", fmt.Sprintf("文件路径: %s", configPath))
	}

	// 尝试加载和验证配置
	cfg, err := LoadConfigFromYAML(configPath)
	if err != nil {
		return errors.WrapErrorWithDetails(err, errors.ErrTypeConfig, errors.ErrCodeConfigValidationFailed,
			"配置文件验证失败", fmt.Sprintf("文件路径: %s", configPath))
	}

	// 额外的启动时验证
	if err := validateStartupRequirements(cfg); err != nil {
		return err
	}

	return nil
}

// validateStartupRequirements 验证启动时的必需配置
func validateStartupRequirements(cfg *Config) error {
	// 验证全局配置是否启用
	if !cfg.Global.Enable {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigServiceDisabled,
			errors.ErrConfigServiceDisabled.Message, "请在配置文件中设置 global.enable: true")
	}

	// 验证代理文件路径
	if cfg.Global.ProxyFile == "" {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigProxyFileEmpty,
			errors.ErrConfigProxyFileEmpty.Message, "请在配置文件中设置 global.proxy_file 字段")
	}

	// 检查代理文件是否存在
	if _, err := os.Stat(cfg.Global.ProxyFile); os.IsNotExist(err) {
		return errors.NewErrorWithDetails(errors.ErrTypeFile, errors.ErrCodeConfigProxyFileNotFound,
			errors.ErrConfigProxyFileNotFound.Message, fmt.Sprintf("文件路径: %s", cfg.Global.ProxyFile))
	}

	return nil
}

// TODO: 添加一个函数来处理 Duration (int or "reboot") 和 URLPatterns (string or []string)
// 这可能需要自定义 UnmarshalYAML 方法或在加载后进行转换。

// 加载后如何访问特定动作参数的示例：
//
// loadedCfg, err := LoadConfigFromYAML("path/to/config.yaml")
// if err != nil {
//     log.Fatalf("加载配置错误: %v", err)
// }
//
// if actionSeq, ok := loadedCfg.Actions["Status"]; ok {
//     if len(actionSeq.Sequence) > 0 {
//         firstAction := actionSeq.Sequence[0]
//         if firstAction.Type == "banip" {
//             if duration, found := firstAction.Params["duration"]; found {
//                 fmt.Printf("BanIP 持续时间: %v\n", duration) // duration 将是一个 interface{}
//             }
//         }
//     }
// }
