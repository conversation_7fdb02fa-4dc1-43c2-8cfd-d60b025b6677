// Package services 提供各种服务实现
package services

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/common/errors"
	"github.com/flexp/flexp/common/logger"
	"github.com/flexp/flexp/internal/interfaces"
)

// AdvancedConfigService 高级配置管理服务实现
type advancedConfigService struct {
	mu         sync.RWMutex
	config     *common.AdvancedConfig
	logger     logger.Logger
	running    bool
	watchers   map[string]*ConfigWatcher
	validators map[string]ConfigValidator
	history    []*ConfigChange
	maxHistory int
	ctx        context.Context
	cancel     context.CancelFunc
}

// ConfigWatcher 配置监视器
type ConfigWatcher struct {
	ID       string                               `json:"id"`
	Path     string                               `json:"path"`
	Callback func(oldValue, newValue interface{}) `json:"-"`
	Enabled  bool                                 `json:"enabled"`
	Created  time.Time                            `json:"created"`
}

// ConfigValidator 配置验证器接口
type ConfigValidator interface {
	Validate(value interface{}) error
}

// ConfigValidatorFunc 配置验证器函数类型
type ConfigValidatorFunc func(value interface{}) error

// Validate 实现ConfigValidator接口
func (f ConfigValidatorFunc) Validate(value interface{}) error {
	return f(value)
}

// ConfigChange 配置变更记录
type ConfigChange struct {
	Timestamp time.Time   `json:"timestamp"`
	Path      string      `json:"path"`
	OldValue  interface{} `json:"old_value"`
	NewValue  interface{} `json:"new_value"`
	Source    string      `json:"source"`
	Reason    string      `json:"reason"`
}

// ConfigStats 配置统计信息
type ConfigStats struct {
	TotalChanges     int                    `json:"total_changes"`
	ActiveWatchers   int                    `json:"active_watchers"`
	ValidationErrors int                    `json:"validation_errors"`
	LastChange       time.Time              `json:"last_change"`
	MostChanged      map[string]int         `json:"most_changed"`
	ConfigSections   map[string]interface{} `json:"config_sections"`
}

// NewAdvancedConfigService 创建新的高级配置管理服务
func NewAdvancedConfigService(config *common.AdvancedConfig, log logger.Logger) interfaces.AdvancedConfigService {
	if log == nil {
		log = logger.GetLogger("advanced_config")
	}

	if config == nil {
		config = &common.AdvancedConfig{
			ErrorRecovery: &common.ErrorRecoveryConfig{
				MaxRetryAttempts:    3,
				InitialRetryDelay:   "1s",
				MaxRetryDelay:       "30s",
				RetryMultiplier:     2.0,
				FailureThreshold:    5,
				SuccessThreshold:    3,
				CircuitTimeout:      "60s",
				CircuitResetTimeout: "300s",
			},
			Tracing: &common.TracingConfig{
				Enabled:            true,
				HexGeneratorLength: 16,
				SequenceModulus:    10000,
			},
			Performance: &common.PerformanceConfig{
				WorkerPoolSize: 10,
				QueueSize:      1000,
				BatchSize:      100,
				FlushInterval:  "5s",
			},
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	acs := &advancedConfigService{
		config:     config,
		logger:     log,
		watchers:   make(map[string]*ConfigWatcher),
		validators: make(map[string]ConfigValidator),
		history:    make([]*ConfigChange, 0),
		maxHistory: 1000,
		ctx:        ctx,
		cancel:     cancel,
	}

	// 注册默认验证器
	acs.registerDefaultValidators()

	acs.logger.Info("高级配置管理服务已初始化")
	return acs
}

// Start 启动高级配置管理服务
func (acs *advancedConfigService) Start() error {
	acs.mu.Lock()
	defer acs.mu.Unlock()

	if acs.running {
		return fmt.Errorf("高级配置管理服务已在运行")
	}

	// 启动配置监控
	go acs.monitorConfig()

	acs.running = true
	acs.logger.Info("高级配置管理服务已启动")
	return nil
}

// Stop 停止高级配置管理服务
func (acs *advancedConfigService) Stop() error {
	acs.mu.Lock()
	defer acs.mu.Unlock()

	if !acs.running {
		return nil
	}

	acs.cancel()
	acs.running = false
	acs.logger.Info("高级配置管理服务已停止")
	return nil
}

// GetConfig 获取完整配置
func (acs *advancedConfigService) GetConfig() map[string]interface{} {
	acs.mu.RLock()
	defer acs.mu.RUnlock()

	// 将配置转换为map[string]interface{}
	configMap := make(map[string]interface{})
	configMap["error_recovery"] = acs.config.ErrorRecovery
	configMap["tracing"] = acs.config.Tracing
	configMap["performance"] = acs.config.Performance
	configMap["debug"] = acs.config.Debug

	return configMap
}

// UpdateConfig 更新配置
func (acs *advancedConfigService) UpdateConfig(config map[string]interface{}) error {
	source := "api"
	reason := "config update"

	// 将map转换为AdvancedConfig结构
	newConfig := &common.AdvancedConfig{
		ErrorRecovery: &common.ErrorRecoveryConfig{
			MaxRetryAttempts:    3,
			InitialRetryDelay:   "1s",
			MaxRetryDelay:       "30s",
			RetryMultiplier:     2.0,
			FailureThreshold:    5,
			SuccessThreshold:    3,
			CircuitTimeout:      "60s",
			CircuitResetTimeout: "300s",
		},
		Tracing: &common.TracingConfig{
			Enabled:            false,
			HexGeneratorLength: 16,
			SequenceModulus:    10000,
		},
		Performance: &common.PerformanceConfig{
			WorkerPoolSize: 10,
			QueueSize:      1000,
			BatchSize:      100,
			FlushInterval:  "5s",
		},
	}
	acs.mu.Lock()
	defer acs.mu.Unlock()

	// 验证新配置
	if err := acs.validateConfig(newConfig); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}

	// 记录变更
	oldConfig := *acs.config
	change := &ConfigChange{
		Timestamp: time.Now(),
		Path:      "root",
		OldValue:  oldConfig,
		NewValue:  *newConfig,
		Source:    source,
		Reason:    reason,
	}

	// 更新配置
	acs.config = newConfig

	// 添加到历史记录
	acs.addToHistory(change)

	// 通知监视器
	acs.notifyWatchers("root", oldConfig, *newConfig)

	acs.logger.Info(fmt.Sprintf("配置已更新，来源: %s，原因: %s", source, reason))
	return nil
}

// GetConfigValue 获取指定路径的配置值
func (acs *advancedConfigService) GetConfigValue(path string) (interface{}, error) {
	acs.mu.RLock()
	defer acs.mu.RUnlock()

	return acs.getValueByPath(acs.config, path)
}

// SetConfigValue 设置指定路径的配置值
func (acs *advancedConfigService) SetConfigValue(path string, value interface{}) error {
	source := "api"
	reason := "manual update"
	acs.mu.Lock()
	defer acs.mu.Unlock()

	// 获取旧值
	oldValue, err := acs.getValueByPath(acs.config, path)
	if err != nil {
		return fmt.Errorf("获取旧值失败: %v", err)
	}

	// 验证新值
	if validator, exists := acs.validators[path]; exists {
		if err := validator.Validate(value); err != nil {
			return fmt.Errorf("值验证失败: %v", err)
		}
	}

	// 设置新值
	if err := acs.setValueByPath(acs.config, path, value); err != nil {
		return fmt.Errorf("设置值失败: %v", err)
	}

	// 记录变更
	change := &ConfigChange{
		Timestamp: time.Now(),
		Path:      path,
		OldValue:  oldValue,
		NewValue:  value,
		Source:    source,
		Reason:    reason,
	}

	// 添加到历史记录
	acs.addToHistory(change)

	// 通知监视器
	acs.notifyWatchers(path, oldValue, value)

	acs.logger.Info(fmt.Sprintf("配置值已更新: %s = %v，来源: %s", path, value, source))
	return nil
}

// AddWatcher 添加配置监视器
func (acs *advancedConfigService) AddWatcher(id, path string, callback func(string, interface{}, interface{})) error {
	if id == "" || path == "" {
		return fmt.Errorf("监视器ID和路径不能为空")
	}

	acs.mu.Lock()
	defer acs.mu.Unlock()

	if _, exists := acs.watchers[id]; exists {
		return fmt.Errorf("监视器已存在: %s", id)
	}

	// 包装回调函数
	wrappedCallback := func(oldValue, newValue interface{}) {
		callback(path, oldValue, newValue)
	}

	watcher := &ConfigWatcher{
		ID:       id,
		Path:     path,
		Callback: wrappedCallback,
		Enabled:  true,
		Created:  time.Now(),
	}

	acs.watchers[id] = watcher
	acs.logger.Debug(fmt.Sprintf("添加配置监视器: %s -> %s", id, path))
	return nil
}

// RemoveWatcher 移除配置监视器
func (acs *advancedConfigService) RemoveWatcher(id string) error {
	acs.mu.Lock()
	defer acs.mu.Unlock()

	if _, exists := acs.watchers[id]; !exists {
		return fmt.Errorf("监视器不存在: %s", id)
	}

	delete(acs.watchers, id)
	acs.logger.Debug(fmt.Sprintf("移除配置监视器: %s", id))
	return nil
}

// AddValidator 添加验证器
func (acs *advancedConfigService) AddValidator(path string, validator func(interface{}) error) error {
	if path == "" {
		return fmt.Errorf("验证器路径不能为空")
	}
	if validator == nil {
		return fmt.Errorf("验证器函数不能为空")
	}

	acs.mu.Lock()
	defer acs.mu.Unlock()

	// 包装验证器函数为ConfigValidator接口
	configValidator := ConfigValidatorFunc(validator)
	acs.validators[path] = configValidator
	acs.logger.Debug(fmt.Sprintf("添加配置验证器: %s", path))
	return nil
}

// GetHistory 获取配置变更历史
func (acs *advancedConfigService) GetHistory(limit int) []interface{} {
	acs.mu.RLock()
	defer acs.mu.RUnlock()

	var result []interface{}
	historyToReturn := acs.history
	if limit > 0 && limit < len(acs.history) {
		start := len(acs.history) - limit
		historyToReturn = acs.history[start:]
	}

	for _, change := range historyToReturn {
		result = append(result, change)
	}

	return result
}

// GetStats 获取配置统计信息
func (acs *advancedConfigService) GetStats() map[string]interface{} {
	acs.mu.RLock()
	defer acs.mu.RUnlock()

	stats := map[string]interface{}{
		"total_changes":   len(acs.history),
		"active_watchers": len(acs.watchers),
		"most_changed":    make(map[string]int),
		"config_sections": make(map[string]interface{}),
		"last_change":     time.Time{},
	}

	// 统计最常变更的路径
	mostChanged := make(map[string]int)
	lastChange := time.Time{}
	for _, change := range acs.history {
		mostChanged[change.Path]++
		if change.Timestamp.After(lastChange) {
			lastChange = change.Timestamp
		}
	}
	stats["most_changed"] = mostChanged
	stats["last_change"] = lastChange

	// 统计配置节
	configSections := make(map[string]interface{})
	configSections["error_recovery"] = acs.config.ErrorRecovery
	configSections["tracing"] = acs.config.Tracing
	configSections["performance"] = acs.config.Performance
	stats["config_sections"] = configSections

	return stats
}

// ResetConfig 重置配置到默认值
func (acs *advancedConfigService) ResetConfig() error {
	defaultConfig := &common.AdvancedConfig{
		ErrorRecovery: &common.ErrorRecoveryConfig{
			MaxRetryAttempts:    3,
			InitialRetryDelay:   "1s",
			MaxRetryDelay:       "30s",
			RetryMultiplier:     2.0,
			FailureThreshold:    5,
			SuccessThreshold:    3,
			CircuitTimeout:      "60s",
			CircuitResetTimeout: "300s",
		},
		Tracing: &common.TracingConfig{
			Enabled:            true,
			HexGeneratorLength: 16,
			SequenceModulus:    10000,
		},
		Performance: &common.PerformanceConfig{
			WorkerPoolSize: 10,
			QueueSize:      1000,
			BatchSize:      100,
			FlushInterval:  "5s",
		},
		Debug: &common.DebugConfig{
			Enabled:        false,
			VerboseLogging: false,
			DumpRequests:   false,
			DumpResponses:  false,
			ProfileEnabled: false,
			ProfilePort:    8081,
		},
	}

	// 将defaultConfig转换为map[string]interface{}
	configMap := map[string]interface{}{
		"error_recovery": defaultConfig.ErrorRecovery,
		"tracing":        defaultConfig.Tracing,
		"performance":    defaultConfig.Performance,
		"debug":          defaultConfig.Debug,
	}
	return acs.UpdateConfig(configMap)
}

// ExportConfig 导出配置
func (acs *advancedConfigService) ExportConfig(format string) ([]byte, error) {
	acs.mu.RLock()
	defer acs.mu.RUnlock()

	switch format {
	case "json":
		return json.MarshalIndent(acs.config, "", "  ")
	default:
		return json.MarshalIndent(acs.config, "", "  ")
	}
}

// ImportConfig 导入配置
func (acs *advancedConfigService) ImportConfig(data []byte, source string) error {
	var newConfig common.AdvancedConfig
	if err := json.Unmarshal(data, &newConfig); err != nil {
		return fmt.Errorf("解析配置失败: %v", err)
	}

	// 将newConfig转换为map[string]interface{}
	configMap := map[string]interface{}{
		"error_recovery": newConfig.ErrorRecovery,
		"tracing":        newConfig.Tracing,
		"performance":    newConfig.Performance,
		"debug":          newConfig.Debug,
	}
	return acs.UpdateConfig(configMap)
}

// 内部方法

// registerDefaultValidators 注册默认验证器
func (acs *advancedConfigService) registerDefaultValidators() {
	// 错误恢复配置验证器
	acs.validators["error_recovery.max_retry_attempts"] = ConfigValidatorFunc(func(value interface{}) error {
		if v, ok := value.(int); ok {
			if v < 0 || v > 10 {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigRetryAttemptsInvalid,
					errors.ErrConfigRetryAttemptsInvalid.Message, "最大重试次数必须在0-10之间")
			}
			return nil
		}
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigValueTypeInvalid,
			errors.ErrConfigValueTypeInvalid.Message, "最大重试次数必须是整数")
	})

	// 追踪配置验证器
	acs.validators["tracing.hex_generator_length"] = ConfigValidatorFunc(func(value interface{}) error {
		if v, ok := value.(int); ok {
			if v < 8 || v > 64 {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigHexLengthInvalid,
					errors.ErrConfigHexLengthInvalid.Message, "十六进制生成器长度必须在8-64之间")
			}
			return nil
		}
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigValueTypeInvalid,
			errors.ErrConfigValueTypeInvalid.Message, "十六进制生成器长度必须是整数")
	})

	// 性能配置验证器
	acs.validators["performance.worker_pool_size"] = ConfigValidatorFunc(func(value interface{}) error {
		if v, ok := value.(int); ok {
			if v < 1 || v > 1000 {
				return fmt.Errorf("工作池大小必须在1-1000之间")
			}
			return nil
		}
		return fmt.Errorf("工作池大小必须是整数")
	})
}

// validateConfig 验证配置
func (acs *advancedConfigService) validateConfig(config *common.AdvancedConfig) error {
	// 验证错误恢复配置
	if config.ErrorRecovery != nil {
		if config.ErrorRecovery.MaxRetryAttempts < 0 || config.ErrorRecovery.MaxRetryAttempts > 10 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigRetryAttemptsInvalid,
				errors.ErrConfigRetryAttemptsInvalid.Message, "最大重试次数必须在0-10之间")
		}

		if config.ErrorRecovery.RetryMultiplier < 1.0 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigRetryMultiplierInvalid,
				errors.ErrConfigRetryMultiplierInvalid.Message, "退避因子必须大于等于1.0")
		}
	}

	// 验证追踪配置
	if config.Tracing != nil {
		if config.Tracing.HexGeneratorLength < 8 || config.Tracing.HexGeneratorLength > 64 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigHexLengthInvalid,
				errors.ErrConfigHexLengthInvalid.Message, "十六进制生成器长度必须在8-64之间")
		}

		if config.Tracing.SequenceModulus < 1000 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigSequenceModulusInvalid,
				errors.ErrConfigSequenceModulusInvalid.Message, "序列模数必须大于等于1000")
		}
	}

	// 验证性能配置
	if config.Performance != nil {
		if config.Performance.WorkerPoolSize < 1 || config.Performance.WorkerPoolSize > 1000 {
			return fmt.Errorf("工作池大小必须在1-1000之间")
		}
	}

	if config.Performance.QueueSize < 1 {
		return fmt.Errorf("队列大小必须大于0")
	}

	return nil
}

// getValueByPath 根据路径获取值
func (acs *advancedConfigService) getValueByPath(config *common.AdvancedConfig, path string) (interface{}, error) {
	switch path {
	case "error_recovery":
		return config.ErrorRecovery, nil
	case "error_recovery.max_retry_attempts":
		return config.ErrorRecovery.MaxRetryAttempts, nil
	case "error_recovery.initial_retry_delay":
		return config.ErrorRecovery.InitialRetryDelay, nil
	case "error_recovery.max_retry_delay":
		return config.ErrorRecovery.MaxRetryDelay, nil
	case "error_recovery.retry_multiplier":
		return config.ErrorRecovery.RetryMultiplier, nil
	case "error_recovery.failure_threshold":
		return config.ErrorRecovery.FailureThreshold, nil
	case "error_recovery.success_threshold":
		return config.ErrorRecovery.SuccessThreshold, nil
	case "error_recovery.circuit_timeout":
		return config.ErrorRecovery.CircuitTimeout, nil
	case "error_recovery.circuit_reset_timeout":
		return config.ErrorRecovery.CircuitResetTimeout, nil
	case "tracing":
		return config.Tracing, nil
	case "tracing.enabled":
		return config.Tracing.Enabled, nil
	case "tracing.hex_generator_length":
		return config.Tracing.HexGeneratorLength, nil
	case "tracing.sequence_modulus":
		return config.Tracing.SequenceModulus, nil
	case "performance":
		return config.Performance, nil
	case "performance.worker_pool_size":
		return config.Performance.WorkerPoolSize, nil
	case "performance.queue_size":
		return config.Performance.QueueSize, nil
	case "performance.batch_size":
		return config.Performance.BatchSize, nil
	case "performance.flush_interval":
		return config.Performance.FlushInterval, nil
	case "debug":
		return config.Debug, nil
	case "debug.enabled":
		return config.Debug.Enabled, nil
	case "debug.verbose_logging":
		return config.Debug.VerboseLogging, nil
	case "debug.dump_requests":
		return config.Debug.DumpRequests, nil
	case "debug.dump_responses":
		return config.Debug.DumpResponses, nil
	case "debug.profile_enabled":
		return config.Debug.ProfileEnabled, nil
	case "debug.profile_port":
		return config.Debug.ProfilePort, nil
	default:
		return nil, fmt.Errorf("未知的配置路径: %s", path)
	}
}

// setValueByPath 根据路径设置值
func (acs *advancedConfigService) setValueByPath(config *common.AdvancedConfig, path string, value interface{}) error {
	switch path {
	case "error_recovery.max_retry_attempts":
		if v, ok := value.(int); ok {
			config.ErrorRecovery.MaxRetryAttempts = v
			return nil
		}
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigValueTypeInvalid,
			errors.ErrConfigValueTypeInvalid.Message, "值类型错误，期望int")
	case "error_recovery.initial_retry_delay":
		if v, ok := value.(string); ok {
			config.ErrorRecovery.InitialRetryDelay = v
			return nil
		}
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigValueTypeInvalid,
			errors.ErrConfigValueTypeInvalid.Message, "值类型错误，期望string")
	case "error_recovery.max_retry_delay":
		if v, ok := value.(string); ok {
			config.ErrorRecovery.MaxRetryDelay = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望string")
	case "error_recovery.retry_multiplier":
		if v, ok := value.(float64); ok {
			config.ErrorRecovery.RetryMultiplier = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望float64")
	case "error_recovery.failure_threshold":
		if v, ok := value.(int); ok {
			config.ErrorRecovery.FailureThreshold = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望int")
	case "error_recovery.success_threshold":
		if v, ok := value.(int); ok {
			config.ErrorRecovery.SuccessThreshold = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望int")
	case "error_recovery.circuit_timeout":
		if v, ok := value.(string); ok {
			config.ErrorRecovery.CircuitTimeout = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望string")
	case "error_recovery.circuit_reset_timeout":
		if v, ok := value.(string); ok {
			config.ErrorRecovery.CircuitResetTimeout = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望string")
	case "tracing.enabled":
		if v, ok := value.(bool); ok {
			config.Tracing.Enabled = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望bool")
	case "tracing.hex_generator_length":
		if v, ok := value.(int); ok {
			config.Tracing.HexGeneratorLength = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望int")
	case "tracing.sequence_modulus":
		if v, ok := value.(int); ok {
			config.Tracing.SequenceModulus = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望int")
	case "performance.worker_pool_size":
		if v, ok := value.(int); ok {
			config.Performance.WorkerPoolSize = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望int")
	case "performance.queue_size":
		if v, ok := value.(int); ok {
			config.Performance.QueueSize = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望int")
	case "performance.batch_size":
		if v, ok := value.(int); ok {
			config.Performance.BatchSize = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望int")
	case "performance.flush_interval":
		if v, ok := value.(string); ok {
			config.Performance.FlushInterval = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望string")
	case "debug.enabled":
		if v, ok := value.(bool); ok {
			config.Debug.Enabled = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望bool")
	case "debug.verbose_logging":
		if v, ok := value.(bool); ok {
			config.Debug.VerboseLogging = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望bool")
	case "debug.dump_requests":
		if v, ok := value.(bool); ok {
			config.Debug.DumpRequests = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望bool")
	case "debug.dump_responses":
		if v, ok := value.(bool); ok {
			config.Debug.DumpResponses = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望bool")
	case "debug.profile_enabled":
		if v, ok := value.(bool); ok {
			config.Debug.ProfileEnabled = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望bool")
	case "debug.profile_port":
		if v, ok := value.(int); ok {
			config.Debug.ProfilePort = v
			return nil
		}
		return fmt.Errorf("值类型错误，期望int")
	default:
		return fmt.Errorf("未知的配置路径: %s", path)
	}
}

// addToHistory 添加到历史记录
func (acs *advancedConfigService) addToHistory(change *ConfigChange) {
	acs.history = append(acs.history, change)

	// 限制历史记录数量
	if len(acs.history) > acs.maxHistory {
		acs.history = acs.history[1:]
	}
}

// notifyWatchers 通知监视器
func (acs *advancedConfigService) notifyWatchers(path string, oldValue, newValue interface{}) {
	for _, watcher := range acs.watchers {
		if watcher.Enabled && (watcher.Path == path || watcher.Path == "*") {
			if watcher.Callback != nil {
				go watcher.Callback(oldValue, newValue)
			}
		}
	}
}

// monitorConfig 监控配置变化
func (acs *advancedConfigService) monitorConfig() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-acs.ctx.Done():
			return
		case <-ticker.C:
			// 定期检查配置一致性
			acs.validateCurrentConfig()
		}
	}
}

// validateCurrentConfig 验证当前配置
func (acs *advancedConfigService) validateCurrentConfig() {
	acs.mu.RLock()
	config := acs.config
	acs.mu.RUnlock()

	if err := acs.validateConfig(config); err != nil {
		acs.logger.Error(fmt.Sprintf("配置验证失败: %v", err))
	}
}
