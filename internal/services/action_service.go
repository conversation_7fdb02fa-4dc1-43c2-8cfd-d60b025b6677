// Package services 提供各种服务实现
package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/common/constants"
	"github.com/flexp/flexp/common/errors"
	"github.com/flexp/flexp/common/logger"
	"github.com/flexp/flexp/common/trace"
	"github.com/flexp/flexp/internal/action"
	"github.com/flexp/flexp/internal/interfaces"
)

// actionService 动作服务实现
type actionService struct {
	mu           sync.RWMutex
	actions      map[string]*action.ActionDefinition
	executors    map[string]action.Executor
	config       *common.Config
	logger       *logger.LoggerAdapter
	cacheService interfaces.CacheService
	proxyService interfaces.ProxyService
	dnsService   interfaces.DNSService
	running      bool
	ctx          context.Context
	cancel       context.CancelFunc
	queue        chan *action.ExecutionRequest
	workers      int
}

// NewActionService 创建动作服务实例
func NewActionService(
	log *logger.LoggerAdapter,
	cacheService interfaces.CacheService,
	proxyService interfaces.ProxyService,
	dnsService interfaces.DNSService,
) interfaces.ActionService {
	// 如果日志器为空，创建默认的适配器
	if log == nil {
		log = logger.GetLoggerAdapter(logger.ModuleActionService)
	}

	return &actionService{
		actions:      make(map[string]*action.ActionDefinition),
		executors:    make(map[string]action.Executor),
		logger:       log,
		cacheService: cacheService,
		proxyService: proxyService,
		dnsService:   dnsService,
		running:      false,
		queue:        make(chan *action.ExecutionRequest, constants.DefaultActionQueueSize),
		workers:      constants.DefaultActionWorkers,
	}
}

// Initialize 初始化动作服务
func (as *actionService) Initialize(config *common.Config) error {
	as.mu.Lock()
	defer as.mu.Unlock()

	as.logger.Info("开始初始化动作服务 [component=action_service, action=initialize]")

	as.config = config

	// 创建上下文
	as.ctx, as.cancel = context.WithCancel(context.Background())

	// 初始化内置执行器
	if err := as.initializeExecutors(); err != nil {
		as.logger.Error(fmt.Sprintf("初始化执行器失败 [component=action_service, error=%s]", err.Error()))
		return errors.WrapError(
			err,
			errors.ErrTypeInitialization,
			errors.ErrCodeInitializationFailed,
			"初始化执行器失败",
		)
	}

	// 初始化动作
	if err := as.initializeActions(); err != nil {
		as.logger.Error(fmt.Sprintf("初始化动作失败 [component=action_service, error=%s]", err.Error()))
		return errors.WrapError(
			err,
			errors.ErrTypeInitialization,
			errors.ErrCodeInitializationFailed,
			"初始化动作失败",
		)
	}

	as.logger.Info(fmt.Sprintf("动作服务已初始化 [component=action_service, actions_count=%d, executors_count=%d]", len(as.actions), len(as.executors)))
	return nil
}

// initializeExecutors 初始化内置执行器
func (as *actionService) initializeExecutors() error {
	as.logger.Debug("开始初始化内置执行器 [component=action_service, action=initialize_executors]")

	// 创建LogService包装器
	logService := NewLogServiceFromAdapter(as.logger)

	// 注册内置执行器 - 完全支持的动作类型
	as.executors[constants.ActionTypeLog] = &action.LogExecutor{Logger: logService}
	as.executors[constants.ActionTypeBanIP] = &action.BanIPExecutor{
		Logger:       logService,
		ProxyService: as.proxyService,
	}
	as.executors[constants.ActionTypeBanDomain] = &action.BanDomainExecutor{
		Logger:       logService,
		ProxyService: as.proxyService,
	}
	as.executors[constants.ActionTypeBlockRequest] = &action.BlockRequestExecutor{
		Logger: logService,
	}
	as.executors[constants.ActionTypeModifyRequest] = &action.ModifyRequestExecutor{
		Logger: logService,
	}
	as.executors[constants.ActionTypeModifyResponse] = &action.ModifyResponseExecutor{
		Logger: logService,
	}
	as.executors[constants.ActionTypeCacheResponse] = &action.CacheResponseExecutor{
		Logger:       logService,
		CacheService: as.cacheService,
	}
	as.executors[constants.ActionTypeScript] = &action.ScriptExecutor{
		Logger: logService,
	}

	// 注册新增的执行器 - 原Action接口转换为Executor
	as.executors[constants.ActionTypeRetrySame] = &action.RetrySameExecutor{
		Logger:       logService,
		ProxyService: as.proxyService,
	}
	as.executors[constants.ActionTypeRetry] = &action.RetryExecutor{
		Logger:       logService,
		ProxyService: as.proxyService,
	}
	as.executors[constants.ActionTypeBanIPDomain] = &action.BanIPDomainExecutor{
		Logger:       logService,
		ProxyService: as.proxyService,
	}
	as.executors[constants.ActionTypeSaveToPool] = &action.SaveToPoolExecutor{
		Logger:       logService,
		ProxyService: as.proxyService,
	}
	as.executors[constants.ActionTypeCache] = &action.CacheExecutor{
		Logger:       logService,
		CacheService: as.cacheService,
	}
	as.executors[constants.ActionTypeRequestURL] = &action.RequestURLExecutor{
		Logger: logService,
	}
	as.executors[constants.ActionTypeNullResponse] = &action.NullResponseExecutor{
		Logger: logService,
	}
	as.executors[constants.ActionTypeBypassProxy] = &action.BypassProxyExecutor{
		Logger: logService,
	}
	as.executors[constants.ActionTypeScript] = &action.ScriptExecutor{
		Logger: logService,
	}

	as.logger.Debug(fmt.Sprintf("内置执行器初始化完成 [component=action_service, executors_count=%d]", len(as.executors)))
	return nil
}

// initializeActions 初始化所有动作
func (as *actionService) initializeActions() error {
	if as.config == nil {
		as.logger.Warn("没有配置，跳过动作初始化 [component=action_service]")
		return nil
	}

	as.logger.Debug(fmt.Sprintf("开始初始化动作 [component=action_service, action_sequences_count=%d]", len(as.config.Actions)))

	// 遍历配置中的动作序列
	for name, actionSeq := range as.config.Actions {
		if actionSeq.Sequence == nil {
			continue
		}

		// 为每个动作创建实例
		for i, actionConfig := range actionSeq.Sequence {
			actionName := fmt.Sprintf("%s_action_%d", name, i)

			act, err := as.createAction(actionName, &actionConfig)
			if err != nil {
				as.logger.Error(fmt.Sprintf("创建动作失败 [component=action_service, action_name=%s, error=%s]", actionName, err.Error()))
				continue
			}

			as.actions[actionName] = act
			as.logger.Debug(fmt.Sprintf("动作已创建 [component=action_service, action_name=%s, action_type=%s]", actionName, actionConfig.Type))
		}
	}

	return nil
}

// createAction 创建单个动作
func (as *actionService) createAction(name string, config *common.ActionConfig) (*action.ActionDefinition, error) {
	if config == nil {
		as.logger.Error(fmt.Sprintf("动作配置为空 [component=action_service, action_name=%s]", name))
		return nil, errors.NewFlexProxyError(
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidConfiguration,
			"动作配置为空",
		)
	}

	// 检查执行器是否存在
	executor, exists := as.executors[config.Type]
	if !exists {
		as.logger.Error(fmt.Sprintf("未知的动作类型 [component=action_service, action_name=%s, action_type=%s]", name, config.Type))
		return nil, errors.NewFlexProxyError(
			errors.ErrTypeValidation,
			errors.ErrCodeUnsupportedActionType,
			"未知的动作类型",
		)
	}

	// 创建动作实例
	act := &action.ActionDefinition{
		Name:         name,
		Type:         config.Type,
		Enabled:      true, // 默认启用
		Parameters:   config.Params,
		Description:  "", // ActionConfig中没有Description字段
		Executor:     executor,
		CreatedAt:    time.Now(),
		LastExecuted: time.Time{},
		ExecuteCount: 0,
	}

	// 验证动作配置
	if err := as.validateAction(act); err != nil {
		as.logger.Error(fmt.Sprintf("动作验证失败 [component=action_service, action_name=%s, error=%s]", name, err.Error()))
		return nil, errors.WrapError(
			err,
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"动作验证失败",
		)
	}

	return act, nil
}

// validateAction 验证动作配置
func (as *actionService) validateAction(act *action.ActionDefinition) error {
	if act.Name == "" {
		return errors.NewFlexProxyError(
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidParameter,
			"动作名称不能为空",
		)
	}

	if act.Type == "" {
		return errors.NewFlexProxyError(
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidParameter,
			"动作类型不能为空",
		)
	}

	if act.Executor == nil {
		return errors.NewFlexProxyError(
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidParameter,
			"动作执行器不能为空",
		)
	}

	// 让执行器验证参数
	if err := act.Executor.Validate(act.Parameters); err != nil {
		return errors.WrapError(
			err,
			errors.ErrTypeValidation,
			errors.ErrCodeParameterValidationFailed,
			"参数验证失败",
		)
	}

	return nil
}

// Start 启动动作服务
func (as *actionService) Start() error {
	as.mu.Lock()
	defer as.mu.Unlock()

	as.logger.Info(fmt.Sprintf("开始启动动作服务 [component=action_service, action=start, workers=%d]", as.workers))

	if as.running {
		as.logger.Error("动作服务已在运行 [component=action_service]")
		return errors.NewFlexProxyError(
			errors.ErrTypeOperation,
			errors.ErrCodeServiceAlreadyRunning,
			"动作服务已在运行",
		)
	}

	as.running = true

	// 启动工作协程
	for i := 0; i < as.workers; i++ {
		go as.worker(i)
	}

	as.logger.Info(fmt.Sprintf("动作服务已启动 [component=action_service, workers_count=%d, queue_size=%d]", as.workers, cap(as.queue)))
	return nil
}

// worker 工作协程
func (as *actionService) worker(id int) {
	as.logger.Debug(fmt.Sprintf("动作工作协程开始运行 [component=action_service, worker_id=%d]", id))

	for {
		select {
		case <-as.ctx.Done():
			as.logger.Debug(fmt.Sprintf("动作工作协程停止运行 [component=action_service, worker_id=%d]", id))
			return
		case req := <-as.queue:
			as.executeActionRequest(req)
		}
	}
}

// executeActionRequest 执行动作请求
func (as *actionService) executeActionRequest(req *action.ExecutionRequest) {
	start := time.Now()
	traceID := trace.GenerateTraceID()

	as.logger.Debug(fmt.Sprintf("开始执行动作请求 [component=action_service, action_name=%s, trace_id=%s]", req.ActionName, traceID))

	// 获取动作
	as.mu.RLock()
	act, exists := as.actions[req.ActionName]
	as.mu.RUnlock()

	if !exists {
		err := errors.NewFlexProxyError(
			errors.ErrTypeValidation,
			errors.ErrCodeResourceNotFound,
			"动作不存在",
		)
		as.logger.Error(fmt.Sprintf("动作不存在 [component=action_service, action_name=%s, trace_id=%s]", req.ActionName, traceID))
		if req.Callback != nil {
			req.Callback(err)
		}
		return
	}

	// 检查动作是否启用
	if !act.Enabled {
		err := errors.NewFlexProxyError(
			errors.ErrTypeOperation,
			errors.ErrCodeActionDisabled,
			"动作已禁用",
		)
		as.logger.Debug(fmt.Sprintf("动作已禁用，跳过执行 [component=action_service, action_name=%s, trace_id=%s]", req.ActionName, traceID))
		if req.Callback != nil {
			req.Callback(err)
		}
		return
	}

	// 执行动作
	err := act.Executor.Execute(req.Context, act.Parameters)

	// 更新统计信息
	as.mu.Lock()
	act.LastExecuted = time.Now()
	act.ExecuteCount++
	if err != nil {
		act.ErrorCount++
	}
	as.mu.Unlock()

	duration := time.Since(start)

	if err != nil {
		as.logger.Error(fmt.Sprintf("动作执行失败 [component=action_service, action_name=%s, trace_id=%s, duration_ms=%d, error=%s]", req.ActionName, traceID, duration.Milliseconds(), err.Error()))
	} else {
		as.logger.Debug(fmt.Sprintf("动作执行成功 [component=action_service, action_name=%s, trace_id=%s, duration_ms=%d]", req.ActionName, traceID, duration.Milliseconds()))
	}

	// 调用回调
	if req.Callback != nil {
		req.Callback(err)
	}
}

// Stop 停止动作服务
func (as *actionService) Stop() error {
	as.mu.Lock()
	defer as.mu.Unlock()

	if !as.running {
		return nil
	}

	as.logger.Info("正在停止动作服务 [component=action_service]")

	as.running = false

	// 取消上下文，停止所有工作协程
	if as.cancel != nil {
		as.cancel()
	}

	// 关闭队列
	close(as.queue)

	as.logger.Info("动作服务已停止 [component=action_service]")
	return nil
}

// ExecuteActionByName 根据动作名称执行动作（实现container.ActionService接口）
func (as *actionService) ExecuteActionByName(actionName string, params map[string]interface{}) error {
	traceID := trace.GenerateTraceID()

	if !as.running {
		err := errors.NewFlexProxyError(
			errors.ErrTypeOperation,
			errors.ErrCodeServiceNotRunning,
			"动作服务未运行",
		)
		as.logger.Error(fmt.Sprintf("动作服务未运行 [component=action_service, action_name=%s, trace_id=%s]", actionName, traceID))
		return err
	}

	// 查找动作
	as.mu.RLock()
	act, exists := as.actions[actionName]
	as.mu.RUnlock()

	if !exists {
		err := errors.NewFlexProxyError(
			errors.ErrTypeValidation,
			errors.ErrCodeResourceNotFound,
			fmt.Sprintf("动作不存在: %s", actionName),
		)
		as.logger.Error(fmt.Sprintf("动作不存在 [component=action_service, action_name=%s, trace_id=%s]", actionName, traceID))
		return err
	}

	// 检查动作是否启用
	if !act.Enabled {
		err := errors.NewFlexProxyError(
			errors.ErrTypeOperation,
			errors.ErrCodeActionDisabled,
			"动作已禁用",
		)
		as.logger.Debug(fmt.Sprintf("动作已禁用，跳过执行 [component=action_service, action_name=%s, trace_id=%s]", actionName, traceID))
		return err
	}

	// 合并参数
	finalParams := make(map[string]interface{})
	for k, v := range act.Parameters {
		finalParams[k] = v
	}
	for k, v := range params {
		finalParams[k] = v
	}

	// 执行动作
	as.logger.Debug(fmt.Sprintf("开始执行动作 [component=action_service, action_name=%s, trace_id=%s]", actionName, traceID))

	err := act.Executor.Execute(as.ctx, finalParams)

	// 更新统计信息
	as.mu.Lock()
	act.LastExecuted = time.Now()
	act.ExecuteCount++
	if err != nil {
		act.ErrorCount++
	}
	as.mu.Unlock()

	if err != nil {
		as.logger.Error(fmt.Sprintf("动作执行失败 [component=action_service, action_name=%s, trace_id=%s, error=%s]", actionName, traceID, err.Error()))
	} else {
		as.logger.Debug(fmt.Sprintf("动作执行成功 [component=action_service, action_name=%s, trace_id=%s]", actionName, traceID))
	}

	return err
}

// ExecuteAction 执行指定动作（实现container.ActionService接口）
func (as *actionService) ExecuteAction(actionType string, params map[string]interface{}) error {
	traceID := trace.GenerateTraceID()

	if !as.running {
		err := errors.NewFlexProxyError(
			errors.ErrTypeOperation,
			errors.ErrCodeServiceNotRunning,
			"动作服务未运行",
		)
		as.logger.Error(fmt.Sprintf("动作服务未运行 [component=action_service, action_type=%s, trace_id=%s]", actionType, traceID))
		return err
	}

	// 查找执行器
	as.mu.RLock()
	executor, exists := as.executors[actionType]
	as.mu.RUnlock()

	if !exists {
		err := errors.NewFlexProxyError(
			errors.ErrTypeValidation,
			errors.ErrCodeUnsupportedActionType,
			fmt.Sprintf("未知的动作类型: %s", actionType),
		)
		as.logger.Error(fmt.Sprintf("未知的动作类型 [component=action_service, action_type=%s, trace_id=%s]", actionType, traceID))
		return err
	}

	// 直接执行动作
	as.logger.Debug(fmt.Sprintf("开始执行动作 [component=action_service, action_type=%s, trace_id=%s]", actionType, traceID))

	err := executor.Execute(as.ctx, params)
	if err != nil {
		as.logger.Error(fmt.Sprintf("动作执行失败 [component=action_service, action_type=%s, trace_id=%s, error=%s]", actionType, traceID, err.Error()))
	} else {
		as.logger.Debug(fmt.Sprintf("动作执行成功 [component=action_service, action_type=%s, trace_id=%s]", actionType, traceID))
	}

	return err
}

// ExecuteActionAsync 异步执行指定动作
func (as *actionService) ExecuteActionAsync(ctx context.Context, actionName string, callback func(error)) error {
	traceID := trace.GenerateTraceID()

	if !as.running {
		err := errors.NewFlexProxyError(
			errors.ErrTypeOperation,
			errors.ErrCodeServiceNotRunning,
			"动作服务未运行",
		)
		as.logger.Error(fmt.Sprintf("动作服务未运行 [component=action_service, action_name=%s, trace_id=%s]", actionName, traceID))
		return err
	}

	req := &action.ExecutionRequest{
		ActionName: actionName,
		Context:    ctx,
		Callback:   callback,
		Timestamp:  time.Now(),
	}

	select {
	case as.queue <- req:
		as.logger.Debug(fmt.Sprintf("动作请求已加入队列 [component=action_service, action_name=%s, trace_id=%s]", actionName, traceID))
		return nil
	default:
		err := errors.NewFlexProxyError(
			errors.ErrTypeOperation,
			errors.ErrCodeQueueFull,
			"动作队列已满",
		)
		as.logger.Error(fmt.Sprintf("动作队列已满 [component=action_service, action_name=%s, trace_id=%s, queue_size=%d]", actionName, traceID, len(as.queue)))
		return err
	}
}

// ExecuteActionSync 同步执行动作
func (as *actionService) ExecuteActionSync(ctx context.Context, actionName string) error {
	done := make(chan error, 1)

	err := as.ExecuteActionAsync(ctx, actionName, func(err error) {
		done <- err
	})

	if err != nil {
		return err
	}

	select {
	case err := <-done:
		return err
	case <-ctx.Done():
		return ctx.Err()
	}
}

// GetAction 获取指定动作
func (as *actionService) GetAction(name string) (interface{}, bool) {
	as.mu.RLock()
	defer as.mu.RUnlock()

	act, exists := as.actions[name]
	as.logger.Debug(fmt.Sprintf("获取动作定义 [component=action_service, action_name=%s, exists=%t]", name, exists))
	return act, exists
}

// GetAllActions 获取所有动作
func (as *actionService) GetAllActions() map[string]*action.ActionDefinition {
	as.mu.RLock()
	defer as.mu.RUnlock()

	// 创建副本以避免并发问题
	result := make(map[string]*action.ActionDefinition)
	for name, act := range as.actions {
		result[name] = act
	}

	as.logger.Debug(fmt.Sprintf("获取所有动作定义 [component=action_service, actions_count=%d]", len(result)))

	return result
}

// EnableAction 启用动作
func (as *actionService) EnableAction(name string) error {
	traceID := trace.GenerateTraceID()

	as.mu.Lock()
	defer as.mu.Unlock()

	act, exists := as.actions[name]
	if !exists {
		err := errors.NewFlexProxyError(
			errors.ErrTypeValidation,
			errors.ErrCodeResourceNotFound,
			"动作不存在",
		)
		as.logger.Error(fmt.Sprintf("动作不存在 [component=action_service, action_name=%s, trace_id=%s]", name, traceID))
		return err
	}

	act.Enabled = true
	as.logger.Info(fmt.Sprintf("动作已启用 [component=action_service, action_name=%s, trace_id=%s]", name, traceID))
	return nil
}

// DisableAction 禁用动作
func (as *actionService) DisableAction(name string) error {
	traceID := trace.GenerateTraceID()

	as.mu.Lock()
	defer as.mu.Unlock()

	act, exists := as.actions[name]
	if !exists {
		err := errors.NewFlexProxyError(
			errors.ErrTypeValidation,
			errors.ErrCodeResourceNotFound,
			"动作不存在",
		)
		as.logger.Error(fmt.Sprintf("动作不存在 [component=action_service, action_name=%s, trace_id=%s]", name, traceID))
		return err
	}

	act.Enabled = false
	as.logger.Info(fmt.Sprintf("动作已禁用 [component=action_service, action_name=%s, trace_id=%s]", name, traceID))
	return nil
}

// RegisterExecutor 注册自定义执行器
func (as *actionService) RegisterExecutor(actionType string, executor action.Executor) error {
	traceID := trace.GenerateTraceID()

	as.mu.Lock()
	defer as.mu.Unlock()

	if _, exists := as.executors[actionType]; exists {
		err := errors.NewFlexProxyError(
			errors.ErrTypeValidation,
			errors.ErrCodeResourceAlreadyExists,
			"执行器已存在",
		)
		as.logger.Error(fmt.Sprintf("执行器已存在 [component=action_service, executor_name=%s, trace_id=%s]", actionType, traceID))
		return err
	}

	as.executors[actionType] = executor
	as.logger.Info(fmt.Sprintf("执行器已注册 [component=action_service, executor_name=%s, trace_id=%s]", actionType, traceID))
	return nil
}

// GetActionStats 获取动作统计信息
func (as *actionService) GetActionStats() map[string]interface{} {
	as.mu.RLock()
	defer as.mu.RUnlock()

	totalActions := len(as.actions)
	enabledActions := 0
	totalExecutions := 0
	totalErrors := 0

	for _, act := range as.actions {
		if act.Enabled {
			enabledActions++
		}
		totalExecutions += act.ExecuteCount
		totalErrors += act.ErrorCount
	}

	stats := map[string]interface{}{
		"total_actions":    totalActions,
		"enabled_actions":  enabledActions,
		"total_executors":  len(as.executors),
		"total_executions": totalExecutions,
		"total_errors":     totalErrors,
		"queue_size":       len(as.queue),
		"workers":          as.workers,
		"running":          as.running,
	}

	as.logger.Debug(fmt.Sprintf("获取动作统计信息 [component=action_service, stats_count=%d]", len(stats)))

	return stats
}

// ExecuteSequence 执行动作序列（实现container.ActionService接口）
func (as *actionService) ExecuteSequence(sequence []string, context interface{}) error {
	traceID := trace.GenerateTraceID()

	if !as.running {
		err := errors.NewFlexProxyError(
			errors.ErrTypeOperation,
			errors.ErrCodeServiceNotRunning,
			"动作服务未运行",
		)
		as.logger.Error(fmt.Sprintf("动作服务未运行 [component=action_service, trace_id=%s]", traceID))
		return err
	}

	as.logger.Info(fmt.Sprintf("开始执行动作序列 [component=action_service, sequence_length=%d, trace_id=%s]", len(sequence), traceID))

	for i, actionName := range sequence {
		// 查找动作
		as.mu.RLock()
		act, exists := as.actions[actionName]
		as.mu.RUnlock()

		if !exists {
			err := errors.NewFlexProxyError(
				errors.ErrTypeValidation,
				errors.ErrCodeResourceNotFound,
				fmt.Sprintf("序列中的动作不存在: %s (位置: %d)", actionName, i),
			)
			as.logger.Error(fmt.Sprintf("序列中的动作不存在 [component=action_service, action_name=%s, sequence_index=%d, trace_id=%s]", actionName, i, traceID))
			return err
		}

		if !act.Enabled {
			as.logger.Debug(fmt.Sprintf("跳过已禁用的动作 [component=action_service, action_name=%s, sequence_index=%d, trace_id=%s]", actionName, i, traceID))
			continue
		}

		// 执行动作
		as.logger.Debug(fmt.Sprintf("执行序列中的动作 [component=action_service, action_name=%s, sequence_index=%d, trace_id=%s]", actionName, i, traceID))

		if err := act.Executor.Execute(as.ctx, act.Parameters); err != nil {
			as.logger.Error(fmt.Sprintf("序列执行失败 [component=action_service, action_name=%s, sequence_index=%d, trace_id=%s, error=%s]", actionName, i, traceID, err.Error()))
			return errors.WrapError(
				err,
				errors.ErrTypeOperation,
				errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("执行动作序列失败 [%s]", actionName),
			)
		}
	}

	as.logger.Info(fmt.Sprintf("动作序列执行完成 [component=action_service, sequence_length=%d, trace_id=%s]", len(sequence), traceID))

	return nil
}

// RegisterAction 注册动作（实现container.ActionService接口）
func (as *actionService) RegisterAction(name string, actionInterface interface{}) error {
	traceID := trace.GenerateTraceID()

	as.mu.Lock()
	defer as.mu.Unlock()

	// 检查动作是否已存在
	if _, exists := as.actions[name]; exists {
		err := errors.NewFlexProxyError(
			errors.ErrTypeValidation,
			errors.ErrCodeResourceAlreadyExists,
			"动作已存在",
		)
		as.logger.Error(fmt.Sprintf("动作已存在 [component=action_service, action_name=%s, trace_id=%s]", name, traceID))
		return err
	}

	// 将接口转换为ActionDefinition
	actionDef, ok := actionInterface.(*action.ActionDefinition)
	if !ok {
		err := errors.NewFlexProxyError(
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidParameter,
			"无效的动作定义类型",
		)
		as.logger.Error(fmt.Sprintf("无效的动作定义类型 [component=action_service, action_name=%s, trace_id=%s]", name, traceID))
		return err
	}

	// 设置动作名称
	actionDef.Name = name

	// 验证动作
	if err := as.validateAction(actionDef); err != nil {
		as.logger.Error(fmt.Sprintf("动作验证失败 [component=action_service, action_name=%s, trace_id=%s, error=%s]", name, traceID, err.Error()))
		return errors.WrapError(
			err,
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"动作验证失败",
		)
	}

	// 注册动作
	as.actions[name] = actionDef

	as.logger.Info(fmt.Sprintf("动作已注册 [component=action_service, action_name=%s, trace_id=%s]", name, traceID))
	return nil
}

// ReloadConfig 重新加载配置
func (as *actionService) ReloadConfig(config *common.Config) error {
	traceID := trace.GenerateTraceID()

	as.mu.Lock()
	defer as.mu.Unlock()

	as.logger.Info(fmt.Sprintf("开始重新加载动作配置 [component=action_service, trace_id=%s]", traceID))

	// 清空现有动作（保留执行器）
	as.actions = make(map[string]*action.ActionDefinition)

	// 重新初始化
	as.config = config

	if err := as.initializeActions(); err != nil {
		as.logger.Error(fmt.Sprintf("重新加载动作配置失败 [component=action_service, trace_id=%s, error=%s]", traceID, err.Error()))
		return errors.WrapError(
			err,
			errors.ErrTypeOperation,
			errors.ErrCodeConfigReloadFailed,
			"重新加载动作配置失败",
		)
	}

	as.logger.Info(fmt.Sprintf("动作配置已重新加载 [component=action_service, actions_count=%d, trace_id=%s]", len(as.actions), traceID))
	return nil
}
