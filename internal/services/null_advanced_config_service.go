// Package services 提供各种服务实现
package services

import (
	"github.com/flexp/flexp/internal/interfaces"
	"github.com/mbndr/logo"
)

// nullAdvancedConfigService 空高级配置服务实现，用于禁用高级功能时
type nullAdvancedConfigService struct {
	logger *logo.Logger
}

// NewNullAdvancedConfigService 创建空高级配置服务实例
func NewNullAdvancedConfigService(log *logo.Logger) interfaces.AdvancedConfigService {
	if log == nil {
		log = logo.NewSimpleLogger(nil, logo.INFO, "null-advanced", false)
	}

	return &nullAdvancedConfigService{
		logger: log,
	}
}

// RestoreConfig 恢复配置（空实现）
func (nacs *nullAdvancedConfigService) RestoreConfig(backupPath string) error {
	return nil
}

// Start 启动服务（空实现）
func (nacs *nullAdvancedConfigService) Start() error {
	nacs.logger.Info("空高级配置服务已启动（无实际功能）")
	return nil
}

// Stop 停止服务（空实现）
func (nacs *nullAdvancedConfigService) Stop() error {
	nacs.logger.Info("空高级配置服务已停止")
	return nil
}

// GetConfig 获取配置（空实现）
func (nacs *nullAdvancedConfigService) GetConfig() map[string]interface{} {
	return map[string]interface{}{
		"enabled": false,
		"status":  "disabled",
	}
}

// UpdateConfig 更新配置（空实现）
func (nacs *nullAdvancedConfigService) UpdateConfig(config map[string]interface{}) error {
	return nil
}

// GetConfigValue 获取配置值（空实现）
func (nacs *nullAdvancedConfigService) GetConfigValue(key string) (interface{}, error) {
	return nil, nil
}

// SetConfigValue 设置配置值（空实现）
func (nacs *nullAdvancedConfigService) SetConfigValue(key string, value interface{}) error {
	return nil
}

// AddWatcher 添加监视器（空实现）
func (nacs *nullAdvancedConfigService) AddWatcher(id string, pattern string, callback func(string, interface{}, interface{})) error {
	return nil
}

// RemoveWatcher 移除监视器（空实现）
func (nacs *nullAdvancedConfigService) RemoveWatcher(id string) error {
	return nil
}

// AddValidator 添加验证器（空实现）
func (nacs *nullAdvancedConfigService) AddValidator(key string, validator func(interface{}) error) error {
	return nil
}

// GetHistory 获取历史记录（空实现）
func (nacs *nullAdvancedConfigService) GetHistory(limit int) []interface{} {
	return []interface{}{}
}

// GetStats 获取统计信息（空实现）
func (nacs *nullAdvancedConfigService) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"enabled": false,
		"status":  "disabled",
	}
}

// ResetConfig 重置配置（空实现）
func (nacs *nullAdvancedConfigService) ResetConfig() error {
	return nil
}

// ExportConfig 导出配置（空实现）
func (nacs *nullAdvancedConfigService) ExportConfig(format string) ([]byte, error) {
	return []byte("{}"), nil
}

// ImportConfig 导入配置（空实现）
func (nacs *nullAdvancedConfigService) ImportConfig(data []byte, format string) error {
	return nil
}
