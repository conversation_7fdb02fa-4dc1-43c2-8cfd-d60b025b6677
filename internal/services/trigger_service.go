package services

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/dop251/goja"
	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/common/constants"
	"github.com/flexp/flexp/common/errors"
	"github.com/flexp/flexp/common/logger"
	"github.com/flexp/flexp/internal/interfaces"
	"github.com/flexp/flexp/internal/trigger"
)

// TriggerStats 触发器统计信息
type TriggerStats struct {
	CreatedAt   time.Time
	LastFired   time.Time
	FireCount   int
	TotalErrors int
}

// triggerService 触发器服务实现
type triggerService struct {
	mu           sync.RWMutex
	triggers     map[string]*trigger.TriggerDefinition
	stats        map[string]*TriggerStats
	config       *common.Config
	logger       logger.Logger
	cacheService interfaces.CacheService
	running      bool
	ctx          context.Context
	cancel       context.CancelFunc
	jsRuntime    *goja.Runtime // JavaScript执行引擎
	jsRuntimeMu  sync.Mutex    // JavaScript引擎锁
}

// NewTriggerService 创建新的触发器服务实例
func NewTriggerService(cacheService interfaces.CacheService, log logger.Logger) interfaces.TriggerService {
	if log == nil {
		log = logger.GetLogger("trigger")
	}

	// 初始化JavaScript运行时
	jsRuntime := goja.New()

	return &triggerService{
		triggers:     make(map[string]*trigger.TriggerDefinition),
		stats:        make(map[string]*TriggerStats),
		logger:       log,
		cacheService: cacheService,
		jsRuntime:    jsRuntime,
	}
}

// Initialize 初始化触发器服务
func (ts *triggerService) Initialize(config interface{}) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	ts.logger.Info("开始初始化触发器服务")

	// 类型断言
	cfg, ok := config.(*common.Config)
	if !ok {
		ts.logger.Error("Initialize: 无效的配置类型")
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidInput,
			"无效的配置类型",
			"触发器服务初始化",
		)
	}

	ts.config = cfg

	// 创建上下文
	ts.ctx, ts.cancel = context.WithCancel(context.Background())

	// 初始化JavaScript运行时
	if err := ts.initializeJavaScriptRuntime(); err != nil {
		ts.logger.Error(fmt.Sprintf("初始化JavaScript运行时失败: %v", err))
		return errors.WrapErrorWithDetails(
			err,
			errors.ErrTypeConfiguration,
			errors.ErrCodeInitializationFailed,
			"初始化JavaScript运行时失败",
			"触发器服务初始化",
		)
	}

	// 初始化触发器
	if err := ts.initializeTriggers(); err != nil {
		ts.logger.Error(fmt.Sprintf("初始化触发器失败: %v", err))
		return errors.WrapErrorWithDetails(
			err,
			errors.ErrTypeConfiguration,
			errors.ErrCodeInitializationFailed,
			"初始化触发器失败",
			"触发器服务初始化",
		)
	}

	ts.logger.Info(fmt.Sprintf("触发器服务已初始化，触发器数量: %d", len(ts.triggers)))
	return nil
}

// RegisterTrigger 注册新的触发器（实现container.TriggerService接口）
func (ts *triggerService) RegisterTrigger(name string, triggerInterface interface{}) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	ts.logger.Debug(fmt.Sprintf("开始注册触发器: %s", name))

	// 类型断言检查
	triggerDef, ok := triggerInterface.(*trigger.TriggerDefinition)
	if !ok {
		ts.logger.Error(fmt.Sprintf("无效的触发器类型: %s", name))
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidInput,
			"无效的触发器类型",
			fmt.Sprintf("触发器名称: %s", name),
		)
	}

	// 设置触发器名称
	triggerDef.Name = name

	// 验证触发器
	if err := ts.validateTrigger(triggerDef); err != nil {
		ts.logger.Error(fmt.Sprintf("触发器验证失败: %s, 错误: %v", name, err))
		return errors.WrapErrorWithDetails(
			err,
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidInput,
			"触发器验证失败",
			fmt.Sprintf("触发器名称: %s", name),
		)
	}

	// 注册触发器
	ts.triggers[name] = triggerDef

	ts.logger.Info(fmt.Sprintf("触发器已注册: %s, 条件数: %d, 动作数: %d", name, len(triggerDef.Conditions), len(triggerDef.Actions)))
	return nil
}

// initializeTriggers 初始化所有触发器
func (ts *triggerService) initializeTriggers() error {
	if ts.config == nil {
		ts.logger.Warn("没有配置，跳过触发器初始化")
		return nil
	}

	ts.logger.Debug(fmt.Sprintf("开始初始化触发器，事件数量: %d", len(ts.config.Events)))

	// 遍历配置中的事件
	successCount := 0
	for _, event := range ts.config.Events {
		// 创建触发器
		trig, err := ts.createTrigger(event.Name, &event)
		if err != nil {
			ts.logger.Error(fmt.Sprintf("创建触发器失败: %s, 错误: %v", event.Name, err))
			continue
		}

		ts.triggers[event.Name] = trig
		successCount++
		ts.logger.Debug(fmt.Sprintf("触发器已创建: %s, 启用状态: %v", event.Name, trig.Enabled))
	}

	ts.logger.Info(fmt.Sprintf("触发器初始化完成，成功: %d, 总数: %d", successCount, len(ts.config.Events)))

	return nil
}

// createTrigger 创建单个触发器
func (ts *triggerService) createTrigger(name string, config *common.EventConfig) (*trigger.TriggerDefinition, error) {
	if config == nil {
		return nil, errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidInput,
			"触发器配置为空",
			fmt.Sprintf("触发器名称: %s", name),
		)
	}

	// 转换条件配置
	var conditions []trigger.ConditionDefinition
	for _, cond := range config.Conditions {
		conditions = append(conditions, trigger.ConditionDefinition{
			Type:     config.TriggerType, // 使用事件的触发器类型
			Field:    cond.Name,          // 使用条件名称作为字段
			Operator: "equals",           // 默认操作符
			Value:    cond.Enable,        // 使用启用状态作为值
			Parameters: map[string]interface{}{
				"name":                     cond.Name,
				"enable":                   cond.Enable,
				"status_codes":             cond.StatusCodes,
				"body_patterns":            cond.BodyPatterns,
				"max_request_time":         cond.MaxRequestTime,
				"connection_timeout":       cond.ConnectionTimeout,
				"min_request_time":         cond.MinRequestTime,
				"url_patterns":             cond.URLPatterns,
				"domain_patterns":          cond.DomainPatterns,
				"request_body_patterns":    cond.RequestBodyPatterns,
				"request_header_patterns":  cond.RequestHeaderPatterns,
				"response_header_patterns": cond.ResponseHeaderPatterns,
				"condition_relation":       cond.ConditionRelation,
				"trigger_id":               cond.TriggerID,
			},
		})
	}

	// 创建触发器实例
	trig := &trigger.TriggerDefinition{
		Name:          name,
		Enabled:       config.Enable,
		Conditions:    conditions,
		Actions:       []string{}, // 需要根据实际需求转换
		CreatedAt:     time.Now(),
		LastTriggered: time.Time{},
		TriggerCount:  0,
	}

	// 验证触发器配置
	if err := ts.validateTrigger(trig); err != nil {
		return nil, errors.WrapErrorWithDetails(
			err,
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidInput,
			"触发器验证失败",
			fmt.Sprintf("触发器名称: %s", name),
		)
	}

	ts.logger.Debug(fmt.Sprintf("触发器创建成功: %s, 条件数: %d, 启用: %v", name, len(trig.Conditions), trig.Enabled))

	return trig, nil
}

// validateTrigger 验证触发器配置
func (ts *triggerService) validateTrigger(trig *trigger.TriggerDefinition) error {
	if trig.Name == "" {
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidInput,
			"触发器名称不能为空",
			"触发器验证",
		)
	}

	if len(trig.Conditions) == 0 {
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidInput,
			"触发器必须至少有一个条件",
			fmt.Sprintf("触发器名称: %s", trig.Name),
		)
	}

	// 注意：暂时移除动作验证，因为某些触发器可能不需要动作
	// if len(trig.Actions) == 0 {
	//     return errors.NewFlexProxyError(...)
	// }

	ts.logger.Debug(fmt.Sprintf("触发器验证通过: %s, 条件数: %d, 动作数: %d", trig.Name, len(trig.Conditions), len(trig.Actions)))

	return nil
}

// Start 启动触发器服务
func (ts *triggerService) Start() error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	if ts.running {
		ts.logger.Warn("触发器服务已在运行")
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeOperation,
			errors.ErrCodeServiceAlreadyRunning,
			"触发器服务已在运行",
			"触发器服务启动",
		)
	}

	ts.logger.Info(fmt.Sprintf("开始启动触发器服务，触发器总数: %d", len(ts.triggers)))

	ts.running = true

	// 启动所有启用的触发器
	startedCount := 0
	for name, trig := range ts.triggers {
		if trig.Enabled {
			go ts.runTrigger(name, trig)
			startedCount++
			ts.logger.Debug(fmt.Sprintf("触发器已启动: %s", name))
		}
	}

	ts.logger.Info(fmt.Sprintf("触发器服务已启动，启动数: %d, 总数: %d", startedCount, len(ts.triggers)))
	return nil
}

// runTrigger 运行单个触发器
func (ts *triggerService) runTrigger(name string, trig *trigger.TriggerDefinition) {
	ts.logger.Debug(fmt.Sprintf("触发器开始运行: %s", name))

	// 初始化触发器统计信息
	ts.mu.Lock()
	if _, exists := ts.stats[name]; !exists {
		ts.stats[name] = &TriggerStats{
			CreatedAt:   time.Now(),
			LastFired:   time.Time{},
			FireCount:   0,
			TotalErrors: 0,
		}
	}
	ts.mu.Unlock()

	for {
		select {
		case <-ts.ctx.Done():
			ts.logger.Debug(fmt.Sprintf("触发器停止运行: %s", name))
			return
		default:
			// 检查触发器是否仍然启用
			ts.mu.RLock()
			enabled := trig.Enabled
			ts.mu.RUnlock()

			if !enabled {
				time.Sleep(constants.DefaultTriggerCheckInterval)
				continue
			}

			// 实现具体的触发器逻辑
			// 对于基于时间的触发器，这里可以检查时间条件
			// 对于基于事件的触发器，这里主要是保持活跃状态
			ts.checkTimerBasedTriggers(name, trig)

			time.Sleep(constants.DefaultTriggerRunInterval)
		}
	}
}

// checkTimerBasedTriggers 检查基于时间的触发器
func (ts *triggerService) checkTimerBasedTriggers(name string, trig *trigger.TriggerDefinition) {
	// 检查是否有基于时间的条件
	for _, condition := range trig.Conditions {
		if condition.Type == "time" || condition.Type == "interval" {
			if ts.evaluateTimeCondition(condition) {
				ts.logger.Info(fmt.Sprintf("时间触发器触发: %s", name))
				ts.fireTrigger(name, trig, nil, nil, nil)
			}
		}
	}
}

// evaluateTimeCondition 评估时间条件
func (ts *triggerService) evaluateTimeCondition(condition trigger.ConditionDefinition) bool {
	now := time.Now()

	switch condition.Operator {
	case "interval":
		// 检查间隔触发
		if intervalStr, ok := condition.Value.(string); ok {
			if duration, err := time.ParseDuration(intervalStr); err == nil {
				// 简单的间隔检查逻辑
				// 这里可以根据需要实现更复杂的间隔逻辑
				return now.Unix()%int64(duration.Seconds()) == 0
			}
		}
	case "at_time":
		// 检查特定时间触发
		if timeStr, ok := condition.Value.(string); ok {
			if targetTime, err := time.Parse("15:04", timeStr); err == nil {
				return now.Hour() == targetTime.Hour() && now.Minute() == targetTime.Minute()
			}
		}
	}

	return false
}

// fireTrigger 触发器触发时的处理
func (ts *triggerService) fireTrigger(name string, trig *trigger.TriggerDefinition, req *http.Request, resp *http.Response, data map[string]interface{}) {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	// 更新触发器统计
	trig.LastTriggered = time.Now()
	trig.TriggerCount++

	// 更新统计信息
	if stat, exists := ts.stats[name]; exists {
		stat.LastFired = time.Now()
		stat.FireCount++
	}

	ts.logger.Info(fmt.Sprintf("触发器已触发: %s, 触发次数: %d, 动作数: %d", name, trig.TriggerCount, len(trig.Actions)))

	// 这里可以添加动作执行逻辑
	// 例如：通知动作服务执行相应的动作
	for _, action := range trig.Actions {
		ts.logger.Debug(fmt.Sprintf("触发器 %s 请求执行动作: %s", name, action))
		// 可以在这里调用动作服务执行动作
	}
}

// Stop 停止触发器服务
func (ts *triggerService) Stop() {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	if !ts.running {
		ts.logger.Debug("触发器服务未运行")
		return
	}

	ts.logger.Info(fmt.Sprintf("开始停止触发器服务，触发器数量: %d", len(ts.triggers)))

	ts.running = false

	// 取消上下文，停止所有触发器
	if ts.cancel != nil {
		ts.cancel()
	}

	ts.logger.Info("触发器服务已停止")
}

// EvaluateTriggers 评估触发器
func (ts *triggerService) EvaluateTriggers(req *http.Request, resp *http.Response, data map[string]interface{}) ([]string, error) {
	ts.mu.RLock()
	defer ts.mu.RUnlock()

	ts.logger.Debug(fmt.Sprintf("开始评估触发器: url=%s", req.URL.String()))

	var triggeredActions []string
	evaluatedCount := 0
	triggeredCount := 0

	for name, trig := range ts.triggers {
		if !trig.Enabled {
			continue
		}

		evaluatedCount++

		// 检查触发器条件
		if ts.evaluateConditionsWithHTTP(trig, req, resp, data) {
			triggeredCount++
			trig.TriggerCount++
			trig.LastTriggered = time.Now()
			ts.logger.Info(fmt.Sprintf("触发器已触发: %s, 触发次数: %d", name, trig.TriggerCount))

			// 添加触发的动作
			for _, action := range trig.Actions {
				triggeredActions = append(triggeredActions, action)
			}
		}
	}

	ts.logger.Debug(fmt.Sprintf("触发器评估完成: 评估数=%d, 触发数=%d, 动作数=%d", evaluatedCount, triggeredCount, len(triggeredActions)))
	return triggeredActions, nil
}

// shouldUseEnhancedMatching 检查是否应该使用增强模式匹配
func (ts *triggerService) shouldUseEnhancedMatching(condition trigger.ConditionDefinition) bool {
	// 检查条件参数中是否启用了增强匹配
	if params, ok := condition.Parameters["enhanced_matching"]; ok {
		if enabled, ok := params.(bool); ok {
			return enabled
		}
	}

	// 检查操作符是否需要增强匹配
	enhancedOperators := []string{"advanced_regex", "fuzzy_match", "semantic_match", "pattern_extract"}
	for _, op := range enhancedOperators {
		if condition.Operator == op {
			return true
		}
	}

	return false
}

// evaluateEnhancedBodyCondition 使用增强模式匹配评估Body条件
func (ts *triggerService) evaluateEnhancedBodyCondition(condition trigger.ConditionDefinition, body string) bool {
	// 这里可以集成EnhancedPatternMatcher
	// 目前先实现基础的增强匹配逻辑

	switch condition.Operator {
	case "advanced_regex":
		return ts.evaluateAdvancedRegex(condition, body)
	case "fuzzy_match":
		return ts.evaluateFuzzyMatch(condition, body)
	case "semantic_match":
		return ts.evaluateSemanticMatch(condition, body)
	case "pattern_extract":
		return ts.evaluatePatternExtract(condition, body)
	default:
		// 回退到标准匹配
		return ts.matchStringCondition(condition, body)
	}
}

// evaluateAdvancedRegex 评估高级正则表达式匹配
func (ts *triggerService) evaluateAdvancedRegex(condition trigger.ConditionDefinition, text string) bool {
	pattern, ok := condition.Value.(string)
	if !ok {
		return false
	}

	// 支持多行和忽略大小写标志
	flags := ""
	if params, ok := condition.Parameters["flags"]; ok {
		if flagStr, ok := params.(string); ok {
			flags = flagStr
		}
	}

	// 根据标志调整正则表达式
	if strings.Contains(flags, "i") {
		pattern = "(?i)" + pattern
	}
	if strings.Contains(flags, "m") {
		pattern = "(?m)" + pattern
	}
	if strings.Contains(flags, "s") {
		pattern = "(?s)" + pattern
	}

	matched, err := regexp.MatchString(pattern, text)
	if err != nil {
		ts.logger.Error(fmt.Sprintf("高级正则表达式匹配错误: %v", err))
		return false
	}

	return matched
}

// evaluateFuzzyMatch 评估模糊匹配
func (ts *triggerService) evaluateFuzzyMatch(condition trigger.ConditionDefinition, text string) bool {
	pattern, ok := condition.Value.(string)
	if !ok {
		return false
	}

	// 获取相似度阈值
	threshold := 0.8 // 默认阈值
	if params, ok := condition.Parameters["threshold"]; ok {
		if thresholdFloat, ok := params.(float64); ok {
			threshold = thresholdFloat
		}
	}

	// 简单的模糊匹配实现（基于编辑距离）
	similarity := ts.calculateSimilarity(pattern, text)
	return similarity >= threshold
}

// evaluateSemanticMatch 评估语义匹配
func (ts *triggerService) evaluateSemanticMatch(condition trigger.ConditionDefinition, text string) bool {
	// 这里可以集成更复杂的语义匹配算法
	// 目前实现简单的关键词匹配
	pattern, ok := condition.Value.(string)
	if !ok {
		return false
	}

	// 获取关键词列表
	keywords := strings.Fields(strings.ToLower(pattern))
	textLower := strings.ToLower(text)

	// 检查关键词匹配度
	matchedCount := 0
	for _, keyword := range keywords {
		if strings.Contains(textLower, keyword) {
			matchedCount++
		}
	}

	// 获取匹配阈值
	threshold := 0.5 // 默认50%的关键词需要匹配
	if params, ok := condition.Parameters["keyword_threshold"]; ok {
		if thresholdFloat, ok := params.(float64); ok {
			threshold = thresholdFloat
		}
	}

	matchRatio := float64(matchedCount) / float64(len(keywords))
	return matchRatio >= threshold
}

// evaluatePatternExtract 评估模式提取
func (ts *triggerService) evaluatePatternExtract(condition trigger.ConditionDefinition, text string) bool {
	pattern, ok := condition.Value.(string)
	if !ok {
		return false
	}

	// 使用正则表达式提取模式
	re, err := regexp.Compile(pattern)
	if err != nil {
		ts.logger.Error(fmt.Sprintf("模式提取正则表达式编译错误: %v", err))
		return false
	}

	// 查找匹配项
	matches := re.FindAllStringSubmatch(text, -1)

	// 获取最小匹配数量
	minMatches := 1 // 默认至少需要1个匹配
	if params, ok := condition.Parameters["min_matches"]; ok {
		if minInt, ok := params.(int); ok {
			minMatches = minInt
		}
	}

	return len(matches) >= minMatches
}

// calculateSimilarity 计算两个字符串的相似度
func (ts *triggerService) calculateSimilarity(s1, s2 string) float64 {
	// 简单的相似度计算（基于最长公共子序列）
	if len(s1) == 0 && len(s2) == 0 {
		return 1.0
	}
	if len(s1) == 0 || len(s2) == 0 {
		return 0.0
	}

	// 计算编辑距离
	distance := ts.levenshteinDistance(s1, s2)
	maxLen := len(s1)
	if len(s2) > maxLen {
		maxLen = len(s2)
	}

	// 转换为相似度
	similarity := 1.0 - float64(distance)/float64(maxLen)
	if similarity < 0 {
		similarity = 0
	}

	return similarity
}

// levenshteinDistance 计算编辑距离
func (ts *triggerService) levenshteinDistance(s1, s2 string) int {
	len1, len2 := len(s1), len(s2)

	// 创建距离矩阵
	dp := make([][]int, len1+1)
	for i := range dp {
		dp[i] = make([]int, len2+1)
	}

	// 初始化第一行和第一列
	for i := 0; i <= len1; i++ {
		dp[i][0] = i
	}
	for j := 0; j <= len2; j++ {
		dp[0][j] = j
	}

	// 填充距离矩阵
	for i := 1; i <= len1; i++ {
		for j := 1; j <= len2; j++ {
			if s1[i-1] == s2[j-1] {
				dp[i][j] = dp[i-1][j-1]
			} else {
				dp[i][j] = 1 + minThree(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])
			}
		}
	}

	return dp[len1][len2]
}

// minThree 返回三个整数中的最小值
func minThree(a, b, c int) int {
	if a <= b && a <= c {
		return a
	}
	if b <= c {
		return b
	}
	return c
}

// evaluateEnhancedURLCondition 使用增强模式匹配评估URL条件
func (ts *triggerService) evaluateEnhancedURLCondition(condition trigger.ConditionDefinition, url string, req *http.Request) bool {
	switch condition.Operator {
	case "advanced_regex":
		return ts.evaluateAdvancedRegex(condition, url)
	case "fuzzy_match":
		return ts.evaluateFuzzyMatch(condition, url)
	case "semantic_match":
		return ts.evaluateSemanticMatch(condition, url)
	case "pattern_extract":
		return ts.evaluatePatternExtract(condition, url)
	case "url_component":
		return ts.evaluateURLComponentMatch(condition, req)
	default:
		// 回退到标准匹配
		return ts.matchStringCondition(condition, url)
	}
}

// evaluateEnhancedDomainCondition 使用增强模式匹配评估域名条件
func (ts *triggerService) evaluateEnhancedDomainCondition(condition trigger.ConditionDefinition, domain string) bool {
	switch condition.Operator {
	case "advanced_regex":
		return ts.evaluateAdvancedRegex(condition, domain)
	case "fuzzy_match":
		return ts.evaluateFuzzyMatch(condition, domain)
	case "semantic_match":
		return ts.evaluateSemanticMatch(condition, domain)
	case "pattern_extract":
		return ts.evaluatePatternExtract(condition, domain)
	case "domain_hierarchy":
		return ts.evaluateDomainHierarchyMatch(condition, domain)
	default:
		// 回退到标准匹配
		return ts.matchStringCondition(condition, domain)
	}
}

// evaluateURLComponentMatch 评估URL组件匹配
func (ts *triggerService) evaluateURLComponentMatch(condition trigger.ConditionDefinition, req *http.Request) bool {
	if req == nil {
		return false
	}

	// 获取要匹配的组件类型
	component, ok := condition.Parameters["component"].(string)
	if !ok {
		component = "full" // 默认匹配完整URL
	}

	pattern, ok := condition.Value.(string)
	if !ok {
		return false
	}

	var targetValue string
	switch component {
	case "scheme":
		targetValue = req.URL.Scheme
	case "host":
		targetValue = req.URL.Host
	case "path":
		targetValue = req.URL.Path
	case "query":
		targetValue = req.URL.RawQuery
	case "fragment":
		targetValue = req.URL.Fragment
	case "port":
		targetValue = req.URL.Port()
	default:
		targetValue = req.URL.String()
	}

	// 使用正则表达式匹配
	matched, err := regexp.MatchString(pattern, targetValue)
	if err != nil {
		ts.logger.Error(fmt.Sprintf("URL组件匹配正则表达式错误: %v", err))
		return false
	}

	return matched
}

// evaluateDomainHierarchyMatch 评估域名层次匹配
func (ts *triggerService) evaluateDomainHierarchyMatch(condition trigger.ConditionDefinition, domain string) bool {
	pattern, ok := condition.Value.(string)
	if !ok {
		return false
	}

	// 获取匹配级别
	level, ok := condition.Parameters["level"].(string)
	if !ok {
		level = "exact" // 默认精确匹配
	}

	switch level {
	case "subdomain":
		// 匹配子域名
		return strings.HasSuffix(domain, "."+pattern) || domain == pattern
	case "parent":
		// 匹配父域名
		parts := strings.Split(domain, ".")
		if len(parts) > 1 {
			parentDomain := strings.Join(parts[1:], ".")
			return parentDomain == pattern
		}
		return false
	case "wildcard":
		// 通配符匹配
		matched, err := regexp.MatchString(strings.ReplaceAll(pattern, "*", ".*"), domain)
		if err != nil {
			ts.logger.Error(fmt.Sprintf("域名通配符匹配错误: %v", err))
			return false
		}
		return matched
	default:
		// 精确匹配
		return domain == pattern
	}
}

// initializeJavaScriptRuntime 初始化JavaScript运行时环境
func (ts *triggerService) initializeJavaScriptRuntime() error {
	ts.jsRuntimeMu.Lock()
	defer ts.jsRuntimeMu.Unlock()

	// 设置全局对象和函数
	ts.jsRuntime.Set("console", map[string]interface{}{
		"log": func(args ...interface{}) {
			ts.logger.Info(fmt.Sprintf("JS: %v", args))
		},
		"error": func(args ...interface{}) {
			ts.logger.Error(fmt.Sprintf("JS Error: %v", args))
		},
		"warn": func(args ...interface{}) {
			ts.logger.Warn(fmt.Sprintf("JS Warn: %v", args))
		},
	})

	// 设置HTTP相关工具函数
	ts.jsRuntime.Set("http", map[string]interface{}{
		"get":  ts.jsHTTPGet,
		"post": ts.jsHTTPPost,
	})

	// 设置字符串处理函数
	ts.jsRuntime.Set("utils", map[string]interface{}{
		"contains":  strings.Contains,
		"hasPrefix": strings.HasPrefix,
		"hasSuffix": strings.HasSuffix,
		"toLower":   strings.ToLower,
		"toUpper":   strings.ToUpper,
		"trim":      strings.TrimSpace,
		"split":     strings.Split,
		"join":      strings.Join,
		"replace":   strings.ReplaceAll,
	})

	// 设置正则表达式函数
	ts.jsRuntime.Set("regex", map[string]interface{}{
		"match": func(pattern, text string) bool {
			matched, _ := regexp.MatchString(pattern, text)
			return matched
		},
		"find": func(pattern, text string) []string {
			re, err := regexp.Compile(pattern)
			if err != nil {
				return nil
			}
			return re.FindAllString(text, -1)
		},
	})

	// 设置时间相关函数
	ts.jsRuntime.Set("time", map[string]interface{}{
		"now": func() int64 {
			return time.Now().Unix()
		},
		"format": func(timestamp int64, layout string) string {
			return time.Unix(timestamp, 0).Format(layout)
		},
		"parse": func(value, layout string) int64 {
			t, err := time.Parse(layout, value)
			if err != nil {
				return 0
			}
			return t.Unix()
		},
	})

	ts.logger.Debug("JavaScript运行时环境已初始化")
	return nil
}

// jsHTTPGet JavaScript HTTP GET请求函数
func (ts *triggerService) jsHTTPGet(url string) map[string]interface{} {
	resp, err := http.Get(url)
	if err != nil {
		return map[string]interface{}{
			"error":  err.Error(),
			"status": 0,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return map[string]interface{}{
			"error":  err.Error(),
			"status": resp.StatusCode,
		}
	}

	return map[string]interface{}{
		"status":  resp.StatusCode,
		"body":    string(body),
		"headers": resp.Header,
	}
}

// jsHTTPPost JavaScript HTTP POST请求函数
func (ts *triggerService) jsHTTPPost(url, data string) map[string]interface{} {
	resp, err := http.Post(url, "application/json", strings.NewReader(data))
	if err != nil {
		return map[string]interface{}{
			"error":  err.Error(),
			"status": 0,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return map[string]interface{}{
			"error":  err.Error(),
			"status": resp.StatusCode,
		}
	}

	return map[string]interface{}{
		"status":  resp.StatusCode,
		"body":    string(body),
		"headers": resp.Header,
	}
}

// executeJavaScript 执行JavaScript代码
func (ts *triggerService) executeJavaScript(script string, context map[string]interface{}) (interface{}, error) {
	ts.jsRuntimeMu.Lock()
	defer ts.jsRuntimeMu.Unlock()

	// 设置上下文变量
	for key, value := range context {
		ts.jsRuntime.Set(key, value)
	}

	// 执行脚本
	value, err := ts.jsRuntime.RunString(script)
	if err != nil {
		ts.logger.Error(fmt.Sprintf("JavaScript执行错误: %v", err))
		return nil, err
	}

	return value.Export(), nil
}

// evaluateJavaScriptCondition 评估JavaScript条件
func (ts *triggerService) evaluateJavaScriptCondition(condition trigger.ConditionDefinition, req *http.Request, resp *http.Response) (bool, map[string]interface{}) {
	matchData := make(map[string]interface{})

	// 准备JavaScript执行上下文
	context := map[string]interface{}{
		"condition": condition,
	}

	// 添加请求信息
	if req != nil {
		context["request"] = map[string]interface{}{
			"url":     req.URL.String(),
			"method":  req.Method,
			"headers": req.Header,
			"host":    req.Host,
			"path":    req.URL.Path,
			"query":   req.URL.RawQuery,
		}

		// 尝试读取请求体
		if req.Body != nil {
			body, err := io.ReadAll(req.Body)
			if err == nil {
				context["request"].(map[string]interface{})["body"] = string(body)
				// 重新设置body
				req.Body = io.NopCloser(strings.NewReader(string(body)))
			}
		}
	}

	// 添加响应信息
	if resp != nil {
		context["response"] = map[string]interface{}{
			"status":  resp.StatusCode,
			"headers": resp.Header,
		}

		// 尝试读取响应体
		if resp.Body != nil {
			body, err := io.ReadAll(resp.Body)
			if err == nil {
				context["response"].(map[string]interface{})["body"] = string(body)
				// 重新设置body
				resp.Body = io.NopCloser(strings.NewReader(string(body)))
			}
		}
	}

	// 获取JavaScript代码
	script, ok := condition.Value.(string)
	if !ok {
		ts.logger.Error("JavaScript条件值必须是字符串")
		return false, matchData
	}

	// 执行JavaScript代码
	result, err := ts.executeJavaScript(script, context)
	if err != nil {
		ts.logger.Error(fmt.Sprintf("JavaScript条件执行失败: %v", err))
		return false, matchData
	}

	// 转换结果为布尔值
	matched := false
	switch v := result.(type) {
	case bool:
		matched = v
	case int64:
		matched = v != 0
	case float64:
		matched = v != 0
	case string:
		matched = v != ""
	default:
		matched = result != nil
	}

	// 记录匹配数据
	matchData["js_result"] = result
	matchData["js_matched"] = matched

	return matched, matchData
}

// evaluateConditions 评估触发器条件
func (ts *triggerService) evaluateConditions(trig *trigger.TriggerDefinition, eventType string, data map[string]interface{}) bool {
	for _, condition := range trig.Conditions {
		if condition.Type == eventType {
			// 简单的条件匹配逻辑
			if value, exists := data[condition.Field]; exists {
				if condition.Operator == "equals" && value == condition.Value {
					return true
				}
			}
		}
	}
	return false
}

// evaluateConditionsWithHTTP 基于HTTP请求和响应评估触发器条件
func (ts *triggerService) evaluateConditionsWithHTTP(trig *trigger.TriggerDefinition, req *http.Request, resp *http.Response, data map[string]interface{}) bool {
	for _, condition := range trig.Conditions {
		switch condition.Type {
		case "url":
			if condition.Operator == "contains" && strings.Contains(req.URL.String(), condition.Value.(string)) {
				return true
			}
		case "method":
			if condition.Operator == "equals" && req.Method == condition.Value.(string) {
				return true
			}
		case "status_code":
			if resp != nil && condition.Operator == "equals" && resp.StatusCode == condition.Value.(int) {
				return true
			}
		case "header":
			if headerValue := req.Header.Get(condition.Field); headerValue != "" {
				if condition.Operator == "equals" && headerValue == condition.Value.(string) {
					return true
				}
			}
		default:
			// 回退到原有的条件评估逻辑
			if value, exists := data[condition.Field]; exists {
				if condition.Operator == "equals" && value == condition.Value {
					return true
				}
			}
		}
	}
	return false
}

// GetTrigger 获取指定触发器
func (ts *triggerService) GetTrigger(name string) (interface{}, bool) {
	ts.mu.RLock()
	defer ts.mu.RUnlock()

	trig, exists := ts.triggers[name]
	return trig, exists
}

// GetAllTriggers 获取所有触发器
func (ts *triggerService) GetAllTriggers() map[string]interface{} {
	ts.mu.RLock()
	defer ts.mu.RUnlock()

	// 创建副本以避免并发问题
	result := make(map[string]interface{})
	for name, trig := range ts.triggers {
		result[name] = trig
	}

	return result
}

// EnableTrigger 启用触发器
func (ts *triggerService) EnableTrigger(name string) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	trig, exists := ts.triggers[name]
	if !exists {
		ts.logger.Error(fmt.Sprintf("触发器不存在: %s", name))
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeResourceNotFound,
			"触发器不存在",
			fmt.Sprintf("触发器名称: %s", name),
		)
	}

	trig.Enabled = true

	// 如果服务正在运行，启动这个触发器
	if ts.running {
		go ts.runTrigger(name, trig)
	}

	ts.logger.Info(fmt.Sprintf("触发器已启用: %s, 服务运行状态: %t", name, ts.running))
	return nil
}

// DisableTrigger 禁用触发器
func (ts *triggerService) DisableTrigger(name string) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	trig, exists := ts.triggers[name]
	if !exists {
		ts.logger.Error(fmt.Sprintf("触发器不存在: %s", name))
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeResourceNotFound,
			"触发器不存在",
			fmt.Sprintf("触发器名称: %s", name),
		)
	}

	trig.Enabled = false
	ts.logger.Info(fmt.Sprintf("触发器已禁用: %s", name))
	return nil
}

// AddTrigger 添加新触发器
func (ts *triggerService) AddTrigger(name string, config interface{}) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	ts.logger.Debug(fmt.Sprintf("开始添加触发器: %s", name))

	if _, exists := ts.triggers[name]; exists {
		ts.logger.Error(fmt.Sprintf("触发器已存在: %s", name))
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeResourceAlreadyExists,
			"触发器已存在",
			fmt.Sprintf("触发器名称: %s", name),
		)
	}

	// 类型断言
	cfg, ok := config.(*common.EventConfig)
	if !ok {
		ts.logger.Error(fmt.Sprintf("AddTrigger: 无效的配置类型: %s", name))
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidInput,
			"无效的配置类型",
			fmt.Sprintf("触发器名称: %s", name),
		)
	}

	trig, err := ts.createTrigger(name, cfg)
	if err != nil {
		ts.logger.Error(fmt.Sprintf("创建触发器失败: %s, 错误: %v", name, err))
		return errors.WrapErrorWithDetails(
			err,
			errors.ErrTypeOperation,
			errors.ErrCodeCreationFailed,
			"创建触发器失败",
			fmt.Sprintf("触发器名称: %s", name),
		)
	}

	ts.triggers[name] = trig

	// 如果服务正在运行且触发器启用，启动它
	if ts.running && trig.Enabled {
		go ts.runTrigger(name, trig)
	}

	ts.logger.Info(fmt.Sprintf("触发器已添加: %s, 启用状态: %t, 服务运行状态: %t", name, trig.Enabled, ts.running))
	return nil
}

// RemoveTrigger 移除触发器
func (ts *triggerService) RemoveTrigger(name string) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	ts.logger.Debug(fmt.Sprintf("开始移除触发器: %s", name))

	if _, exists := ts.triggers[name]; !exists {
		ts.logger.Error(fmt.Sprintf("触发器不存在: %s", name))
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeResourceNotFound,
			"触发器不存在",
			fmt.Sprintf("触发器名称: %s", name),
		)
	}

	delete(ts.triggers, name)
	ts.logger.Info(fmt.Sprintf("触发器已移除: %s", name))
	return nil
}

// GetTriggerStats 获取触发器统计信息
func (ts *triggerService) GetTriggerStats() map[string]interface{} {
	ts.mu.RLock()
	defer ts.mu.RUnlock()

	totalTriggers := len(ts.triggers)
	enabledTriggers := 0
	totalFireCount := 0

	for _, trig := range ts.triggers {
		if trig.Enabled {
			enabledTriggers++
		}
		totalFireCount += trig.TriggerCount
	}

	return map[string]interface{}{
		"total_triggers":   totalTriggers,
		"enabled_triggers": enabledTriggers,
		"total_fire_count": totalFireCount,
		"running":          ts.running,
	}
}

// ProcessTriggers 检查请求的所有触发器（实现container.TriggerService接口）
func (ts *triggerService) ProcessTriggers(req interface{}) ([]string, error) {
	ts.mu.RLock()
	defer ts.mu.RUnlock()

	ts.logger.Debug("开始处理触发器")

	if !ts.running {
		ts.logger.Error("触发器服务未运行")
		return nil, errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeOperation,
			errors.ErrCodeServiceNotRunning,
			"触发器服务未运行",
			"触发器处理",
		)
	}

	// 尝试将req转换为HTTP请求
	httpReq, ok := req.(*http.Request)
	if !ok {
		ts.logger.Debug("请求不是HTTP请求类型，使用基础处理逻辑")
		return ts.processBasicTriggers(req)
	}

	// 使用HTTP处理逻辑
	return ts.processHTTPTriggers(httpReq, nil, "")
}

// processBasicTriggers 处理基础触发器（非HTTP）
func (ts *triggerService) processBasicTriggers(req interface{}) ([]string, error) {
	var triggeredActions []string
	processedCount := 0
	triggeredCount := 0

	// 遍历所有启用的触发器
	for name, trig := range ts.triggers {
		if !trig.Enabled {
			continue
		}

		processedCount++

		// 实现具体的触发器处理逻辑
		ts.logger.Debug(fmt.Sprintf("处理触发器: %s, 类型: %s", name, trig.Type))

		// 根据触发器类型进行不同的处理
		matched := false
		matchData := make(map[string]interface{})

		switch trig.Type {
		case "event":
			// 事件类型触发器，检查事件数据
			matched = ts.evaluateEventTrigger(trig, req, matchData)
		case "timer":
			// 定时器类型触发器，检查时间条件
			matched = ts.evaluateTimerTrigger(trig, matchData)
		case "custom":
			// 自定义类型触发器，使用通用条件评估
			matched = ts.evaluateCustomTrigger(trig, req, matchData)
		default:
			// 默认使用通用条件评估
			matched = ts.evaluateGenericTrigger(trig, req, matchData)
		}

		if matched {
			ts.logger.Info(fmt.Sprintf("触发器匹配成功: %s", name))

			// 更新触发器统计
			trig.LastTriggered = time.Now()
			trig.TriggerCount++
			triggeredCount++

			// 触发器触发
			ts.fireTrigger(name, trig, nil, nil, matchData)

			// 添加触发的动作
			triggeredActions = append(triggeredActions, trig.Actions...)
		} else {
			ts.logger.Debug(fmt.Sprintf("触发器条件不匹配: %s", name))
		}
	}

	ts.logger.Debug(fmt.Sprintf("触发器处理完成，处理数: %d, 触发数: %d, 动作数: %d", processedCount, triggeredCount, len(triggeredActions)))

	return triggeredActions, nil
}

// processHTTPTriggers 处理HTTP触发器
func (ts *triggerService) processHTTPTriggers(req *http.Request, resp *http.Response, stage string) ([]string, error) {
	var actions []string
	processedCount := 0
	triggeredCount := 0

	// 遍历所有触发器
	for name, trig := range ts.triggers {
		if !trig.Enabled {
			continue
		}

		// 检查处理阶段
		if trig.Stage != "" && string(trig.Stage) != stage {
			continue
		}

		processedCount++

		// 实现具体的触发器匹配逻辑
		ts.logger.Debug(fmt.Sprintf("检查触发器: %s", name))

		// 评估触发器条件
		matched, matchData := ts.evaluateTriggerConditions(name, trig, req, resp)
		if matched {
			ts.logger.Info(fmt.Sprintf("触发器匹配成功: %s", name))

			// 更新匹配统计
			trig.TriggerCount++
			triggeredCount++

			// 触发器触发
			ts.fireTrigger(name, trig, req, resp, matchData)

			// 收集动作
			actions = append(actions, trig.Actions...)
		} else {
			ts.logger.Debug(fmt.Sprintf("触发器条件不匹配: %s", name))
		}
	}

	ts.logger.Debug(fmt.Sprintf("HTTP触发器处理完成，处理数: %d, 触发数: %d, 动作数: %d", processedCount, triggeredCount, len(actions)))

	return actions, nil
}

// evaluateTriggerConditions 评估触发器条件
func (ts *triggerService) evaluateTriggerConditions(name string, trig *trigger.TriggerDefinition, req *http.Request, resp *http.Response) (bool, map[string]interface{}) {
	matchData := make(map[string]interface{})

	// 如果没有条件，默认匹配
	if len(trig.Conditions) == 0 {
		return true, matchData
	}

	// 获取条件关系，默认为AND
	relation := strings.ToUpper(trig.ConditionRelation)
	if relation == "" {
		relation = "AND"
	}

	// 评估所有条件并收集结果
	conditionResults := make([]bool, len(trig.Conditions))
	for i, condition := range trig.Conditions {
		matched, data := ts.evaluateSingleCondition(condition, req, resp)
		conditionResults[i] = matched

		// 合并匹配数据（无论是否匹配都收集数据）
		for k, v := range data {
			matchData[k] = v
		}
	}

	// 根据条件关系计算最终结果
	finalResult := ts.applyConditionLogic(conditionResults, relation)

	if !finalResult {
		return false, nil
	}

	return true, matchData
}

// evaluateSingleCondition 评估单个条件
func (ts *triggerService) evaluateSingleCondition(condition trigger.ConditionDefinition, req *http.Request, resp *http.Response) (bool, map[string]interface{}) {
	matchData := make(map[string]interface{})

	switch condition.Type {
	case "url":
		return ts.evaluateURLCondition(condition, req, matchData)
	case "header":
		return ts.evaluateHeaderCondition(condition, req, resp, matchData)
	case "method":
		return ts.evaluateMethodCondition(condition, req, matchData)
	case "body":
		return ts.evaluateBodyCondition(condition, req, resp, matchData)
	case "status":
		return ts.evaluateStatusCondition(condition, resp, matchData)
	case "domain":
		return ts.evaluateDomainCondition(condition, req, matchData)
	case "path":
		return ts.evaluatePathCondition(condition, req, matchData)
	case "query":
		return ts.evaluateQueryCondition(condition, req, matchData)
	case "javascript", "js":
		return ts.evaluateJavaScriptCondition(condition, req, resp)
	default:
		ts.logger.Warn(fmt.Sprintf("未知的条件类型: %s", condition.Type))
		return false, matchData
	}
}

// evaluateURLCondition 评估URL条件
func (ts *triggerService) evaluateURLCondition(condition trigger.ConditionDefinition, req *http.Request, matchData map[string]interface{}) (bool, map[string]interface{}) {
	if req == nil {
		return false, matchData
	}

	url := req.URL.String()
	matchData["url"] = url

	// 检查是否使用增强模式匹配
	if ts.shouldUseEnhancedMatching(condition) {
		matched := ts.evaluateEnhancedURLCondition(condition, url, req)
		if matched {
			matchData["host"] = req.Host
			matchData["path"] = req.URL.Path
			matchData["query"] = req.URL.RawQuery
			matchData["enhanced_match"] = true
		}
		return matched, matchData
	}

	matched := ts.matchStringCondition(condition, url)
	if matched {
		matchData["host"] = req.Host
		matchData["path"] = req.URL.Path
		matchData["query"] = req.URL.RawQuery
	}

	return matched, matchData
}

// evaluateHeaderCondition 评估Header条件
func (ts *triggerService) evaluateHeaderCondition(condition trigger.ConditionDefinition, req *http.Request, resp *http.Response, matchData map[string]interface{}) (bool, map[string]interface{}) {
	headerName := condition.Field
	if headerName == "" {
		return false, matchData
	}

	var headerValue string
	if req != nil {
		headerValue = req.Header.Get(headerName)
	}
	if headerValue == "" && resp != nil {
		headerValue = resp.Header.Get(headerName)
	}

	matchData["header_"+headerName] = headerValue

	return ts.matchStringCondition(condition, headerValue), matchData
}

// evaluateMethodCondition 评估HTTP方法条件
func (ts *triggerService) evaluateMethodCondition(condition trigger.ConditionDefinition, req *http.Request, matchData map[string]interface{}) (bool, map[string]interface{}) {
	if req == nil {
		return false, matchData
	}

	method := req.Method
	matchData["method"] = method

	return ts.matchStringCondition(condition, method), matchData
}

// evaluateBodyCondition 评估Body条件
func (ts *triggerService) evaluateBodyCondition(condition trigger.ConditionDefinition, req *http.Request, resp *http.Response, matchData map[string]interface{}) (bool, map[string]interface{}) {
	var body string

	// 尝试从请求或响应中获取body
	if req != nil && req.Body != nil {
		bodyBytes, err := io.ReadAll(req.Body)
		if err == nil {
			body = string(bodyBytes)
			// 重新设置body以供后续使用
			req.Body = io.NopCloser(strings.NewReader(body))
		}
	} else if resp != nil && resp.Body != nil {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err == nil {
			body = string(bodyBytes)
			// 重新设置body以供后续使用
			resp.Body = io.NopCloser(strings.NewReader(body))
		}
	}

	matchData["body"] = body

	// 检查是否使用增强模式匹配
	if ts.shouldUseEnhancedMatching(condition) {
		matched := ts.evaluateEnhancedBodyCondition(condition, body)
		if matched {
			matchData["body_length"] = len(body)
			matchData["enhanced_match"] = true
		}
		return matched, matchData
	}

	matched := ts.matchStringCondition(condition, body)
	if matched {
		matchData["body_length"] = len(body)
	}

	return matched, matchData
}

// evaluateStatusCondition 评估状态码条件
func (ts *triggerService) evaluateStatusCondition(condition trigger.ConditionDefinition, resp *http.Response, matchData map[string]interface{}) (bool, map[string]interface{}) {
	if resp == nil {
		return false, matchData
	}

	status := fmt.Sprintf("%d", resp.StatusCode)
	matchData["status"] = status

	return ts.matchStringCondition(condition, status), matchData
}

// evaluateDomainCondition 评估域名条件
func (ts *triggerService) evaluateDomainCondition(condition trigger.ConditionDefinition, req *http.Request, matchData map[string]interface{}) (bool, map[string]interface{}) {
	if req == nil {
		return false, matchData
	}

	domain := req.Host
	matchData["domain"] = domain

	// 检查是否使用增强模式匹配
	if ts.shouldUseEnhancedMatching(condition) {
		matched := ts.evaluateEnhancedDomainCondition(condition, domain)
		if matched {
			matchData["enhanced_match"] = true
		}
		return matched, matchData
	}

	return ts.matchStringCondition(condition, domain), matchData
}

// evaluatePathCondition 评估路径条件
func (ts *triggerService) evaluatePathCondition(condition trigger.ConditionDefinition, req *http.Request, matchData map[string]interface{}) (bool, map[string]interface{}) {
	if req == nil {
		return false, matchData
	}

	path := req.URL.Path
	matchData["path"] = path

	return ts.matchStringCondition(condition, path), matchData
}

// evaluateQueryCondition 评估查询参数条件
func (ts *triggerService) evaluateQueryCondition(condition trigger.ConditionDefinition, req *http.Request, matchData map[string]interface{}) (bool, map[string]interface{}) {
	if req == nil {
		return false, matchData
	}

	queryParam := condition.Field
	if queryParam == "" {
		return false, matchData
	}

	queryValue := req.URL.Query().Get(queryParam)
	matchData["query_"+queryParam] = queryValue

	return ts.matchStringCondition(condition, queryValue), matchData
}

// evaluateEventTrigger 评估事件类型触发器
func (ts *triggerService) evaluateEventTrigger(trig *trigger.TriggerDefinition, req interface{}, matchData map[string]interface{}) bool {
	// 将请求数据转换为map以便条件评估
	var eventData map[string]interface{}

	switch v := req.(type) {
	case map[string]interface{}:
		eventData = v
	case string:
		// 如果是字符串，作为事件类型处理
		eventData = map[string]interface{}{"event_type": v}
	default:
		// 其他类型，创建基础事件数据
		eventData = map[string]interface{}{"data": req}
	}

	// 评估所有条件
	for _, condition := range trig.Conditions {
		if !ts.evaluateEventCondition(condition, eventData) {
			return false
		}
	}

	// 将事件数据添加到匹配数据中
	for k, v := range eventData {
		matchData[k] = v
	}

	return true
}

// evaluateTimerTrigger 评估定时器类型触发器
func (ts *triggerService) evaluateTimerTrigger(trig *trigger.TriggerDefinition, matchData map[string]interface{}) bool {
	now := time.Now()
	matchData["current_time"] = now

	// 评估时间相关条件
	for _, condition := range trig.Conditions {
		if condition.Type == "time" || condition.Type == "interval" {
			if !ts.evaluateTimeCondition(condition) {
				return false
			}
		}
	}

	return len(trig.Conditions) > 0 // 至少要有一个时间条件
}

// evaluateCustomTrigger 评估自定义类型触发器
func (ts *triggerService) evaluateCustomTrigger(trig *trigger.TriggerDefinition, req interface{}, matchData map[string]interface{}) bool {
	// 检查是否是高级自定义触发器
	if ts.isAdvancedCustomTrigger(trig) {
		return ts.evaluateAdvancedCustomTrigger(trig, req, matchData)
	}

	// 检查是否是多条件触发器
	if ts.isMultiConditionTrigger(trig) {
		return ts.evaluateMultiConditionTrigger(trig, req, matchData)
	}

	// 检查是否是正则匹配触发器
	if ts.isRegexMatchTrigger(trig) {
		return ts.evaluateRegexMatchTrigger(trig, req, matchData)
	}

	// 默认使用通用条件评估逻辑
	return ts.evaluateGenericTrigger(trig, req, matchData)
}

// evaluateGenericTrigger 评估通用触发器
func (ts *triggerService) evaluateGenericTrigger(trig *trigger.TriggerDefinition, req interface{}, matchData map[string]interface{}) bool {
	// 如果没有条件，默认匹配
	if len(trig.Conditions) == 0 {
		return true
	}

	// 将请求转换为通用数据格式
	var genericData map[string]interface{}
	switch v := req.(type) {
	case map[string]interface{}:
		genericData = v
	default:
		genericData = map[string]interface{}{"request": req}
	}

	// 评估所有条件
	for _, condition := range trig.Conditions {
		if !ts.evaluateGenericCondition(condition, genericData) {
			return false
		}
	}

	// 将通用数据添加到匹配数据中
	for k, v := range genericData {
		matchData[k] = v
	}

	return true
}

// evaluateEventCondition 评估事件条件
func (ts *triggerService) evaluateEventCondition(condition trigger.ConditionDefinition, eventData map[string]interface{}) bool {
	value, exists := eventData[condition.Field]
	if !exists {
		return false
	}

	expectedValue := condition.Value

	switch condition.Operator {
	case "equals", "==":
		return value == expectedValue
	case "not_equals", "!=":
		return value != expectedValue
	case "contains":
		if str, ok := value.(string); ok {
			if expectedStr, ok := expectedValue.(string); ok {
				return strings.Contains(str, expectedStr)
			}
		}
		return false
	case "exists":
		return exists
	case "not_exists":
		return !exists
	default:
		return value == expectedValue
	}
}

// evaluateGenericCondition 评估通用条件
func (ts *triggerService) evaluateGenericCondition(condition trigger.ConditionDefinition, data map[string]interface{}) bool {
	value, exists := data[condition.Field]
	if !exists {
		// 如果字段不存在，检查是否是"not_exists"操作
		return condition.Operator == "not_exists"
	}

	// 将值转换为字符串进行比较
	strValue := fmt.Sprintf("%v", value)
	expectedStr := fmt.Sprintf("%v", condition.Value)

	switch condition.Operator {
	case "equals", "==":
		return strValue == expectedStr
	case "not_equals", "!=":
		return strValue != expectedStr
	case "contains":
		return strings.Contains(strValue, expectedStr)
	case "not_contains":
		return !strings.Contains(strValue, expectedStr)
	case "starts_with":
		return strings.HasPrefix(strValue, expectedStr)
	case "ends_with":
		return strings.HasSuffix(strValue, expectedStr)
	case "regex":
		if matched, err := regexp.MatchString(expectedStr, strValue); err == nil {
			return matched
		}
		return false
	case "exists":
		return exists
	case "not_exists":
		return !exists
	case "empty":
		return strValue == ""
	case "not_empty":
		return strValue != ""
	default:
		return strValue == expectedStr
	}
}

// applyConditionLogic 应用条件逻辑关系
func (ts *triggerService) applyConditionLogic(results []bool, relation string) bool {
	if len(results) == 0 {
		return false
	}

	if len(results) == 1 {
		return results[0]
	}

	switch relation {
	case "AND":
		// 所有条件都必须为真
		for _, result := range results {
			if !result {
				return false
			}
		}
		return true

	case "OR":
		// 至少一个条件为真
		for _, result := range results {
			if result {
				return true
			}
		}
		return false

	case "NOT":
		// 对第一个条件取反
		return !results[0]

	case "XOR":
		// 异或：只有一个条件为真
		trueCount := 0
		for _, result := range results {
			if result {
				trueCount++
			}
		}
		return trueCount == 1

	case "NAND":
		// 非与：不是所有条件都为真
		for _, result := range results {
			if !result {
				return true
			}
		}
		return false

	case "NOR":
		// 非或：所有条件都为假
		for _, result := range results {
			if result {
				return false
			}
		}
		return true

	default:
		// 默认使用AND逻辑
		ts.logger.Warn(fmt.Sprintf("未知的条件关系: %s，使用默认AND逻辑", relation))
		for _, result := range results {
			if !result {
				return false
			}
		}
		return true
	}
}

// isAdvancedCustomTrigger 检查是否是高级自定义触发器
func (ts *triggerService) isAdvancedCustomTrigger(trig *trigger.TriggerDefinition) bool {
	// 检查触发器类型或参数中是否标明为高级自定义触发器
	if trig.Type == "advanced_custom" {
		return true
	}

	// 检查是否包含JavaScript代码
	for _, condition := range trig.Conditions {
		if condition.Type == "javascript" || condition.Type == "js" {
			return true
		}
	}

	return false
}

// isMultiConditionTrigger 检查是否是多条件触发器
func (ts *triggerService) isMultiConditionTrigger(trig *trigger.TriggerDefinition) bool {
	return trig.Type == "multi_condition" || len(trig.Conditions) > 1
}

// isRegexMatchTrigger 检查是否是正则匹配触发器
func (ts *triggerService) isRegexMatchTrigger(trig *trigger.TriggerDefinition) bool {
	return trig.Type == "regex_match"
}

// evaluateAdvancedCustomTrigger 评估高级自定义触发器
func (ts *triggerService) evaluateAdvancedCustomTrigger(trig *trigger.TriggerDefinition, req interface{}, matchData map[string]interface{}) bool {
	// 准备JavaScript执行上下文
	context := map[string]interface{}{
		"trigger":   trig,
		"request":   req,
		"matchData": matchData,
	}

	// 查找JavaScript条件
	for _, condition := range trig.Conditions {
		if condition.Type == "javascript" || condition.Type == "js" {
			script, ok := condition.Value.(string)
			if !ok {
				continue
			}

			// 执行JavaScript代码
			result, err := ts.executeJavaScript(script, context)
			if err != nil {
				ts.logger.Error(fmt.Sprintf("高级自定义触发器JavaScript执行失败: %v", err))
				return false
			}

			// 转换结果为布尔值
			if matched, ok := result.(bool); ok {
				if !matched {
					return false
				}
			} else {
				// 非布尔值按照JavaScript真值规则处理
				if result == nil || result == false || result == 0 || result == "" {
					return false
				}
			}
		}
	}

	return true
}

// evaluateMultiConditionTrigger 评估多条件触发器
func (ts *triggerService) evaluateMultiConditionTrigger(trig *trigger.TriggerDefinition, req interface{}, matchData map[string]interface{}) bool {
	// 使用已有的条件关系逻辑
	if len(trig.Conditions) == 0 {
		return true
	}

	// 获取条件关系，默认为AND
	relation := strings.ToUpper(trig.ConditionRelation)
	if relation == "" {
		relation = "AND"
	}

	// 评估所有条件
	conditionResults := make([]bool, len(trig.Conditions))
	for i, condition := range trig.Conditions {
		conditionResults[i] = ts.evaluateGenericCondition(condition, map[string]interface{}{"request": req})
	}

	// 应用条件逻辑
	return ts.applyConditionLogic(conditionResults, relation)
}

// evaluateRegexMatchTrigger 评估正则匹配触发器
func (ts *triggerService) evaluateRegexMatchTrigger(trig *trigger.TriggerDefinition, req interface{}, matchData map[string]interface{}) bool {
	// 获取要匹配的文本
	targetText := ""
	switch v := req.(type) {
	case *http.Request:
		targetText = v.URL.String()
	case string:
		targetText = v
	case map[string]interface{}:
		if text, ok := v["text"].(string); ok {
			targetText = text
		}
	default:
		targetText = fmt.Sprintf("%v", req)
	}

	// 评估所有正则条件
	for _, condition := range trig.Conditions {
		if condition.Type == "regex" {
			pattern, ok := condition.Value.(string)
			if !ok {
				continue
			}

			matched, err := regexp.MatchString(pattern, targetText)
			if err != nil {
				ts.logger.Error(fmt.Sprintf("正则匹配触发器错误: %v", err))
				return false
			}

			if !matched {
				return false
			}

			// 记录匹配数据
			matchData["regex_pattern"] = pattern
			matchData["matched_text"] = targetText
		}
	}

	return true
}

// matchStringCondition 匹配字符串条件
func (ts *triggerService) matchStringCondition(condition trigger.ConditionDefinition, value string) bool {
	expectedValue, ok := condition.Value.(string)
	if !ok {
		return false
	}

	switch condition.Operator {
	case "equals", "==":
		return value == expectedValue
	case "not_equals", "!=":
		return value != expectedValue
	case "contains":
		return strings.Contains(value, expectedValue)
	case "not_contains":
		return !strings.Contains(value, expectedValue)
	case "starts_with":
		return strings.HasPrefix(value, expectedValue)
	case "ends_with":
		return strings.HasSuffix(value, expectedValue)
	case "regex":
		if matched, err := regexp.MatchString(expectedValue, value); err == nil {
			return matched
		}
		return false
	case "empty":
		return value == ""
	case "not_empty":
		return value != ""
	default:
		// 默认使用equals
		return value == expectedValue
	}
}

// ReloadConfig 重新加载配置
func (ts *triggerService) ReloadConfig(config *common.Config) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	ts.logger.Info(fmt.Sprintf("开始重新加载触发器配置，当前触发器数: %d", len(ts.triggers)))

	// 停止所有现有触发器
	if ts.cancel != nil {
		ts.cancel()
	}

	// 清空现有触发器
	oldTriggerCount := len(ts.triggers)
	ts.triggers = make(map[string]*trigger.TriggerDefinition)

	// 重新初始化
	ts.config = config
	ts.ctx, ts.cancel = context.WithCancel(context.Background())

	if err := ts.initializeTriggers(); err != nil {
		ts.logger.Error(fmt.Sprintf("重新加载触发器配置失败: %v", err))
		return errors.WrapErrorWithDetails(
			err,
			errors.ErrTypeConfiguration,
			errors.ErrCodeConfigReloadFailed,
			"重新加载触发器配置失败",
			"触发器配置重载",
		)
	}

	// 如果服务正在运行，重新启动触发器
	restartedCount := 0
	if ts.running {
		for name, trig := range ts.triggers {
			if trig.Enabled {
				go ts.runTrigger(name, trig)
				restartedCount++
			}
		}
	}

	ts.logger.Info(fmt.Sprintf("触发器配置已重新加载，旧触发器数: %d, 新触发器数: %d, 重启数: %d", oldTriggerCount, len(ts.triggers), restartedCount))
	return nil
}
