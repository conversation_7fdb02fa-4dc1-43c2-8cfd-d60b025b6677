package strategy

import (
	"context"
	"math"
	"math/rand"
	"sort"
	"sync"
	"time"

	"github.com/flexp/flexp/common/constants"
	"github.com/flexp/flexp/common/errors"
)

// RandomProxyStrategy 随机代理选择策略
type RandomProxyStrategy struct {
	mu          sync.RWMutex
	metrics     map[string]*ProxyMetrics
	random      *rand.Rand
	baseManager *BaseMetricsManager
}

// NewRandomProxyStrategy 创建新的随机代理策略
func NewRandomProxyStrategy() *RandomProxyStrategy {
	return &RandomProxyStrategy{
		metrics:     make(map[string]*ProxyMetrics),
		random:      rand.New(rand.NewSource(time.Now().UnixNano())),
		baseManager: NewBaseMetricsManager(),
	}
}

// SelectProxy 随机选择代理
func (s *RandomProxyStrategy) SelectProxy(ctx context.Context, proxies []string, domain string) (string, error) {
	if len(proxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理列表为空")
	}

	s.mu.RLock()
	defer s.mu.RUnlock()

	// 过滤可用代理
	availableProxies := make([]string, 0, len(proxies))
	for _, proxy := range proxies {
		metrics := s.metrics[proxy]
		if s.baseManager.IsProxyAvailable(metrics) {
			availableProxies = append(availableProxies, proxy)
		}
	}

	if len(availableProxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "没有可用的代理")
	}

	// 随机选择
	index := s.random.Intn(len(availableProxies))
	return availableProxies[index], nil
}

// GetName 获取策略名称
func (s *RandomProxyStrategy) GetName() string {
	return "random"
}

// UpdateProxyMetrics 更新代理指标
func (s *RandomProxyStrategy) UpdateProxyMetrics(proxy string, success bool, responseTime time.Duration, domain string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	metrics, exists := s.metrics[proxy]
	if !exists {
		metrics = &ProxyMetrics{
			ProxyURL:    proxy,
			DomainStats: make(map[string]*DomainPerformance),
		}
		s.metrics[proxy] = metrics
	}

	s.baseManager.UpdateProxyMetrics(metrics, success, responseTime, domain)
}

// SequentialProxyStrategy 实现顺序代理选择
type SequentialProxyStrategy struct {
	mu           sync.RWMutex
	currentIndex int
	metrics      map[string]*ProxyMetrics
	baseManager  *BaseMetricsManager
}

// NewSequentialProxyStrategy 创建新的顺序代理策略
func NewSequentialProxyStrategy() *SequentialProxyStrategy {
	return &SequentialProxyStrategy{
		metrics:      make(map[string]*ProxyMetrics),
		baseManager:  NewBaseMetricsManager(),
		currentIndex: 0,
	}
}

// SelectProxy 顺序选择代理
func (s *SequentialProxyStrategy) SelectProxy(ctx context.Context, proxies []string, domain string) (string, error) {
	if len(proxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理列表为空")
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	// 过滤可用代理
	availableProxies := make([]string, 0, len(proxies))
	for _, proxy := range proxies {
		metrics := s.metrics[proxy]
		if s.baseManager.IsProxyAvailable(metrics) {
			availableProxies = append(availableProxies, proxy)
		}
	}

	if len(availableProxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "没有可用的代理")
	}

	// 顺序选择
	proxy := availableProxies[s.currentIndex%len(availableProxies)]
	s.currentIndex++

	return proxy, nil
}

// GetName 获取策略名称
func (s *SequentialProxyStrategy) GetName() string {
	return "sequential"
}

// UpdateProxyMetrics 更新代理指标
func (s *SequentialProxyStrategy) UpdateProxyMetrics(proxy string, success bool, responseTime time.Duration, domain string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	metrics, exists := s.metrics[proxy]
	if !exists {
		metrics = &ProxyMetrics{
			ProxyURL:    proxy,
			DomainStats: make(map[string]*DomainPerformance),
		}
		s.metrics[proxy] = metrics
	}

	s.baseManager.UpdateProxyMetrics(metrics, success, responseTime, domain)
}

// QualityBasedProxyStrategy 实现基于质量的代理选择
type QualityBasedProxyStrategy struct {
	baseManager *BaseMetricsManager
	mu          sync.RWMutex
	metrics     map[string]*ProxyMetrics
	random      *rand.Rand
}

// NewQualityBasedProxyStrategy 创建新的基于质量的代理策略
func NewQualityBasedProxyStrategy() *QualityBasedProxyStrategy {
	return &QualityBasedProxyStrategy{
		baseManager: NewBaseMetricsManager(),
		metrics:     make(map[string]*ProxyMetrics),
		random:      rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// SelectProxy 基于质量选择代理
func (s *QualityBasedProxyStrategy) SelectProxy(ctx context.Context, proxies []string, domain string) (string, error) {
	if len(proxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理列表为空")
	}

	s.mu.RLock()
	defer s.mu.RUnlock()

	// 按质量评分排序代理
	type proxyScore struct {
		proxy string
		score float64
	}

	proxyScores := make([]proxyScore, 0, len(proxies))
	for _, proxy := range proxies {
		metrics, exists := s.metrics[proxy]
		if !exists {
			metrics = &ProxyMetrics{ProxyURL: proxy}
		}
		if !s.baseManager.IsProxyAvailable(metrics) {
			continue
		}

		score := s.getProxyScore(proxy, domain)
		proxyScores = append(proxyScores, proxyScore{proxy: proxy, score: score})
	}

	if len(proxyScores) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "没有可用的代理")
	}

	// 按评分降序排序
	sort.Slice(proxyScores, func(i, j int) bool {
		return proxyScores[i].score > proxyScores[j].score
	})

	// 使用加权随机选择前30%的高质量代理
	topCount := int(math.Max(1, float64(len(proxyScores))*constants.TopProxyRatio))
	topProxies := proxyScores[:topCount]

	// 加权随机选择
	totalWeight := 0.0
	for _, ps := range topProxies {
		totalWeight += ps.score
	}

	if totalWeight == 0 {
		return topProxies[0].proxy, nil
	}

	randomValue := s.random.Float64() * totalWeight
	currentWeight := 0.0

	for _, ps := range topProxies {
		currentWeight += ps.score
		if currentWeight >= randomValue {
			return ps.proxy, nil
		}
	}

	return topProxies[0].proxy, nil
}

// GetName 获取策略名称
func (s *QualityBasedProxyStrategy) GetName() string {
	return "quality"
}

// UpdateProxyMetrics 更新代理指标
func (s *QualityBasedProxyStrategy) UpdateProxyMetrics(proxy string, success bool, responseTime time.Duration, domain string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	metrics, exists := s.metrics[proxy]
	if !exists {
		metrics = &ProxyMetrics{
			ProxyURL:    proxy,
			DomainStats: make(map[string]*DomainPerformance),
		}
		s.metrics[proxy] = metrics
	}

	// 使用基础指标管理器更新指标
	s.baseManager.UpdateProxyMetrics(metrics, success, responseTime, domain)
}

// getProxyScore 获取代理评分（考虑域名特定性能）
func (s *QualityBasedProxyStrategy) getProxyScore(proxy, domain string) float64 {
	metrics, exists := s.metrics[proxy]
	if !exists {
		return constants.DefaultQualityScore // 新代理默认评分
	}

	// 如果有域名特定统计，优先使用
	if domain != "" {
		if domainStats, exists := metrics.DomainStats[domain]; exists {
			return domainStats.QualityScore
		}
	}

	return metrics.QualityScore
}

// SmartProxyStrategy 智能代理选择策略
// 根据触发的动作类型智能决定是否切换代理
type SmartProxyStrategy struct {
	mu             sync.RWMutex
	metrics        map[string]*ProxyMetrics
	random         *rand.Rand
	baseManager    *BaseMetricsManager
	currentProxy   string
	lastActionType string
}

// NewSmartProxyStrategy 创建新的智能代理策略
func NewSmartProxyStrategy() *SmartProxyStrategy {
	return &SmartProxyStrategy{
		metrics:     make(map[string]*ProxyMetrics),
		random:      rand.New(rand.NewSource(time.Now().UnixNano())),
		baseManager: NewBaseMetricsManager(),
	}
}

// SelectProxy 智能选择代理
// 根据上下文中的动作信息决定是否切换代理
func (s *SmartProxyStrategy) SelectProxy(ctx context.Context, proxies []string, domain string) (string, error) {
	if len(proxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理列表为空")
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	// 检查上下文中是否有重试动作信息
	shouldSwitchProxy := s.shouldSwitchProxy(ctx)

	// 如果不需要切换代理且当前代理可用，继续使用当前代理
	if !shouldSwitchProxy && s.currentProxy != "" {
		for _, proxy := range proxies {
			if proxy == s.currentProxy {
				metrics := s.metrics[proxy]
				if s.baseManager.IsProxyAvailable(metrics) {
					return proxy, nil
				}
				break
			}
		}
	}

	// 需要切换代理或当前代理不可用，选择新代理
	availableProxies := make([]string, 0, len(proxies))
	for _, proxy := range proxies {
		// 如果需要切换，排除当前代理
		if shouldSwitchProxy && proxy == s.currentProxy {
			continue
		}

		metrics := s.metrics[proxy]
		if s.baseManager.IsProxyAvailable(metrics) {
			availableProxies = append(availableProxies, proxy)
		}
	}

	if len(availableProxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "没有可用的代理")
	}

	// 使用质量优先的选择策略
	selectedProxy := s.selectBestProxy(availableProxies, domain)
	s.currentProxy = selectedProxy

	return selectedProxy, nil
}

// shouldSwitchProxy 检查是否应该切换代理
func (s *SmartProxyStrategy) shouldSwitchProxy(ctx context.Context) bool {
	// 检查上下文中的重试动作类型
	if retryActionType, ok := ctx.Value("retry_action_type").(string); ok {
		s.lastActionType = retryActionType
		// 如果是使用新IP重试，则需要切换代理
		return retryActionType == "retry"
	}

	// 检查是否标记需要重试
	if actionRequiresRetry, ok := ctx.Value("action_requires_retry").(bool); ok && actionRequiresRetry {
		// 如果上次动作类型是retry，则需要切换
		return s.lastActionType == "retry"
	}

	return false
}

// selectBestProxy 选择最佳代理（基于质量评分）
func (s *SmartProxyStrategy) selectBestProxy(proxies []string, domain string) string {
	if len(proxies) == 1 {
		return proxies[0]
	}

	// 按质量评分排序代理
	type proxyScore struct {
		proxy string
		score float64
	}

	proxyScores := make([]proxyScore, 0, len(proxies))
	for _, proxy := range proxies {
		score := s.getProxyScore(proxy, domain)
		proxyScores = append(proxyScores, proxyScore{proxy: proxy, score: score})
	}

	// 按评分降序排序
	sort.Slice(proxyScores, func(i, j int) bool {
		return proxyScores[i].score > proxyScores[j].score
	})

	// 使用加权随机选择前30%的高质量代理
	topCount := int(math.Max(1, float64(len(proxyScores))*constants.TopProxyRatio))
	topProxies := proxyScores[:topCount]

	// 加权随机选择
	totalWeight := 0.0
	for _, ps := range topProxies {
		totalWeight += ps.score
	}

	if totalWeight == 0 {
		return topProxies[0].proxy
	}

	randomValue := s.random.Float64() * totalWeight
	currentWeight := 0.0

	for _, ps := range topProxies {
		currentWeight += ps.score
		if currentWeight >= randomValue {
			return ps.proxy
		}
	}

	return topProxies[0].proxy
}

// UpdateProxyMetrics 更新代理指标
func (s *SmartProxyStrategy) UpdateProxyMetrics(proxy string, success bool, responseTime time.Duration, domain string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	metrics, exists := s.metrics[proxy]
	if !exists {
		metrics = &ProxyMetrics{
			ProxyURL:    proxy,
			DomainStats: make(map[string]*DomainPerformance),
		}
		s.metrics[proxy] = metrics
	}

	// 使用基础指标管理器更新指标
	s.baseManager.UpdateProxyMetrics(metrics, success, responseTime, domain)
}

// GetName 获取策略名称
func (s *SmartProxyStrategy) GetName() string {
	return "smart"
}

// getProxyScore 获取代理评分（考虑域名特定性能）
func (s *SmartProxyStrategy) getProxyScore(proxy, domain string) float64 {
	metrics, exists := s.metrics[proxy]
	if !exists {
		return constants.DefaultQualityScore // 新代理默认评分
	}

	// 如果有域名特定统计，优先使用
	if domain != "" {
		if domainStats, exists := metrics.DomainStats[domain]; exists {
			return domainStats.QualityScore
		}
	}

	return metrics.QualityScore
}
