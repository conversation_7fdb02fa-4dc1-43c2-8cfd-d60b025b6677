// Package strategy 提供代理选择策略
package strategy

import (
	"math"
	"sync"
	"time"

	"github.com/flexp/flexp/common/constants"
)

// BaseMetricsManager 为代理策略提供基础指标管理
type BaseMetricsManager struct {
	mu sync.RWMutex
}

// NewBaseMetricsManager 创建新的基础指标管理器实例
func NewBaseMetricsManager() *BaseMetricsManager {
	return &BaseMetricsManager{}
}

// UpdateTargetMetrics 更新目标指标
func (bm *BaseMetricsManager) UpdateTargetMetrics(metrics *TargetMetrics, success bool, responseTime time.Duration) {
	metrics.TotalRequests++
	metrics.LastUsed = time.Now()

	if success {
		metrics.SuccessCount++
	} else {
		metrics.FailureCount++
	}

	if responseTime > 0 {
		if metrics.AvgResponseTime == 0 {
			metrics.AvgResponseTime = responseTime
		} else {
			metrics.AvgResponseTime = (metrics.AvgResponseTime + responseTime) / 2
		}
	}
}

// IsTargetHealthy 检查目标是否健康
func (bm *BaseMetricsManager) IsTargetHealthy(metrics *TargetMetrics) bool {
	if metrics == nil {
		return true // 新目标默认健康
	}

	if metrics.TotalRequests < constants.MinRequestsForStats {
		return true // 请求数太少，认为健康
	}

	failureRate := float64(metrics.FailureCount) / float64(metrics.TotalRequests)
	return failureRate < constants.MaxFailureRate
}

// UpdateTargetHealthStatus 更新目标健康状态
func (bm *BaseMetricsManager) UpdateTargetHealthStatus(metrics *TargetMetrics) {
	if metrics.TotalRequests < constants.MinRequestsForStats {
		return // 请求数太少，不更新健康状态
	}

	failureRate := float64(metrics.FailureCount) / float64(metrics.TotalRequests)
	metrics.Healthy = failureRate < constants.MaxFailureRate
}

// UpdateTargetHealth 更新目标健康状态的通用方法
func (bm *BaseMetricsManager) UpdateTargetHealth(metricsMap map[string]*TargetMetrics, target Target, healthy bool, mu *sync.RWMutex) {
	mu.Lock()
	defer mu.Unlock()

	metrics, exists := metricsMap[target.ID]
	if !exists {
		metrics = &TargetMetrics{
			TargetID: target.ID,
			Healthy:  healthy,
			Weight:   target.Weight,
		}
		metricsMap[target.ID] = metrics
	} else {
		metrics.Healthy = healthy
	}
}

// GetOrCreateTargetMetrics 获取或创建目标指标的通用方法
func (bm *BaseMetricsManager) GetOrCreateTargetMetrics(metricsMap map[string]*TargetMetrics, targetID string, mu *sync.RWMutex) *TargetMetrics {
	mu.Lock()
	defer mu.Unlock()

	metrics, exists := metricsMap[targetID]
	if !exists {
		metrics = &TargetMetrics{
			TargetID: targetID,
			Healthy:  true,
			Weight:   1,
		}
		metricsMap[targetID] = metrics
	}
	return metrics
}

// UpdateProxyMetrics 更新代理指标的通用逻辑
func (bm *BaseMetricsManager) UpdateProxyMetrics(metrics *ProxyMetrics, success bool, responseTime time.Duration, domain string) {
	metrics.TotalRequests++
	metrics.LastUsed = time.Now()

	if success {
		metrics.SuccessCount++
	} else {
		metrics.FailureCount++
	}

	if responseTime > 0 {
		if metrics.AvgResponseTime == 0 {
			metrics.AvgResponseTime = responseTime
		} else {
			metrics.AvgResponseTime = (metrics.AvgResponseTime + responseTime) / 2
		}
	}

	// 更新域名级别统计
	if domain != "" {
		bm.UpdateDomainStats(metrics, domain, success, responseTime)
	}

	// 计算质量评分
	metrics.QualityScore = bm.CalculateQualityScore(metrics)
}

// IsProxyAvailable 检查代理是否可用的通用逻辑
func (bm *BaseMetricsManager) IsProxyAvailable(metrics *ProxyMetrics) bool {
	if metrics == nil {
		return true // 新代理默认可用
	}

	if metrics.TotalRequests > constants.MinRequestsForStats {
		failureRate := float64(metrics.FailureCount) / float64(metrics.TotalRequests)
		if failureRate > constants.MaxFailureRate {
			return false
		}
	}

	return true
}

// UpdateDomainStats 更新域名统计的通用逻辑
func (bm *BaseMetricsManager) UpdateDomainStats(metrics *ProxyMetrics, domain string, success bool, responseTime time.Duration) {
	domainStats, exists := metrics.DomainStats[domain]
	if !exists {
		domainStats = &DomainPerformance{
			Domain: domain,
		}
		metrics.DomainStats[domain] = domainStats
	}

	domainStats.LastUsed = time.Now()
	if success {
		domainStats.SuccessCount++
	} else {
		domainStats.FailureCount++
	}

	if responseTime > 0 {
		if domainStats.AvgResponseTime == 0 {
			domainStats.AvgResponseTime = responseTime
		} else {
			domainStats.AvgResponseTime = (domainStats.AvgResponseTime + responseTime) / 2
		}
	}

	// 计算域名质量评分
	totalRequests := domainStats.SuccessCount + domainStats.FailureCount
	if totalRequests > 0 {
		successRate := float64(domainStats.SuccessCount) / float64(totalRequests)
		responseTimeFactor := bm.calculateResponseTimeFactor(domainStats.AvgResponseTime)
		domainStats.QualityScore = (successRate * constants.SuccessRateWeight) + (responseTimeFactor * constants.ResponseTimeWeight)
	}
}

// CalculateQualityScore 计算质量评分的通用逻辑
func (bm *BaseMetricsManager) CalculateQualityScore(metrics *ProxyMetrics) float64 {
	if metrics.TotalRequests == 0 {
		return constants.DefaultQualityScore
	}

	successRate := float64(metrics.SuccessCount) / float64(metrics.TotalRequests)
	responseTimeFactor := bm.calculateResponseTimeFactor(metrics.AvgResponseTime)

	return (successRate * constants.SuccessRateWeight) + (responseTimeFactor * constants.ResponseTimeWeight)
}

// calculateResponseTimeFactor 计算响应时间因子
func (bm *BaseMetricsManager) calculateResponseTimeFactor(responseTime time.Duration) float64 {
	return math.Max(0, 1.0-float64(responseTime.Milliseconds())/constants.ResponseTimeBaseline)
}
