package trigger

import (
	"regexp"
	"strings"

	"github.com/flexp/flexp/common"
)

// EnhancedPatternMatcher 增强的模式匹配器
type EnhancedPatternMatcher struct {
	config     *common.EnhancedPatternConfig
	regexCache map[string]*regexp.Regexp
}

// NewEnhancedPatternMatcher 创建增强模式匹配器
func NewEnhancedPatternMatcher(config *common.EnhancedPatternConfig) *EnhancedPatternMatcher {
	if config == nil {
		return nil
	}

	matcher := &EnhancedPatternMatcher{
		config:     config,
		regexCache: make(map[string]*regexp.Regexp),
	}

	// 预编译所有正则表达式
	matcher.precompileRegexes()

	return matcher
}

// precompileRegexes 预编译所有正则表达式
func (m *EnhancedPatternMatcher) precompileRegexes() {
	for _, pattern := range m.config.Patterns {
		m.compilePatternExpression(&pattern)
	}
}

// compilePatternExpression 编译模式表达式中的正则
func (m *EnhancedPatternMatcher) compilePatternExpression(expr *common.PatternExpression) {
	// 编译主正则表达式
	if expr.Pattern != "" {
		m.compileAndCache(expr.Pattern)
	}

	// 编译链式模式中的正则
	for _, chain := range expr.Chain {
		m.compileAndCache(chain.Pattern)
	}

	// 递归编译子组
	if expr.SubGroup != nil {
		for _, subPattern := range expr.SubGroup.Patterns {
			m.compilePatternExpression(&subPattern)
		}
	}
}

// compileAndCache 编译并缓存正则表达式
func (m *EnhancedPatternMatcher) compileAndCache(pattern string) {
	if pattern == "" {
		return
	}

	if _, exists := m.regexCache[pattern]; !exists {
		compiled, err := regexp.Compile(pattern)
		if err != nil {
			triggerLogger.GetRawLogger().Errorf("编译正则表达式失败 '%s': %v", pattern, err)
			return
		}
		m.regexCache[pattern] = compiled
	}
}

// Match 执行模式匹配
func (m *EnhancedPatternMatcher) Match(text string) bool {
	if m == nil || m.config == nil || len(m.config.Patterns) == 0 {
		return false
	}

	results := make([]bool, len(m.config.Patterns))

	// 计算每个模式表达式的结果
	for i, pattern := range m.config.Patterns {
		results[i] = m.matchPatternExpression(&pattern, text)
	}

	// 应用顶级逻辑操作（使用默认OR逻辑）
	return m.applyLogic(results, "OR")
}

// matchPatternExpression 匹配单个模式表达式
func (m *EnhancedPatternMatcher) matchPatternExpression(expr *common.PatternExpression, text string) bool {
	var result bool

	// 处理子组
	if expr.SubGroup != nil {
		subMatcher := NewEnhancedPatternMatcher(expr.SubGroup)
		result = subMatcher.Match(text)
	} else if expr.Pattern != "" {
		// 处理单个正则表达式
		result = m.matchSingleRegex(expr.Pattern, text)

		// 处理链式模式
		if len(expr.Chain) > 0 {
			chainResults := []bool{result}

			for _, chain := range expr.Chain {
				chainMatch := m.matchSingleRegex(chain.Pattern, text)
				if chain.Negate {
					chainMatch = !chainMatch
				}
				chainResults = append(chainResults, chainMatch)
			}

			// 应用链式逻辑
			result = m.applyChainLogic(chainResults, expr.Chain, expr.Logic)
		}
	} else if len(expr.Chain) > 0 {
		// 仅有链式模式，没有主正则
		chainResults := make([]bool, len(expr.Chain))
		for i, chain := range expr.Chain {
			chainMatch := m.matchSingleRegex(chain.Pattern, text)
			if chain.Negate {
				chainMatch = !chainMatch
			}
			chainResults[i] = chainMatch
		}
		result = m.applyChainLogic(chainResults, expr.Chain, expr.Logic)
	}

	// 应用否定
	if expr.Negate {
		result = !result
	}

	return result
}

// matchSingleRegex 匹配单个正则表达式
func (m *EnhancedPatternMatcher) matchSingleRegex(pattern, text string) bool {
	if pattern == "" {
		return false
	}

	regex, exists := m.regexCache[pattern]
	if !exists {
		// 如果缓存中没有，尝试重新编译
		m.compileAndCache(pattern)
		regex, exists = m.regexCache[pattern]
		if !exists {
			return false
		}
	}

	return regex.MatchString(text)
}

// applyLogic 应用逻辑操作
func (m *EnhancedPatternMatcher) applyLogic(results []bool, logic string) bool {
	if len(results) == 0 {
		return false
	}

	if len(results) == 1 {
		return results[0]
	}

	switch strings.ToUpper(logic) {
	case "AND":
		for _, result := range results {
			if !result {
				return false
			}
		}
		return true

	case "OR":
		for _, result := range results {
			if result {
				return true
			}
		}
		return false

	case "XOR":
		trueCount := 0
		for _, result := range results {
			if result {
				trueCount++
			}
		}
		return trueCount == 1

	case "NOT":
		// NOT 操作只对第一个结果取反
		return !results[0]

	default:
		// 默认为 OR 逻辑
		for _, result := range results {
			if result {
				return true
			}
		}
		return false
	}
}

// applyChainLogic 应用链式逻辑操作
func (m *EnhancedPatternMatcher) applyChainLogic(results []bool, chains []common.ChainedPattern, defaultLogic string) bool {
	if len(results) == 0 {
		return false
	}

	if len(results) == 1 {
		return results[0]
	}

	// 从第一个结果开始
	finalResult := results[0]

	// 逐步应用链式操作
	for i := 1; i < len(results); i++ {
		operation := defaultLogic
		if i-1 < len(chains) && chains[i-1].Operator != "" {
			operation = chains[i-1].Operator
		}

		switch strings.ToUpper(operation) {
		case "AND":
			finalResult = finalResult && results[i]
		case "OR":
			finalResult = finalResult || results[i]
		case "XOR":
			finalResult = finalResult != results[i]
		case "NOT":
			finalResult = finalResult && !results[i]
		default:
			// 默认为 OR
			finalResult = finalResult || results[i]
		}
	}

	return finalResult
}

// EnhancedHeaderPatternMatcher 增强的头部模式匹配器
type EnhancedHeaderPatternMatcher struct {
	config         *common.EnhancedHeaderPatternConfig
	headerMatchers map[string]*EnhancedPatternMatcher
}

// NewEnhancedHeaderPatternMatcher 创建增强头部模式匹配器
func NewEnhancedHeaderPatternMatcher(config *common.EnhancedHeaderPatternConfig) *EnhancedHeaderPatternMatcher {
	if config == nil {
		return nil
	}

	matcher := &EnhancedHeaderPatternMatcher{
		config:         config,
		headerMatchers: make(map[string]*EnhancedPatternMatcher),
	}

	// 为每个头部创建模式匹配器
	for headerName, patterns := range config.Headers {
		if patterns != nil {
			matcher.headerMatchers[headerName] = NewEnhancedPatternMatcher(patterns)
		}
	}

	return matcher
}

// Match 执行头部模式匹配
func (m *EnhancedHeaderPatternMatcher) Match(headers map[string][]string) bool {
	if m == nil || m.config == nil || len(m.config.Headers) == 0 {
		return false
	}

	results := make([]bool, 0, len(m.config.Headers))

	// 计算每个头部表达式的结果
	for headerName, patterns := range m.config.Headers {
		headerValues, exists := headers[headerName]
		if !exists {
			results = append(results, false)
			continue
		}

		// 检查头部的所有值
		headerMatch := false
		if matcher, exists := m.headerMatchers[headerName]; exists {
			for _, value := range headerValues {
				if matcher.Match(value) {
					headerMatch = true
					break
				}
			}
		}

		// 应用否定（如果patterns配置中有否定设置）
		if patterns.Negation {
			headerMatch = !headerMatch
		}

		results = append(results, headerMatch)
	}

	// 应用顶级逻辑操作（使用默认OR逻辑）
	return m.applyLogic(results, "OR")
}

// applyLogic 应用逻辑操作（复用EnhancedPatternMatcher的逻辑）
func (m *EnhancedHeaderPatternMatcher) applyLogic(results []bool, logic string) bool {
	if len(results) == 0 {
		return false
	}

	if len(results) == 1 {
		return results[0]
	}

	switch strings.ToUpper(logic) {
	case "AND":
		for _, result := range results {
			if !result {
				return false
			}
		}
		return true

	case "OR":
		for _, result := range results {
			if result {
				return true
			}
		}
		return false

	case "XOR":
		trueCount := 0
		for _, result := range results {
			if result {
				trueCount++
			}
		}
		return trueCount == 1

	case "NOT":
		// NOT 操作只对第一个结果取反
		return !results[0]

	default:
		// 默认为 OR 逻辑
		for _, result := range results {
			if result {
				return true
			}
		}
		return false
	}
}
