package trigger

import (
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sort"
	"sync"
	"time"

	"github.com/flexp/flexp/common/errors"
	"github.com/flexp/flexp/common/logger"
)

// StatsPersistenceManager 统计数据持久化管理器
type StatsPersistenceManager struct {
	dataDir          string
	saveInterval     time.Duration
	maxHistoryDays   int
	compressionLevel int
	logger           *logger.LoggerAdapter
	mu               sync.RWMutex
	stopChan         chan struct{}
	running          bool
}

// StatsPersistenceOptions 持久化选项
type StatsPersistenceOptions struct {
	DataDir          string        `json:"data_dir"`
	SaveInterval     time.Duration `json:"save_interval"`
	MaxHistoryDays   int           `json:"max_history_days"`
	CompressionLevel int           `json:"compression_level"`
	EnableAutoSave   bool          `json:"enable_auto_save"`
}

// StatsSnapshot 统计数据快照
type StatsSnapshot struct {
	Timestamp time.Time                `json:"timestamp"`
	Version   int                      `json:"version"`
	Stats     map[string]*TriggerStats `json:"stats"`
	Metadata  map[string]interface{}   `json:"metadata"`
}

// StatsHistoryEntry 历史记录条目
type StatsHistoryEntry struct {
	Date     string `json:"date"`
	Filename string `json:"filename"`
	Size     int64  `json:"size"`
	Count    int    `json:"count"`
}

// NewStatsPersistenceManager 创建统计数据持久化管理器
func NewStatsPersistenceManager(options StatsPersistenceOptions) *StatsPersistenceManager {
	if options.DataDir == "" {
		options.DataDir = "./stats_data"
	}
	if options.SaveInterval <= 0 {
		options.SaveInterval = 5 * time.Minute
	}
	if options.MaxHistoryDays <= 0 {
		options.MaxHistoryDays = 30
	}
	if options.CompressionLevel <= 0 {
		options.CompressionLevel = gzip.DefaultCompression
	}

	// 确保数据目录存在
	os.MkdirAll(options.DataDir, 0755)

	return &StatsPersistenceManager{
		dataDir:          options.DataDir,
		saveInterval:     options.SaveInterval,
		maxHistoryDays:   options.MaxHistoryDays,
		compressionLevel: options.CompressionLevel,
		logger:           logger.GetLoggerAdapter("STATS_PERSISTENCE"),
		stopChan:         make(chan struct{}),
	}
}

// Start 启动持久化服务
func (spm *StatsPersistenceManager) Start() error {
	spm.mu.Lock()
	defer spm.mu.Unlock()

	if spm.running {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeInvalidConfiguration,
			"统计数据持久化服务已在运行",
			"",
		)
	}

	spm.running = true
	go spm.autoSaveLoop()

	spm.logger.Info("统计数据持久化服务已启动，保存间隔: %v", spm.saveInterval)
	return nil
}

// Stop 停止持久化服务
func (spm *StatsPersistenceManager) Stop() error {
	spm.mu.Lock()
	defer spm.mu.Unlock()

	if !spm.running {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeInvalidConfiguration,
			"统计数据持久化服务未运行",
			"",
		)
	}

	close(spm.stopChan)
	spm.running = false

	spm.logger.Info("统计数据持久化服务已停止")
	return nil
}

// SaveStats 保存统计数据
func (spm *StatsPersistenceManager) SaveStats(stats map[string]*TriggerStats) error {
	spm.mu.RLock()
	defer spm.mu.RUnlock()

	snapshot := StatsSnapshot{
		Timestamp: time.Now(),
		Version:   1,
		Stats:     stats,
		Metadata: map[string]interface{}{
			"total_triggers": len(stats),
			"save_time":      time.Now().Format(time.RFC3339),
		},
	}

	// 生成文件名（包含毫秒以避免重复）
	filename := fmt.Sprintf("stats_%s.json.gz",
		snapshot.Timestamp.Format("20060102_150405.000"))
	filepath := filepath.Join(spm.dataDir, filename)

	// 序列化数据
	data, err := json.MarshalIndent(snapshot, "", "  ")
	if err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigSerializationFailed,
			"统计数据序列化失败",
			fmt.Sprintf("错误: %v", err),
		)
	}

	// 压缩并保存
	if err := spm.saveCompressed(filepath, data); err != nil {
		return err
	}

	spm.logger.Info("统计数据已保存: %s", filename)
	return nil
}

// LoadStats 加载统计数据
func (spm *StatsPersistenceManager) LoadStats() (map[string]*TriggerStats, error) {
	spm.mu.RLock()
	defer spm.mu.RUnlock()

	// 查找最新的统计文件
	latestFile, err := spm.findLatestStatsFile()
	if err != nil {
		return nil, err
	}

	if latestFile == "" {
		spm.logger.Info("未找到统计数据文件，返回空统计")
		return make(map[string]*TriggerStats), nil
	}

	// 加载并解压数据
	data, err := spm.loadCompressed(latestFile)
	if err != nil {
		return nil, err
	}

	// 反序列化
	var snapshot StatsSnapshot
	if err := json.Unmarshal(data, &snapshot); err != nil {
		return nil, errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigParseError,
			"统计数据反序列化失败",
			fmt.Sprintf("文件: %s, 错误: %v", latestFile, err),
		)
	}

	spm.logger.Info("统计数据已加载: %s (版本: %d, 触发器数量: %d)",
		filepath.Base(latestFile), snapshot.Version, len(snapshot.Stats))

	return snapshot.Stats, nil
}

// ExportStats 导出统计数据到指定路径
func (spm *StatsPersistenceManager) ExportStats(stats map[string]*TriggerStats, exportPath string) error {
	snapshot := StatsSnapshot{
		Timestamp: time.Now(),
		Version:   1,
		Stats:     stats,
		Metadata: map[string]interface{}{
			"export_time":    time.Now().Format(time.RFC3339),
			"total_triggers": len(stats),
			"exported_by":    "FlexProxy",
		},
	}

	// 序列化数据
	data, err := json.MarshalIndent(snapshot, "", "  ")
	if err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigSerializationFailed,
			"统计数据序列化失败",
			fmt.Sprintf("错误: %v", err),
		)
	}

	// 根据文件扩展名决定是否压缩
	if filepath.Ext(exportPath) == ".gz" {
		if err := spm.saveCompressed(exportPath, data); err != nil {
			return err
		}
	} else {
		if err := os.WriteFile(exportPath, data, 0644); err != nil {
			return errors.NewErrorWithDetails(
				errors.ErrTypeConfig,
				errors.ErrCodeConfigExportFailed,
				"统计数据导出失败",
				fmt.Sprintf("路径: %s, 错误: %v", exportPath, err),
			)
		}
	}

	spm.logger.Info("统计数据已导出: %s", exportPath)
	return nil
}

// ImportStats 从指定路径导入统计数据
func (spm *StatsPersistenceManager) ImportStats(importPath string) (map[string]*TriggerStats, error) {
	var data []byte
	var err error

	// 根据文件扩展名决定是否解压
	if filepath.Ext(importPath) == ".gz" {
		data, err = spm.loadCompressed(importPath)
	} else {
		data, err = os.ReadFile(importPath)
	}

	if err != nil {
		return nil, errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigFileNotFound,
			"无法读取统计数据文件",
			fmt.Sprintf("路径: %s, 错误: %v", importPath, err),
		)
	}

	// 反序列化
	var snapshot StatsSnapshot
	if err := json.Unmarshal(data, &snapshot); err != nil {
		return nil, errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigParseError,
			"统计数据反序列化失败",
			fmt.Sprintf("文件: %s, 错误: %v", importPath, err),
		)
	}

	spm.logger.Info("统计数据已导入: %s (版本: %d, 触发器数量: %d)",
		filepath.Base(importPath), snapshot.Version, len(snapshot.Stats))

	return snapshot.Stats, nil
}

// CleanupOldData 清理旧数据
func (spm *StatsPersistenceManager) CleanupOldData() error {
	spm.mu.Lock()
	defer spm.mu.Unlock()

	cutoff := time.Now().AddDate(0, 0, -spm.maxHistoryDays)

	files, err := filepath.Glob(filepath.Join(spm.dataDir, "stats_*.json.gz"))
	if err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeFileOperationFailed,
			"扫描统计数据文件失败",
			fmt.Sprintf("错误: %v", err),
		)
	}

	removedCount := 0
	for _, file := range files {
		info, err := os.Stat(file)
		if err != nil {
			continue
		}

		if info.ModTime().Before(cutoff) {
			if err := os.Remove(file); err != nil {
				spm.logger.Warn("删除旧统计文件失败: %s, 错误: %v", file, err)
			} else {
				removedCount++
			}
		}
	}

	spm.logger.Info("清理了 %d 个旧统计数据文件", removedCount)
	return nil
}

// GetHistoryInfo 获取历史数据信息
func (spm *StatsPersistenceManager) GetHistoryInfo() ([]StatsHistoryEntry, error) {
	files, err := filepath.Glob(filepath.Join(spm.dataDir, "stats_*.json.gz"))
	if err != nil {
		return nil, errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeFileOperationFailed,
			"扫描统计数据文件失败",
			fmt.Sprintf("错误: %v", err),
		)
	}

	var history []StatsHistoryEntry
	for _, file := range files {
		info, err := os.Stat(file)
		if err != nil {
			continue
		}

		// 尝试加载文件获取统计数量
		count := 0
		if data, err := spm.loadCompressed(file); err == nil {
			var snapshot StatsSnapshot
			if err := json.Unmarshal(data, &snapshot); err == nil {
				count = len(snapshot.Stats)
			}
		}

		history = append(history, StatsHistoryEntry{
			Date:     info.ModTime().Format("2006-01-02"),
			Filename: filepath.Base(file),
			Size:     info.Size(),
			Count:    count,
		})
	}

	// 按日期排序
	sort.Slice(history, func(i, j int) bool {
		return history[i].Date > history[j].Date
	})

	return history, nil
}

// autoSaveLoop 自动保存循环
func (spm *StatsPersistenceManager) autoSaveLoop() {
	ticker := time.NewTicker(spm.saveInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 这里需要从TriggerManager获取统计数据
			// 实际使用时需要注入TriggerManager的引用
			spm.logger.Info("自动保存定时器触发")
		case <-spm.stopChan:
			return
		}
	}
}

// saveCompressed 压缩保存数据
func (spm *StatsPersistenceManager) saveCompressed(filepath string, data []byte) error {
	file, err := os.Create(filepath)
	if err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeFileWriteFailed,
			"创建统计数据文件失败",
			fmt.Sprintf("路径: %s, 错误: %v", filepath, err),
		)
	}
	defer file.Close()

	gzWriter, err := gzip.NewWriterLevel(file, spm.compressionLevel)
	if err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigSerializationFailed,
			"创建压缩写入器失败",
			fmt.Sprintf("错误: %v", err),
		)
	}
	defer gzWriter.Close()

	if _, err := gzWriter.Write(data); err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeFileWriteFailed,
			"写入压缩数据失败",
			fmt.Sprintf("错误: %v", err),
		)
	}

	return nil
}

// loadCompressed 加载压缩数据
func (spm *StatsPersistenceManager) loadCompressed(filepath string) ([]byte, error) {
	file, err := os.Open(filepath)
	if err != nil {
		return nil, errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeFileReadFailed,
			"打开统计数据文件失败",
			fmt.Sprintf("路径: %s, 错误: %v", filepath, err),
		)
	}
	defer file.Close()

	gzReader, err := gzip.NewReader(file)
	if err != nil {
		return nil, errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigParseError,
			"创建解压读取器失败",
			fmt.Sprintf("错误: %v", err),
		)
	}
	defer gzReader.Close()

	data, err := io.ReadAll(gzReader)
	if err != nil {
		return nil, errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeFileReadFailed,
			"读取压缩数据失败",
			fmt.Sprintf("错误: %v", err),
		)
	}

	return data, nil
}

// findLatestStatsFile 查找最新的统计文件
func (spm *StatsPersistenceManager) findLatestStatsFile() (string, error) {
	files, err := filepath.Glob(filepath.Join(spm.dataDir, "stats_*.json.gz"))
	if err != nil {
		return "", errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeFileOperationFailed,
			"扫描统计数据文件失败",
			fmt.Sprintf("错误: %v", err),
		)
	}

	if len(files) == 0 {
		return "", nil
	}

	// 按修改时间排序，最新的在前
	sort.Slice(files, func(i, j int) bool {
		infoI, errI := os.Stat(files[i])
		infoJ, errJ := os.Stat(files[j])
		if errI != nil || errJ != nil {
			return false
		}
		return infoI.ModTime().After(infoJ.ModTime())
	})

	return files[0], nil
}
