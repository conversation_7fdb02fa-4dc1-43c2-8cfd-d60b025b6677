package trigger

import (
	"net/http"
	"time"

	"github.com/flexp/flexp/common"
)

// EnhancedBodyTrigger 增强的响应体触发器
type EnhancedBodyTrigger struct {
	matcher      *EnhancedPatternMatcher
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// NewEnhancedBodyTrigger 创建增强响应体触发器
func NewEnhancedBodyTrigger(config *common.EnhancedPatternConfig, priority int, stage ProcessStage, actions []common.ActionConfig) *EnhancedBodyTrigger {
	return &EnhancedBodyTrigger{
		matcher:      NewEnhancedPatternMatcher(config),
		Priority:     priority,
		ProcessStage: stage,
		Actions:      actions,
	}
}

// Match 判断响应体是否匹配
func (t *EnhancedBodyTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if t.matcher == nil {
		return false
	}

	if resp == nil {
		triggerLogger.Debug("响应为空，跳过EnhancedBodyTrigger匹配")
		return false
	}

	if resp.Body == nil {
		triggerLogger.Debug("响应体为nil，跳过EnhancedBodyTrigger匹配")
		return false
	}

	// 读取响应体内容（复用原有的逻辑）
	bodyBytes, err := readBodyWithoutConsuming(resp)
	if err != nil {
		triggerLogger.GetRawLogger().Errorf("读取响应体失败: %v", err)
		return false
	}

	if len(bodyBytes) == 0 {
		return false
	}

	// 使用增强模式匹配器进行匹配
	return t.matcher.Match(string(bodyBytes))
}

// GetPriority 获取优先级
func (t *EnhancedBodyTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *EnhancedBodyTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *EnhancedBodyTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// EnhancedURLTrigger 增强的URL触发器
type EnhancedURLTrigger struct {
	matcher      *EnhancedPatternMatcher
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// NewEnhancedURLTrigger 创建增强URL触发器
func NewEnhancedURLTrigger(config *common.EnhancedPatternConfig, priority int, stage ProcessStage, actions []common.ActionConfig) *EnhancedURLTrigger {
	return &EnhancedURLTrigger{
		matcher:      NewEnhancedPatternMatcher(config),
		Priority:     priority,
		ProcessStage: stage,
		Actions:      actions,
	}
}

// Match 判断URL是否匹配
func (t *EnhancedURLTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if t.matcher == nil {
		return false
	}

	if req == nil || req.URL == nil {
		return false
	}

	urlStr := req.URL.String()
	return t.matcher.Match(urlStr)
}

// GetPriority 获取优先级
func (t *EnhancedURLTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *EnhancedURLTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *EnhancedURLTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// EnhancedDomainTrigger 增强的域名触发器
type EnhancedDomainTrigger struct {
	matcher      *EnhancedPatternMatcher
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// NewEnhancedDomainTrigger 创建增强域名触发器
func NewEnhancedDomainTrigger(config *common.EnhancedPatternConfig, priority int, stage ProcessStage, actions []common.ActionConfig) *EnhancedDomainTrigger {
	return &EnhancedDomainTrigger{
		matcher:      NewEnhancedPatternMatcher(config),
		Priority:     priority,
		ProcessStage: stage,
		Actions:      actions,
	}
}

// Match 判断域名是否匹配
func (t *EnhancedDomainTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if t.matcher == nil {
		return false
	}

	if req == nil || req.URL == nil || req.URL.Host == "" {
		return false
	}

	host := req.URL.Host
	return t.matcher.Match(host)
}

// GetPriority 获取优先级
func (t *EnhancedDomainTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *EnhancedDomainTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *EnhancedDomainTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// EnhancedRequestBodyTrigger 增强的请求体触发器
type EnhancedRequestBodyTrigger struct {
	matcher      *EnhancedPatternMatcher
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// NewEnhancedRequestBodyTrigger 创建增强请求体触发器
func NewEnhancedRequestBodyTrigger(config *common.EnhancedPatternConfig, priority int, stage ProcessStage, actions []common.ActionConfig) *EnhancedRequestBodyTrigger {
	return &EnhancedRequestBodyTrigger{
		matcher:      NewEnhancedPatternMatcher(config),
		Priority:     priority,
		ProcessStage: stage,
		Actions:      actions,
	}
}

// Match 判断请求体是否匹配
func (t *EnhancedRequestBodyTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if t.matcher == nil {
		return false
	}

	if req == nil || req.Body == nil {
		return false
	}

	// 读取请求体内容
	bodyBytes, err := readBodyWithoutConsuming(req)
	if err != nil {
		triggerLogger.GetRawLogger().Errorf("读取请求体失败: %v", err)
		return false
	}

	if len(bodyBytes) == 0 {
		return false
	}

	// 使用增强模式匹配器进行匹配
	return t.matcher.Match(string(bodyBytes))
}

// GetPriority 获取优先级
func (t *EnhancedRequestBodyTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *EnhancedRequestBodyTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *EnhancedRequestBodyTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// EnhancedRequestHeaderTrigger 增强的请求头触发器
type EnhancedRequestHeaderTrigger struct {
	matcher      *EnhancedHeaderPatternMatcher
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// NewEnhancedRequestHeaderTrigger 创建增强请求头触发器
func NewEnhancedRequestHeaderTrigger(config *common.EnhancedHeaderPatternConfig, priority int, stage ProcessStage, actions []common.ActionConfig) *EnhancedRequestHeaderTrigger {
	return &EnhancedRequestHeaderTrigger{
		matcher:      NewEnhancedHeaderPatternMatcher(config),
		Priority:     priority,
		ProcessStage: stage,
		Actions:      actions,
	}
}

// Match 判断请求头是否匹配
func (t *EnhancedRequestHeaderTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if t.matcher == nil {
		return false
	}

	if req == nil || req.Header == nil {
		return false
	}

	// 转换头部格式
	headers := make(map[string][]string)
	for name, values := range req.Header {
		headers[name] = values
	}

	return t.matcher.Match(headers)
}

// GetPriority 获取优先级
func (t *EnhancedRequestHeaderTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *EnhancedRequestHeaderTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *EnhancedRequestHeaderTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// EnhancedResponseHeaderTrigger 增强的响应头触发器
type EnhancedResponseHeaderTrigger struct {
	matcher      *EnhancedHeaderPatternMatcher
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// NewEnhancedResponseHeaderTrigger 创建增强响应头触发器
func NewEnhancedResponseHeaderTrigger(config *common.EnhancedHeaderPatternConfig, priority int, stage ProcessStage, actions []common.ActionConfig) *EnhancedResponseHeaderTrigger {
	return &EnhancedResponseHeaderTrigger{
		matcher:      NewEnhancedHeaderPatternMatcher(config),
		Priority:     priority,
		ProcessStage: stage,
		Actions:      actions,
	}
}

// Match 判断响应头是否匹配
func (t *EnhancedResponseHeaderTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if t.matcher == nil {
		return false
	}

	if resp == nil || resp.Header == nil {
		return false
	}

	// 转换头部格式
	headers := make(map[string][]string)
	for name, values := range resp.Header {
		headers[name] = values
	}

	return t.matcher.Match(headers)
}

// GetPriority 获取优先级
func (t *EnhancedResponseHeaderTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *EnhancedResponseHeaderTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *EnhancedResponseHeaderTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}
