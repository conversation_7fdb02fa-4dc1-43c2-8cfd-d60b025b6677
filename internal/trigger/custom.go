package trigger

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/dop251/goja"
	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/common/errors"
)

// CustomTrigger 自定义触发器
type CustomTrigger struct {
	Code         string
	VM           *goja.Runtime
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// NewCustomTrigger 创建自定义触发器
func NewCustomTrigger(code string, priority int, stage ProcessStage, actions []common.ActionConfig) *CustomTrigger {
	vm := goja.New()
	_, err := vm.RunString(`
        function matchRegex(str, pattern) { return new RegExp(pattern).test(str); }
        function parseURL(url) { try { const parsed = new URL(url); return { protocol: parsed.protocol, hostname: parsed.hostname, pathname: parsed.pathname, search: parsed.search, hash: parsed.hash }; } catch(e) { return null; } }
        function base64Encode(str) { return btoa(str); }
        function base64Decode(str) { return atob(str); }
    `)
	if err != nil {
		triggerLogger.GetRawLogger().Errorf("预加载JavaScript工具函数失败: %v", err)
	}
	return &CustomTrigger{
		Code:         code,
		VM:           vm,
		Priority:     priority,
		ProcessStage: stage,
		Actions:      actions,
	}
}

// Match 执行自定义代码判断是否匹配
func (t *CustomTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	options := map[string]interface{}{
		"url":         req.URL.String(),
		"method":      req.Method,
		"time_passed": reqTime.Milliseconds(),
		"headers":     getHeadersMap(req.Header),
	}
	if req.Body != nil {
		bodyBytes, err := readBodyWithoutConsuming(req)
		if err == nil {
			options["request_body"] = string(bodyBytes)
		}
	}
	if resp != nil {
		options["status"] = resp.StatusCode
		options["response_headers"] = getHeadersMap(resp.Header)
		if resp.Body != nil {
			bodyBytes, err := readBodyWithoutConsuming(resp)
			if err == nil {
				options["response_body"] = string(bodyBytes)
			}
		}
	}
	err := t.VM.Set("opt", options)
	if err != nil {
		return false
	}
	val, err := t.VM.RunString(t.Code)
	if err != nil {
		return false
	}
	result, ok := val.Export().(bool)
	if !ok {
		return false
	}
	return result
}

func (t *CustomTrigger) GetPriority() int                  { return t.Priority }
func (t *CustomTrigger) GetProcessStage() ProcessStage     { return t.ProcessStage }
func (t *CustomTrigger) GetActions() []common.ActionConfig { return t.Actions }

// GetMatchedActions 获取匹配的动作
func (t *CustomTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []common.ActionConfig{}
}

type AdvancedCustomTrigger struct {
	Code         string
	VM           *goja.Runtime
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
	Presets      map[string]string
	Libraries    []string
}

func NewAdvancedCustomTrigger(code string, priority int, stage ProcessStage, actions []common.ActionConfig, presets map[string]string, libraries []string) *AdvancedCustomTrigger {
	vm := goja.New()
	return &AdvancedCustomTrigger{Code: code, VM: vm, Priority: priority, ProcessStage: stage, Actions: actions, Presets: presets, Libraries: libraries}
}
func (t *AdvancedCustomTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	options := map[string]interface{}{"url": req.URL.String(), "method": req.Method} // 简化版本
	t.VM.Set("opt", options)
	val, err := t.VM.RunString(t.Code)
	if err != nil {
		return false
	}
	result, _ := val.Export().(bool)
	return result
}
func (t *AdvancedCustomTrigger) GetPriority() int                  { return t.Priority }
func (t *AdvancedCustomTrigger) GetProcessStage() ProcessStage     { return t.ProcessStage }
func (t *AdvancedCustomTrigger) GetActions() []common.ActionConfig { return t.Actions }

// GetMatchedActions 获取匹配的动作
func (t *AdvancedCustomTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []common.ActionConfig{}
}

type MultiConditionTrigger struct {
	Conditions    []Trigger
	LogicOperator string
	Priority      int
	ProcessStage  ProcessStage
	Actions       []common.ActionConfig
}

func NewMultiConditionTrigger(conditions []Trigger, operator string, priority int, stage ProcessStage, actions []common.ActionConfig) *MultiConditionTrigger {
	return &MultiConditionTrigger{Conditions: conditions, LogicOperator: operator, Priority: priority, ProcessStage: stage, Actions: actions}
}
func (t *MultiConditionTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if len(t.Conditions) == 0 {
		return false
	}
	switch t.LogicOperator {
	case "AND":
		for _, cond := range t.Conditions {
			if !cond.Match(req, resp, reqTime) {
				return false
			}
		}
		return true
	case "OR":
		for _, cond := range t.Conditions {
			if cond.Match(req, resp, reqTime) {
				return true
			}
		}
		return false
	case "NOT":
		if len(t.Conditions) > 0 {
			return !t.Conditions[0].Match(req, resp, reqTime)
		}
		return false
	default:
		for _, cond := range t.Conditions {
			if cond.Match(req, resp, reqTime) {
				return true
			}
		}
		return false
	}
}
func (t *MultiConditionTrigger) GetPriority() int                  { return t.Priority }
func (t *MultiConditionTrigger) GetProcessStage() ProcessStage     { return t.ProcessStage }
func (t *MultiConditionTrigger) GetActions() []common.ActionConfig { return t.Actions }

// GetMatchedActions 获取匹配的动作
func (t *MultiConditionTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []common.ActionConfig{}
}

type RegexMatchTrigger struct {
	Patterns     map[string]*regexp.Regexp
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
	MatchAll     bool
}

func NewRegexMatchTrigger(patterns map[string]string, matchAll bool, priority int, stage ProcessStage, actions []common.ActionConfig) *RegexMatchTrigger {
	compiledPatterns := make(map[string]*regexp.Regexp)
	for key, pattern := range patterns {
		if compiled, err := regexp.Compile(pattern); err == nil {
			compiledPatterns[key] = compiled
		} else {
			triggerLogger.GetRawLogger().Warnf("RegexMatchTrigger的正则表达式无效 '%s': %v", pattern, err)
		}
	}
	return &RegexMatchTrigger{Patterns: compiledPatterns, MatchAll: matchAll, Priority: priority, ProcessStage: stage, Actions: actions}
}
func (t *RegexMatchTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if len(t.Patterns) == 0 {
		return false
	}
	matches := 0
	expectedMatches := 0
	for key, pattern := range t.Patterns {
		expectedMatches++
		var textToMatch string
		switch key {
		case "url":
			if req != nil && req.URL != nil {
				textToMatch = req.URL.String()
			}
		case "domain":
			if req != nil && req.URL != nil {
				textToMatch = req.URL.Hostname()
			}
		// TODO: 添加更多情况：request_header, request_body, status, response_header, response_body
		default:
			continue
		}
		if textToMatch != "" && pattern.MatchString(textToMatch) {
			matches++
		}
	}
	if t.MatchAll {
		return matches == expectedMatches && expectedMatches > 0
	}
	return matches > 0
}
func (t *RegexMatchTrigger) GetPriority() int                  { return t.Priority }
func (t *RegexMatchTrigger) GetProcessStage() ProcessStage     { return t.ProcessStage }
func (t *RegexMatchTrigger) GetActions() []common.ActionConfig { return t.Actions }

// GetMatchedActions 获取匹配的动作
func (t *RegexMatchTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []common.ActionConfig{}
}

func readBodyWithoutConsuming(obj interface{}) ([]byte, error) {
	var body io.ReadCloser
	var err error
	var bodyBytes []byte
	switch v := obj.(type) {
	case *http.Request:
		if v.Body == nil {
			return nil, nil
		}
		body = v.Body
		bodyBytes, err = io.ReadAll(body)
		if err != nil {
			return nil, err
		}
		v.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	case *http.Response:
		if v.Body == nil {
			return nil, nil
		}
		body = v.Body
		bodyBytes, err = io.ReadAll(body)
		if err != nil {
			return nil, err
		}
		v.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	default:
		return nil, errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "不支持的类型", fmt.Sprintf("type: %T", obj))
	}
	return bodyBytes, nil
}
func getHeadersMap(header http.Header) map[string]string {
	headers := make(map[string]string)
	for name, values := range header {
		if len(values) > 0 {
			headers[name] = strings.Join(values, ", ")
		}
	}
	return headers
}
