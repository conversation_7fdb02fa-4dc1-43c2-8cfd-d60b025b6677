package action

import (
	"context"
	"net"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/flexp/flexp/common/constants"
	"github.com/flexp/flexp/common/errors"
)

// extractRealIP 从HTTP请求中提取真实IP地址
// 按优先级检查多个HTTP头部字段
func extractRealIP(req *http.Request) string {
	// 优先级顺序：X-Real-IP > X-Forwarded-For > 其他代理头部 > RemoteAddr

	// 1. 检查 X-Real-IP 头部
	if realIP := req.Header.Get(constants.HeaderXRealIP); realIP != "" {
		return strings.TrimSpace(realIP)
	}

	// 2. 检查 X-Forwarded-For 头部（可能包含多个IP，取第一个）
	if forwardedFor := req.Header.Get(constants.HeaderXForwardedFor); forwardedFor != "" {
		ips := strings.Split(forwardedFor, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}

	// 3. 检查其他常见的代理头部
	proxyHeaders := []string{
		constants.HeaderCFConnectingIP,   // Cloudflare
		constants.HeaderTrueClientIP,     // Akamai
		constants.HeaderXClientIP,        // 通用
		constants.HeaderXClusterClientIP, // 集群
	}

	for _, header := range proxyHeaders {
		if ip := req.Header.Get(header); ip != "" {
			return strings.TrimSpace(ip)
		}
	}

	// 4. 最后使用 RemoteAddr（需要去掉端口号）
	if req.RemoteAddr != "" {
		host, _, err := net.SplitHostPort(req.RemoteAddr)
		if err == nil {
			return host
		}
		// 如果没有端口号，直接返回
		return req.RemoteAddr
	}

	return ""
}

// extractIPFromContext 从上下文或参数中提取IP地址
func extractIPFromContext(ctx context.Context, parameters map[string]interface{}) string {
	// 首先尝试从参数中获取
	if val, ok := parameters["ip"].(string); ok && val != "" {
		return val
	}

	// 然后尝试从HTTP请求上下文中获取
	if req := ctx.Value(constants.HTTPRequestContextKey); req != nil {
		if httpReq, ok := req.(*http.Request); ok {
			return extractRealIP(httpReq)
		}
	}

	return ""
}

// extractDomainFromContext 从上下文或参数中提取域名
func extractDomainFromContext(ctx context.Context, parameters map[string]interface{}) string {
	// 首先尝试从参数中获取
	if val, ok := parameters["domain"].(string); ok && val != "" {
		return val
	}

	// 然后尝试从HTTP请求URL中获取
	if req := ctx.Value(constants.HTTPRequestContextKey); req != nil {
		if httpReq, ok := req.(*http.Request); ok {
			return httpReq.URL.Hostname()
		}
	}

	return ""
}

// parseDuration 解析封禁时长参数
// 支持int、float64、string类型，包括特殊值"reboot"
func parseDuration(durationParam interface{}) int {
	if durationParam == nil {
		return constants.DefaultBanDurationSeconds
	}

	switch v := durationParam.(type) {
	case int:
		if v > 0 {
			return v
		}
	case float64:
		if v > 0 {
			return int(v)
		}
	case string:
		if v == constants.BanDurationReboot {
			return constants.RebootBanDurationSeconds
		}
		if d, err := strconv.Atoi(v); err == nil && d > 0 {
			return d
		}
	}

	return constants.DefaultBanDurationSeconds
}

// validateHTTPStatusCode 验证HTTP状态码是否在有效范围内
func validateHTTPStatusCode(statusCode int) int {
	if statusCode >= constants.MinHTTPStatusCode && statusCode <= constants.MaxHTTPStatusCode {
		return statusCode
	}
	return constants.DefaultBlockStatusCode
}

// createIPBanError 创建IP封禁相关错误
func createIPBanError(ip string, duration int, cause error) error {
	if cause != nil {
		return errors.WrapErrorWithDetails(
			cause,
			errors.ErrTypeAction,
			errors.ErrCodeActionExecutionFailed,
			"IP封禁执行失败",
			"ip="+ip+", duration="+strconv.Itoa(duration),
		)
	}
	return errors.ErrIPBanFailed
}

// createDomainBanError 创建域名封禁相关错误
func createDomainBanError(domain string, duration int, cause error) error {
	if cause != nil {
		return errors.WrapErrorWithDetails(
			cause,
			errors.ErrTypeAction,
			errors.ErrCodeActionExecutionFailed,
			"域名封禁执行失败",
			"domain="+domain+", duration="+strconv.Itoa(duration),
		)
	}
	return errors.ErrDomainBanFailed
}

// logIPBanSuccess 记录IP封禁成功日志
func logIPBanSuccess(logger interface{ Info(string, ...interface{}) }, ip string, duration int) {
	logger.Info("IP封禁成功: ip=%s, duration=%d秒", ip, duration)
}

// logDomainBanSuccess 记录域名封禁成功日志
func logDomainBanSuccess(logger interface{ Info(string, ...interface{}) }, domain string, duration int, scope string, permanent bool) {
	logger.Info("域名封禁成功: domain=%s, duration=%d秒, scope=%s, permanent=%v", domain, duration, scope, permanent)
}

// logIPBanError 记录IP封禁失败日志
func logIPBanError(logger interface{ Error(string, ...interface{}) }, ip string, duration int, err error) {
	logger.Error("IP封禁失败: ip=%s, duration=%d, error=%v", ip, duration, err)
}

// logDomainBanError 记录域名封禁失败日志
func logDomainBanError(logger interface{ Error(string, ...interface{}) }, domain string, duration int, scope string, permanent bool, err error) {
	logger.Error("域名封禁失败: domain=%s, duration=%d, scope=%s, permanent=%v, error=%v", domain, duration, scope, permanent, err)
}

// logHTTPRequestBlocked 记录HTTP请求阻止日志
func logHTTPRequestBlocked(logger interface{ Info(string, ...interface{}) }, req *http.Request, reason string, statusCode int) {
	logger.Info("HTTP请求已被阻止: method=%s, url=%s, reason=%s, status_code=%d, remote_addr=%s",
		req.Method, req.URL.String(), reason, statusCode, req.RemoteAddr)
}

// logParameterMissingError 记录参数缺失错误日志
func logParameterMissingError(logger interface{ Error(string, ...interface{}) }, paramName string) {
	logger.Error("无法获取要封禁的%s", paramName)
}

// =============================================================================
// 重试执行器相关辅助函数
// =============================================================================

// parseRetryCount 解析重试次数参数
func parseRetryCount(parameters map[string]interface{}) int {
	// 优先检查 retry_count 参数
	if val, ok := parameters["retry_count"]; ok {
		count := convertToInt(val)
		if count >= 0 { // 允许0值，用于表示无效的重试次数
			return count
		}
	}

	// 然后检查 attempts 参数
	if val, ok := parameters["attempts"]; ok {
		count := convertToInt(val)
		if count >= 0 { // 允许0值，用于表示无效的重试次数
			return count
		}
	}

	// 返回默认值
	return constants.DefaultMaxRetries
}

// parseRetryDelay 解析重试延迟参数
func parseRetryDelay(parameters map[string]interface{}) string {
	if val, ok := parameters["delay"].(string); ok && val != "" {
		return val
	}
	return constants.RetryExecutorDefaultDelay
}

// parseRotationMode 解析IP轮换模式参数
func parseRotationMode(parameters map[string]interface{}) string {
	if val, ok := parameters["rotation_mode"].(string); ok && val != "" {
		// 验证轮换模式是否有效
		switch val {
		case constants.StrategyRandom, constants.StrategySequential, constants.StrategySmart, constants.StrategyQuality:
			return val
		}
	}
	return constants.StrategyRandom // 默认随机模式
}

// parseBypassTimeout 解析绕过代理的超时参数
func parseBypassTimeout(parameters map[string]interface{}) int {
	// 优先检查 timeout_ms 参数
	if val, ok := parameters["timeout_ms"]; ok {
		if timeout := convertToInt(val); timeout > 0 {
			return timeout
		}
	}

	// 然后检查 timeout 参数
	if val, ok := parameters["timeout"]; ok {
		if timeout := convertToInt(val); timeout > 0 {
			return timeout
		}
	}

	// 返回默认值（30秒）
	return constants.BypassProxyDefaultTimeout
}

// parseKeepAlive 解析是否保持连接参数
func parseKeepAlive(parameters map[string]interface{}) bool {
	if val, ok := parameters["keep_alive"].(bool); ok {
		return val
	}
	return false // 默认不保持连接
}

// parseDNSMode 解析DNS模式参数
func parseDNSMode(parameters map[string]interface{}) string {
	if val, ok := parameters["dns_mode"].(string); ok && val != "" {
		// 验证DNS模式是否有效
		switch val {
		case constants.DNSModeLocal, constants.DNSModeRemote, constants.DNSModeCustom:
			return val
		}
	}
	return constants.DNSModeLocal // 默认本地DNS
}

// convertToInt 将interface{}转换为int
func convertToInt(val interface{}) int {
	switch v := val.(type) {
	case int:
		return v
	case float64:
		return int(v)
	case string:
		if i, err := strconv.Atoi(v); err == nil {
			return i
		}
	}
	return 0
}

// getCurrentProxy 从上下文中获取当前代理
func getCurrentProxy(ctx context.Context) string {
	if proxy, ok := ctx.Value("current_proxy").(string); ok {
		return proxy
	}
	return ""
}

// =============================================================================
// 参数验证函数
// =============================================================================

// isValidRetryCount 验证重试次数是否有效
func isValidRetryCount(val interface{}) bool {
	count := convertToInt(val)
	return count > 0 && count <= constants.DefaultMaxRetryAttempts
}

// isValidDelay 验证延迟参数是否有效
func isValidDelay(val interface{}) bool {
	if str, ok := val.(string); ok {
		// 尝试解析为时间间隔
		if _, err := time.ParseDuration(str); err == nil {
			return true
		}
	}
	return false
}

// isValidRotationMode 验证IP轮换模式是否有效
func isValidRotationMode(val interface{}) bool {
	if mode, ok := val.(string); ok {
		switch mode {
		case constants.StrategyRandom, constants.StrategySequential, constants.StrategySmart, constants.StrategyQuality:
			return true
		}
	}
	return false
}

// isValidTimeout 验证超时参数是否有效
func isValidTimeout(val interface{}) bool {
	timeout := convertToInt(val)
	return timeout > 0 && timeout <= constants.BypassProxyMaxTimeout
}

// isValidDNSMode 验证DNS模式是否有效
func isValidDNSMode(val interface{}) bool {
	if mode, ok := val.(string); ok {
		switch mode {
		case constants.DNSModeLocal, constants.DNSModeRemote, constants.DNSModeCustom:
			return true
		}
	}
	return false
}

// =============================================================================
// 上下文设置函数
// =============================================================================

// setRetrySameContext 在上下文中设置使用相同代理重试的标记
func setRetrySameContext(ctx context.Context, retryCount int, delay string, proxy string) error {
	// 由于Go的context是不可变的，我们通过在HTTP请求中设置特殊头部来传递重试信息
	// 这些头部会被HTTP处理流程识别并处理

	if req := ctx.Value(constants.HTTPRequestContextKey); req != nil {
		if httpReq, ok := req.(*http.Request); ok {
			// 设置重试相关的内部头部
			httpReq.Header.Set(constants.HeaderRetrySame, "true")
			httpReq.Header.Set(constants.HeaderRetryCount, strconv.Itoa(retryCount))
			httpReq.Header.Set(constants.HeaderRetryDelay, delay)
			if proxy != "" {
				httpReq.Header.Set(constants.HeaderRetryProxy, proxy)
			}
		}
	}

	return nil
}

// setRetryContext 在上下文中设置使用新代理重试的标记
func setRetryContext(ctx context.Context, retryCount int, delay string, rotationMode string) error {
	// 设置使用新代理重试的标记
	if req := ctx.Value(constants.HTTPRequestContextKey); req != nil {
		if httpReq, ok := req.(*http.Request); ok {
			// 设置重试相关的内部头部
			httpReq.Header.Set(constants.HeaderRetryNew, "true")
			httpReq.Header.Set(constants.HeaderRetryCount, strconv.Itoa(retryCount))
			httpReq.Header.Set(constants.HeaderRetryDelay, delay)
			httpReq.Header.Set(constants.HeaderRotationMode, rotationMode)
		}
	}

	return nil
}

// setBypassProxyContext 在上下文中设置绕过代理的标记
func setBypassProxyContext(ctx context.Context, timeout int, keepAlive bool, dnsMode string) error {
	// 设置绕过代理相关的标记
	if req := ctx.Value(constants.HTTPRequestContextKey); req != nil {
		if httpReq, ok := req.(*http.Request); ok {
			// 设置绕过代理相关的内部头部
			httpReq.Header.Set(constants.HeaderBypass, "true")
			httpReq.Header.Set(constants.HeaderBypassTimeout, strconv.Itoa(timeout))
			httpReq.Header.Set(constants.HeaderBypassKeepAlive, strconv.FormatBool(keepAlive))
			httpReq.Header.Set(constants.HeaderBypassDNS, dnsMode)
		}
	}

	return nil
}

// =============================================================================
// 日志记录函数
// =============================================================================

// logRetrySameSuccess 记录使用相同代理重试成功日志
func logRetrySameSuccess(logger interface{ Info(string, ...interface{}) }, retryCount int, delay string, proxy string) {
	if proxy != "" {
		logger.Info(constants.LogMsgRetrySameSuccess, retryCount, delay, proxy)
	} else {
		logger.Info(constants.LogMsgRetrySameSuccessNoProxy, retryCount, delay)
	}
}

// logRetrySuccess 记录使用新代理重试成功日志
func logRetrySuccess(logger interface{ Info(string, ...interface{}) }, retryCount int, delay string, rotationMode string, proxyCount int) {
	logger.Info(constants.LogMsgRetrySuccess, retryCount, delay, rotationMode, proxyCount)
}

// logBypassProxySuccess 记录绕过代理成功日志
func logBypassProxySuccess(logger interface{ Info(string, ...interface{}) }, timeout int, keepAlive bool, dnsMode string) {
	logger.Info(constants.LogMsgBypassProxySuccess, timeout, keepAlive, dnsMode)
}

// =============================================================================
// 缓存响应执行器相关辅助函数
// =============================================================================

// parseCacheDuration 解析缓存时间参数
func parseCacheDuration(parameters map[string]interface{}) int {
	if val, ok := parameters["duration"]; ok {
		if duration := convertToInt(val); duration > 0 {
			return duration
		}
	}
	return constants.CacheResponseDefaultDuration
}

// parseCacheKey 解析缓存键参数
func parseCacheKey(parameters map[string]interface{}, ctx context.Context) string {
	if val, ok := parameters["key"].(string); ok && val != "" {
		return val
	}

	// 如果没有指定缓存键，尝试从请求中生成
	if req := ctx.Value(constants.HTTPRequestContextKey); req != nil {
		if httpReq, ok := req.(*http.Request); ok {
			// 使用URL作为默认缓存键
			return httpReq.URL.String()
		}
	}

	return constants.CacheResponseDefaultKey
}

// parseCacheScope 解析缓存作用域参数
func parseCacheScope(parameters map[string]interface{}) string {
	if val, ok := parameters["cache_scope"].(string); ok && val != "" {
		return val
	}
	return "global" // 默认全局作用域
}

// setCacheResponseContext 在上下文中设置缓存响应标记
func setCacheResponseContext(ctx context.Context, duration int, cacheKey string, cacheScope string) error {
	if req := ctx.Value(constants.HTTPRequestContextKey); req != nil {
		if httpReq, ok := req.(*http.Request); ok {
			httpReq.Header.Set(constants.HeaderCacheResponse, "true")
			httpReq.Header.Set(constants.HeaderCacheDuration, strconv.Itoa(duration))
			httpReq.Header.Set(constants.HeaderCacheKey, cacheKey)
			httpReq.Header.Set(constants.HeaderCacheScope, cacheScope)
		}
	}
	return nil
}

// logCacheResponseSuccess 记录缓存响应成功日志
func logCacheResponseSuccess(logger interface{ Info(string, ...interface{}) }, duration int, cacheKey string, cacheScope string) {
	logger.Info(constants.LogMsgCacheResponseSuccess, duration, cacheKey, cacheScope)
}

// isValidCacheDuration 验证缓存时间是否有效
func isValidCacheDuration(val interface{}) bool {
	duration := convertToInt(val)
	return duration > 0 && duration <= constants.CacheResponseMaxDuration
}

// isValidCacheKey 验证缓存键是否有效
func isValidCacheKey(val interface{}) bool {
	if key, ok := val.(string); ok {
		return len(key) > 0 && len(key) <= 255 // 限制缓存键长度
	}
	return false
}

// =============================================================================
// 保存到代理池执行器相关辅助函数
// =============================================================================

// parseQualityTier 解析质量等级参数
func parseQualityTier(parameters map[string]interface{}) string {
	if val, ok := parameters["quality_tier"].(string); ok && val != "" {
		// 验证质量等级是否有效
		switch val {
		case "high", "medium", "low", "auto":
			return val
		}
	}
	return constants.SaveToPoolDefaultQuality
}

// parseDomainSpecific 解析是否域名特定参数
func parseDomainSpecific(parameters map[string]interface{}) bool {
	if val, ok := parameters["domain_specific"].(bool); ok {
		return val
	}
	return false // 默认不是域名特定
}

// parseMinScore 解析最小分数参数
func parseMinScore(parameters map[string]interface{}) float64 {
	if val, ok := parameters["min_score"]; ok {
		switch v := val.(type) {
		case float64:
			if v >= 0.0 && v <= 100.0 {
				return v
			}
		case int:
			if v >= 0 && v <= 100 {
				return float64(v)
			}
		case string:
			if score, err := strconv.ParseFloat(v, 64); err == nil && score >= 0.0 && score <= 100.0 {
				return score
			}
		}
	}
	return constants.SaveToPoolDefaultScore
}

// parsePoolName 解析代理池名称参数
func parsePoolName(parameters map[string]interface{}) string {
	if val, ok := parameters["pool_name"].(string); ok && val != "" {
		return val
	}
	return constants.SaveToPoolDefaultPool
}

// setSaveToPoolContext 在上下文中设置保存到代理池标记
func setSaveToPoolContext(ctx context.Context, qualityTier string, minScore float64, poolName string, domainSpecific bool, proxy string, domain string) error {
	if req := ctx.Value(constants.HTTPRequestContextKey); req != nil {
		if httpReq, ok := req.(*http.Request); ok {
			httpReq.Header.Set(constants.HeaderSaveToPool, "true")
			httpReq.Header.Set(constants.HeaderQualityTier, qualityTier)
			httpReq.Header.Set(constants.HeaderMinScore, strconv.FormatFloat(minScore, 'f', 1, 64))
			httpReq.Header.Set(constants.HeaderPoolName, poolName)
			if domainSpecific && domain != "" {
				httpReq.Header.Set("X-FlexProxy-Domain-Specific", "true")
				httpReq.Header.Set("X-FlexProxy-Target-Domain", domain)
			}
			if proxy != "" {
				httpReq.Header.Set("X-FlexProxy-Current-Proxy", proxy)
			}
		}
	}
	return nil
}

// logSaveToPoolSuccess 记录保存到代理池成功日志
func logSaveToPoolSuccess(logger interface{ Info(string, ...interface{}) }, qualityTier string, minScore float64, poolName string, domainSpecific bool) {
	logger.Info(constants.LogMsgSaveToPoolSuccess, qualityTier, minScore, poolName, domainSpecific)
}

// isValidQualityTier 验证质量等级是否有效
func isValidQualityTier(val interface{}) bool {
	if tier, ok := val.(string); ok {
		switch tier {
		case "high", "medium", "low", "auto":
			return true
		}
	}
	return false
}

// isValidMinScore 验证最小分数是否有效
func isValidMinScore(val interface{}) bool {
	switch v := val.(type) {
	case float64:
		return v >= 0.0 && v <= 100.0
	case int:
		return v >= 0 && v <= 100
	case string:
		if score, err := strconv.ParseFloat(v, 64); err == nil {
			return score >= 0.0 && score <= 100.0
		}
	}
	return false
}

// isValidPoolName 验证代理池名称是否有效
func isValidPoolName(val interface{}) bool {
	if name, ok := val.(string); ok {
		return len(name) > 0 && len(name) <= 100 // 限制代理池名称长度
	}
	return false
}

// =============================================================================
// 空响应执行器相关辅助函数
// =============================================================================

// parseStatusCode 解析状态码参数
func parseStatusCode(parameters map[string]interface{}) int {
	if val, ok := parameters["status_code"]; ok {
		if code := convertToInt(val); code >= 100 && code <= 599 {
			return code
		}
	}
	return constants.NullResponseDefaultStatus
}

// parseContentType 解析内容类型参数
func parseContentType(parameters map[string]interface{}) string {
	if val, ok := parameters["content_type"].(string); ok && val != "" {
		return val
	}
	return constants.NullResponseDefaultContentType
}

// parseResponseBody 解析响应体参数
func parseResponseBody(parameters map[string]interface{}) string {
	if val, ok := parameters["body"].(string); ok {
		return val
	}
	return constants.NullResponseDefaultBody
}

// parseCustomHeaders 解析自定义头部参数
func parseCustomHeaders(parameters map[string]interface{}) map[string]string {
	headers := make(map[string]string)

	// 支持多种格式的头部定义
	if val, ok := parameters["headers"]; ok {
		switch v := val.(type) {
		case string:
			// 格式: "Key1:Value1,Key2:Value2"
			if v != "" {
				pairs := strings.Split(v, ",")
				for _, pair := range pairs {
					if kv := strings.SplitN(strings.TrimSpace(pair), ":", 2); len(kv) == 2 {
						headers[strings.TrimSpace(kv[0])] = strings.TrimSpace(kv[1])
					}
				}
			}
		case map[string]interface{}:
			// 格式: {"Key1": "Value1", "Key2": "Value2"}
			for k, v := range v {
				if str, ok := v.(string); ok {
					headers[k] = str
				}
			}
		case map[string]string:
			// 直接的字符串映射
			headers = v
		}
	}

	return headers
}

// setNullResponseContext 在上下文中设置空响应标记
func setNullResponseContext(ctx context.Context, statusCode int, contentType string, body string, customHeaders map[string]string) error {
	if req := ctx.Value(constants.HTTPRequestContextKey); req != nil {
		if httpReq, ok := req.(*http.Request); ok {
			httpReq.Header.Set(constants.HeaderNullResponse, "true")
			httpReq.Header.Set(constants.HeaderNullStatus, strconv.Itoa(statusCode))
			httpReq.Header.Set(constants.HeaderNullContentType, contentType)

			// 设置响应体（通过特殊头部传递）
			if body != "" {
				httpReq.Header.Set("X-FlexProxy-Null-Body", body)
			}

			// 设置自定义头部
			if len(customHeaders) > 0 {
				for key, value := range customHeaders {
					httpReq.Header.Set("X-FlexProxy-Custom-"+key, value)
				}
			}
		}
	}
	return nil
}

// logNullResponseSuccess 记录空响应成功日志
func logNullResponseSuccess(logger interface{ Info(string, ...interface{}) }, statusCode int, contentType string, bodyLength int) {
	logger.Info(constants.LogMsgNullResponseSuccess, statusCode, contentType, bodyLength)
}

// isValidStatusCode 验证状态码是否有效
func isValidStatusCode(val interface{}) bool {
	code := convertToInt(val)
	return code >= 100 && code <= 599 // HTTP状态码范围
}

// isValidContentType 验证内容类型是否有效
func isValidContentType(val interface{}) bool {
	if contentType, ok := val.(string); ok {
		// 简单验证：包含斜杠且不为空
		return len(contentType) > 0 && strings.Contains(contentType, "/")
	}
	return false
}

// isValidResponseBody 验证响应体是否有效
func isValidResponseBody(val interface{}) bool {
	if body, ok := val.(string); ok {
		// 限制响应体大小（1MB）
		return len(body) <= 1024*1024
	}
	return false
}

// =============================================================================
// 脚本执行器相关辅助函数
// =============================================================================

// parseScriptEngine 解析脚本引擎参数
func parseScriptEngine(parameters map[string]interface{}) string {
	if val, ok := parameters["engine"].(string); ok && val != "" {
		switch strings.ToLower(val) {
		case "javascript", "js":
			return "javascript"
		case "lua":
			return "lua"
		}
	}
	return constants.ScriptExecutorDefaultEngine
}

// parseScriptContent 解析脚本内容参数
func parseScriptContent(parameters map[string]interface{}) (string, string, error) {
	// 检查内联脚本
	if script, ok := parameters["script"].(string); ok && script != "" {
		return script, "inline", nil
	}

	// 检查脚本文件路径
	if filePath, ok := parameters["script_file"].(string); ok && filePath != "" {
		// 这里应该读取文件内容，但为了简化，我们只返回文件路径
		// 实际实现中应该读取文件并验证文件存在性
		return filePath, "file", nil
	}

	return "", "", errors.ErrScriptContentInvalid
}

// parseScriptTimeout 解析脚本超时时间参数
func parseScriptTimeout(parameters map[string]interface{}) int {
	if val, ok := parameters["timeout"]; ok {
		if timeout := convertToInt(val); timeout > 0 {
			return timeout
		}
	}
	return constants.ScriptExecutorDefaultTimeout
}

// setScriptExecutorContext 在上下文中设置脚本执行标记
func setScriptExecutorContext(ctx context.Context, engine string, script string, timeout int, scriptType string) error {
	if req := ctx.Value(constants.HTTPRequestContextKey); req != nil {
		if httpReq, ok := req.(*http.Request); ok {
			httpReq.Header.Set(constants.HeaderScriptExecutor, "true")
			httpReq.Header.Set(constants.HeaderScriptEngine, engine)
			httpReq.Header.Set(constants.HeaderScriptTimeout, strconv.Itoa(timeout))
			httpReq.Header.Set(constants.HeaderScriptType, scriptType)
			// 脚本内容通过特殊头部传递（实际实现中可能需要更安全的方式）
			if len(script) <= 8192 { // 限制头部大小
				httpReq.Header.Set("X-FlexProxy-Script-Content", script)
			}
		}
	}
	return nil
}

// logScriptExecutorSuccess 记录脚本执行成功日志
func logScriptExecutorSuccess(logger interface{ Info(string, ...interface{}) }, engine string, timeout int, scriptType string, scriptLength int) {
	logger.Info(constants.LogMsgScriptExecutorSuccess, engine, timeout, scriptType, scriptLength)
}

// isValidScriptEngine 验证脚本引擎是否有效
func isValidScriptEngine(val interface{}) bool {
	if engine, ok := val.(string); ok {
		switch strings.ToLower(engine) {
		case "javascript", "js", "lua":
			return true
		}
	}
	return false
}

// isValidScriptTimeout 验证脚本超时时间是否有效
func isValidScriptTimeout(val interface{}) bool {
	timeout := convertToInt(val)
	return timeout > 0 && timeout <= constants.ScriptExecutorMaxTimeout
}

// =============================================================================
// 请求URL执行器相关辅助函数
// =============================================================================

// parseRequestURL 解析请求URL参数
func parseRequestURL(parameters map[string]interface{}) (string, error) {
	if url, ok := parameters["url"].(string); ok && url != "" {
		// 简单的URL格式验证
		if strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://") {
			return url, nil
		}
		return "", errors.ErrURLInvalid
	}
	return "", errors.ErrURLInvalid
}

// parseHTTPMethod 解析HTTP方法参数
func parseHTTPMethod(parameters map[string]interface{}) string {
	if method, ok := parameters["method"].(string); ok && method != "" {
		method = strings.ToUpper(method)
		switch method {
		case "GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS":
			return method
		}
	}
	return constants.RequestURLDefaultMethod
}

// parseRequestTimeout 解析请求超时时间参数
func parseRequestTimeout(parameters map[string]interface{}) int {
	if val, ok := parameters["timeout"]; ok {
		if timeout := convertToInt(val); timeout > 0 {
			return timeout
		}
	}
	return constants.RequestURLDefaultTimeout
}

// parseRequestRetries 解析请求重试次数参数
func parseRequestRetries(parameters map[string]interface{}) int {
	if val, ok := parameters["retries"]; ok {
		if retries := convertToInt(val); retries >= 0 && retries <= constants.RequestURLMaxRetries {
			return retries
		}
	}
	return 0 // 默认不重试
}

// parseRequestHeaders 解析请求头参数
func parseRequestHeaders(parameters map[string]interface{}) map[string]string {
	headers := make(map[string]string)

	if val, ok := parameters["headers"]; ok {
		switch v := val.(type) {
		case string:
			// 格式: "Key1:Value1,Key2:Value2"
			if v != "" {
				pairs := strings.Split(v, ",")
				for _, pair := range pairs {
					if kv := strings.SplitN(strings.TrimSpace(pair), ":", 2); len(kv) == 2 {
						headers[strings.TrimSpace(kv[0])] = strings.TrimSpace(kv[1])
					}
				}
			}
		case map[string]interface{}:
			for k, v := range v {
				if str, ok := v.(string); ok {
					headers[k] = str
				}
			}
		case map[string]string:
			headers = v
		}
	}

	// 设置默认User-Agent
	if _, exists := headers["User-Agent"]; !exists {
		headers["User-Agent"] = constants.RequestURLDefaultUserAgent
	}

	return headers
}

// parseRequestBody 解析请求体参数
func parseRequestBody(parameters map[string]interface{}) string {
	if body, ok := parameters["body"].(string); ok {
		return body
	}
	return ""
}

// parseFollowRedirect 解析是否跟随重定向参数
func parseFollowRedirect(parameters map[string]interface{}) bool {
	if val, ok := parameters["follow_redirect"].(bool); ok {
		return val
	}
	return true // 默认跟随重定向
}

// setRequestURLContext 在上下文中设置请求URL标记
func setRequestURLContext(ctx context.Context, url string, method string, timeout int, retries int, headers map[string]string, body string, followRedirect bool) error {
	if req := ctx.Value(constants.HTTPRequestContextKey); req != nil {
		if httpReq, ok := req.(*http.Request); ok {
			httpReq.Header.Set(constants.HeaderRequestURL, "true")
			httpReq.Header.Set(constants.HeaderRequestMethod, method)
			httpReq.Header.Set(constants.HeaderRequestTimeout, strconv.Itoa(timeout))
			httpReq.Header.Set(constants.HeaderRequestRetries, strconv.Itoa(retries))
			httpReq.Header.Set("X-FlexProxy-Target-URL", url)
			if body != "" {
				httpReq.Header.Set("X-FlexProxy-Request-Body", body)
			}
			if !followRedirect {
				httpReq.Header.Set("X-FlexProxy-No-Redirect", "true")
			}
			// 设置自定义头部
			for key, value := range headers {
				httpReq.Header.Set("X-FlexProxy-Header-"+key, value)
			}
		}
	}
	return nil
}

// logRequestURLSuccess 记录请求URL成功日志
func logRequestURLSuccess(logger interface{ Info(string, ...interface{}) }, method string, url string, timeout int, retries int) {
	logger.Info(constants.LogMsgRequestURLSuccess, method, url, timeout, retries)
}

// isValidHTTPMethod 验证HTTP方法是否有效
func isValidHTTPMethod(val interface{}) bool {
	if method, ok := val.(string); ok {
		method = strings.ToUpper(method)
		switch method {
		case "GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS":
			return true
		}
	}
	return false
}

// isValidRequestTimeout 验证请求超时时间是否有效
func isValidRequestTimeout(val interface{}) bool {
	timeout := convertToInt(val)
	return timeout > 0 && timeout <= constants.RequestURLMaxTimeout
}

// isValidRequestRetries 验证请求重试次数是否有效
func isValidRequestRetries(val interface{}) bool {
	retries := convertToInt(val)
	return retries >= 0 && retries <= constants.RequestURLMaxRetries
}

// isValidRequestHeaders 验证请求头是否有效
func isValidRequestHeaders(val interface{}) bool {
	switch v := val.(type) {
	case string:
		return len(v) <= 8192 // 限制头部字符串长度
	case map[string]interface{}:
		return len(v) <= 50 // 限制头部数量
	case map[string]string:
		return len(v) <= 50
	}
	return false
}

// =============================================================================
// 缓存执行器相关辅助函数
// =============================================================================

// parseCacheExecutorDuration 解析缓存时间参数
func parseCacheExecutorDuration(parameters map[string]interface{}) int {
	if val, ok := parameters["duration"]; ok {
		if duration := convertToInt(val); duration > 0 {
			return duration
		}
	}
	return constants.CacheExecutorDefaultDuration
}

// parseCacheMaxUseCount 解析最大使用次数参数
func parseCacheMaxUseCount(parameters map[string]interface{}) int {
	if val, ok := parameters["max_use_count"]; ok {
		if count := convertToInt(val); count >= 0 {
			return count
		}
	}
	return constants.CacheExecutorDefaultMaxUse
}

// parseCacheExecutorScope 解析缓存作用域参数
func parseCacheExecutorScope(parameters map[string]interface{}) string {
	if scope, ok := parameters["cache_scope"].(string); ok && scope != "" {
		switch scope {
		case "url", "domain", "global":
			return scope
		}
	}
	return constants.CacheExecutorDefaultScope
}

// parseCustomCacheKey 解析自定义缓存键参数
func parseCustomCacheKey(parameters map[string]interface{}) string {
	if key, ok := parameters["custom_key"].(string); ok {
		return key
	}
	return ""
}

// parseIgnoreParams 解析是否忽略参数
func parseIgnoreParams(parameters map[string]interface{}) bool {
	if val, ok := parameters["ignore_params"].(bool); ok {
		return val
	}
	return false
}

// setCacheExecutorContext 在上下文中设置缓存标记
func setCacheExecutorContext(ctx context.Context, duration int, maxUseCount int, cacheScope string, customKey string, ignoreParams bool) error {
	if req := ctx.Value(constants.HTTPRequestContextKey); req != nil {
		if httpReq, ok := req.(*http.Request); ok {
			httpReq.Header.Set(constants.HeaderCacheExecutor, "true")
			httpReq.Header.Set(constants.HeaderCacheDuration, strconv.Itoa(duration))
			httpReq.Header.Set(constants.HeaderCacheMaxUse, strconv.Itoa(maxUseCount))
			httpReq.Header.Set(constants.HeaderCacheScope, cacheScope)
			httpReq.Header.Set(constants.HeaderIgnoreParams, strconv.FormatBool(ignoreParams))
			if customKey != "" {
				httpReq.Header.Set("X-FlexProxy-Custom-Cache-Key", customKey)
			}
		}
	}
	return nil
}

// logCacheExecutorSuccess 记录缓存执行成功日志
func logCacheExecutorSuccess(logger interface{ Info(string, ...interface{}) }, duration int, maxUseCount int, cacheScope string, ignoreParams bool) {
	logger.Info(constants.LogMsgCacheExecutorSuccess, duration, maxUseCount, cacheScope, ignoreParams)
}

// isValidCacheExecutorDuration 验证缓存时间是否有效
func isValidCacheExecutorDuration(val interface{}) bool {
	duration := convertToInt(val)
	return duration > 0 && duration <= constants.CacheExecutorMaxDuration
}

// isValidCacheMaxUseCount 验证最大使用次数是否有效
func isValidCacheMaxUseCount(val interface{}) bool {
	count := convertToInt(val)
	return count >= 0 // 0表示无限制
}

// isValidCacheScope 验证缓存作用域是否有效
func isValidCacheScope(val interface{}) bool {
	if scope, ok := val.(string); ok {
		switch scope {
		case "url", "domain", "global":
			return true
		}
	}
	return false
}

// =============================================================================
// IP域名封禁执行器相关辅助函数
// =============================================================================

// parseBanTarget 解析封禁目标参数
func parseBanTarget(parameters map[string]interface{}) (string, string, error) {
	// 检查IP地址
	if ip, ok := parameters["ip"].(string); ok && ip != "" {
		if isValidIP(ip) {
			return ip, "ip", nil
		}
		return "", "", errors.ErrBanTargetInvalid
	}

	// 检查域名
	if domain, ok := parameters["domain"].(string); ok && domain != "" {
		if isValidDomain(domain) {
			return domain, "domain", nil
		}
		return "", "", errors.ErrBanTargetInvalid
	}

	// 检查IP段
	if cidr, ok := parameters["cidr"].(string); ok && cidr != "" {
		if isValidCIDR(cidr) {
			return cidr, "cidr", nil
		}
		return "", "", errors.ErrBanTargetInvalid
	}

	return "", "", errors.ErrBanTargetInvalid
}

// parseBanDuration 解析封禁时间参数
func parseBanDuration(parameters map[string]interface{}) int {
	if val, ok := parameters["duration"]; ok {
		if duration := convertToInt(val); duration > 0 {
			return duration
		}
	}
	return constants.BanIPDomainDefaultDuration
}

// parseBanReason 解析封禁原因参数
func parseBanReason(parameters map[string]interface{}) string {
	if reason, ok := parameters["reason"].(string); ok && reason != "" {
		return reason
	}
	return constants.BanIPDomainDefaultReason
}

// parsePermanentBan 解析是否永久封禁参数
func parsePermanentBan(parameters map[string]interface{}) bool {
	if val, ok := parameters["permanent"].(bool); ok {
		return val
	}
	return false
}

// setBanIPDomainContext 在上下文中设置封禁标记
func setBanIPDomainContext(ctx context.Context, target string, targetType string, duration int, reason string, permanent bool) error {
	if req := ctx.Value(constants.HTTPRequestContextKey); req != nil {
		if httpReq, ok := req.(*http.Request); ok {
			httpReq.Header.Set(constants.HeaderBanIPDomain, "true")
			httpReq.Header.Set(constants.HeaderBanTarget, target)
			httpReq.Header.Set(constants.HeaderBanDuration, strconv.Itoa(duration))
			httpReq.Header.Set(constants.HeaderBanReason, reason)
			httpReq.Header.Set("X-FlexProxy-Ban-Type", targetType)
			if permanent {
				httpReq.Header.Set("X-FlexProxy-Ban-Permanent", "true")
			}
		}
	}
	return nil
}

// logBanIPDomainSuccess 记录封禁成功日志
func logBanIPDomainSuccess(logger interface{ Info(string, ...interface{}) }, target string, duration int, reason string, targetType string) {
	logger.Info(constants.LogMsgBanIPDomainSuccess, target, duration, reason, targetType)
}

// isValidIP 验证IP地址是否有效
func isValidIP(ip string) bool {
	// 简单的IP地址验证
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return false
	}
	for _, part := range parts {
		if num, err := strconv.Atoi(part); err != nil || num < 0 || num > 255 {
			return false
		}
	}
	return true
}

// isValidDomain 验证域名是否有效
func isValidDomain(domain string) bool {
	// 简单的域名验证
	if len(domain) == 0 || len(domain) > 253 {
		return false
	}
	if strings.Contains(domain, "..") {
		return false
	}
	// 允许通配符域名
	if strings.HasPrefix(domain, "*.") {
		domain = domain[2:]
	}
	parts := strings.Split(domain, ".")
	for _, part := range parts {
		if len(part) == 0 || len(part) > 63 {
			return false
		}
	}
	return true
}

// isValidCIDR 验证CIDR格式是否有效
func isValidCIDR(cidr string) bool {
	parts := strings.Split(cidr, "/")
	if len(parts) != 2 {
		return false
	}
	if !isValidIP(parts[0]) {
		return false
	}
	if mask, err := strconv.Atoi(parts[1]); err != nil || mask < 0 || mask > 32 {
		return false
	}
	return true
}

// isValidBanDuration 验证封禁时间是否有效
func isValidBanDuration(val interface{}) bool {
	duration := convertToInt(val)
	return duration > 0 && duration <= constants.BanIPDomainMaxDuration
}

// isValidBanReason 验证封禁原因是否有效
func isValidBanReason(val interface{}) bool {
	if reason, ok := val.(string); ok {
		return len(reason) > 0 && len(reason) <= 255
	}
	return false
}
