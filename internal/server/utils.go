package server

import (
	"context"
	"os"
	"path/filepath"
	"time"

	"github.com/fsnotify/fsnotify"
)

// Stop stops the server and all gateways (if any).
func Stop(ctx context.Context) {
	if len(handler.Gateways) > 0 {
		handler.mu.Lock()
		defer handler.mu.Unlock()

		for _, gateway := range handler.Gateways {
			_ = gateway.Close(ctx)
		}
	}

	_ = server.Shutdown(ctx)
}

func interrupt(sig chan os.Signal) {
	<-sig
	serverLogger.Warn("已中断. 退出...")

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	Stop(ctx)
}

// func watch(w *fsnotify.Watcher) {
// 	for {
// 		select {
// 		case event := <-w.Events:
// 			if event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Create == fsnotify.Create {
// 				log.Info("代理文件已更改，正在重新加载...")

//					err := handler.Options.ProxyManager.Reload()
//					if err != nil {
//						log.Fatal(err)
//					}
//				}
//			case err := <-w.Errors:
//				log.Fatal(err)
//			}
//		}
//	}
//
// watch 监控代理文件变更
func watch(w *fsnotify.Watcher, configFile string) {
	configFileAbs, _ := filepath.Abs(configFile)
	for {
		select {
		case event := <-w.Events:
			if event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Create == fsnotify.Create {
				eventFileAbs, _ := filepath.Abs(event.Name)
				if eventFileAbs != configFileAbs {
					// 代理文件变更
					serverLogger.GetRawLogger().Infof("代理文件已更改（%s），正在重新加载...", event.Name)
					err := handler.Options.ProxyManager.Reload()
					if err != nil {
						serverLogger.GetRawLogger().Fatalf(err.Error())
					}
				}
				// 配置文件变更由 ConfigService 处理
			}
		case err := <-w.Errors:
			serverLogger.GetRawLogger().Fatalf(err.Error())
		}
	}
}
