package certificate

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
)

// checkWindowsCertificate 检查Windows系统证书
func (cm *CertificateManager) checkWindowsCertificate() (bool, error) {
	// 使用PowerShell检查证书存储
	cmd := exec.Command("powershell", "-Command",
		fmt.Sprintf(`Get-ChildItem -Path "Cert:\LocalMachine\Root" | Where-Object {$_.Subject -like "*FlexProxy Root CA*"}`))

	output, err := cmd.Output()
	if err != nil {
		return false, fmt.Errorf("检查Windows证书失败: %v", err)
	}

	return len(strings.TrimSpace(string(output))) > 0, nil
}

// checkMacOSCertificate 检查macOS系统证书
func (cm *CertificateManager) checkMacOSCertificate() (bool, error) {
	// 使用security命令检查Keychain
	cmd := exec.Command("security", "find-certificate", "-c", "FlexProxy Root CA", "/Library/Keychains/System.keychain")

	err := cmd.Run()
	return err == nil, nil
}

// checkLinuxCertificate 检查Linux系统证书
func (cm *CertificateManager) checkLinuxCertificate() (bool, error) {
	// 检查常见的证书目录
	certDirs := []string{
		"/etc/ssl/certs",
		"/usr/local/share/ca-certificates",
		"/etc/ca-certificates/trust-source/anchors",
	}

	for _, dir := range certDirs {
		certPath := filepath.Join(dir, "flexproxy-ca.crt")
		if _, err := os.Stat(certPath); err == nil {
			return true, nil
		}
	}

	return false, nil
}

// InstallCertificate 安装证书到系统
func (cm *CertificateManager) InstallCertificate() error {
	switch runtime.GOOS {
	case "windows":
		return cm.installWindowsCertificate()
	case "darwin":
		return cm.installMacOSCertificate()
	case "linux":
		return cm.installLinuxCertificate()
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}
}

// installWindowsCertificate 安装Windows证书
func (cm *CertificateManager) installWindowsCertificate() error {
	certPath := cm.GetCACertPath()

	// 使用certlm.msc或PowerShell安装证书
	cmd := exec.Command("powershell", "-Command",
		fmt.Sprintf(`Import-Certificate -FilePath "%s" -CertStoreLocation "Cert:\LocalMachine\Root"`, certPath))

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("安装Windows证书失败: %v, 输出: %s", err, string(output))
	}

	cm.logger.Info("Windows证书安装成功")
	return nil
}

// installMacOSCertificate 安装macOS证书
func (cm *CertificateManager) installMacOSCertificate() error {
	certPath := cm.GetCACertPath()

	// 使用security命令安装证书
	cmd := exec.Command("sudo", "security", "add-trusted-cert", "-d", "-r", "trustRoot",
		"-k", "/Library/Keychains/System.keychain", certPath)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("安装macOS证书失败: %v, 输出: %s", err, string(output))
	}

	cm.logger.Info("macOS证书安装成功")
	return nil
}

// installLinuxCertificate 安装Linux证书
func (cm *CertificateManager) installLinuxCertificate() error {
	certPath := cm.GetCACertPath()

	// 尝试不同的Linux发行版方法
	if err := cm.installUbuntuCertificate(certPath); err == nil {
		return nil
	}

	if err := cm.installCentOSCertificate(certPath); err == nil {
		return nil
	}

	if err := cm.installArchCertificate(certPath); err == nil {
		return nil
	}

	return fmt.Errorf("无法确定Linux发行版或安装方法")
}

// installUbuntuCertificate 安装Ubuntu/Debian证书
func (cm *CertificateManager) installUbuntuCertificate(certPath string) error {
	// 复制证书到ca-certificates目录
	destPath := "/usr/local/share/ca-certificates/flexproxy-ca.crt"

	cmd := exec.Command("sudo", "cp", certPath, destPath)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("复制证书失败: %v", err)
	}

	// 更新证书
	cmd = exec.Command("sudo", "update-ca-certificates")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("更新证书失败: %v", err)
	}

	cm.logger.Info("Ubuntu/Debian证书安装成功")
	return nil
}

// installCentOSCertificate 安装CentOS/RHEL证书
func (cm *CertificateManager) installCentOSCertificate(certPath string) error {
	// 复制证书到anchors目录
	destPath := "/etc/pki/ca-trust/source/anchors/flexproxy-ca.crt"

	cmd := exec.Command("sudo", "cp", certPath, destPath)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("复制证书失败: %v", err)
	}

	// 更新证书
	cmd = exec.Command("sudo", "update-ca-trust")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("更新证书失败: %v", err)
	}

	cm.logger.Info("CentOS/RHEL证书安装成功")
	return nil
}

// installArchCertificate 安装Arch Linux证书
func (cm *CertificateManager) installArchCertificate(certPath string) error {
	// 复制证书到trust目录
	destPath := "/etc/ca-certificates/trust-source/anchors/flexproxy-ca.crt"

	cmd := exec.Command("sudo", "cp", certPath, destPath)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("复制证书失败: %v", err)
	}

	// 更新证书
	cmd = exec.Command("sudo", "trust", "extract-compat")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("更新证书失败: %v", err)
	}

	cm.logger.Info("Arch Linux证书安装成功")
	return nil
}

// GenerateInstallScript 生成安装脚本
func (cm *CertificateManager) GenerateInstallScript() (string, error) {
	certPath := cm.GetCACertPath()
	scriptDir := filepath.Join(cm.certDir, "scripts")

	if err := os.MkdirAll(scriptDir, 0755); err != nil {
		return "", fmt.Errorf("创建脚本目录失败: %v", err)
	}

	switch runtime.GOOS {
	case "windows":
		return cm.generateWindowsScript(scriptDir, certPath)
	case "darwin":
		return cm.generateMacOSScript(scriptDir, certPath)
	case "linux":
		return cm.generateLinuxScript(scriptDir, certPath)
	default:
		return "", fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}
}

// generateWindowsScript 生成Windows安装脚本
func (cm *CertificateManager) generateWindowsScript(scriptDir, certPath string) (string, error) {
	scriptPath := filepath.Join(scriptDir, "install-cert.bat")

	script := fmt.Sprintf(`@echo off
echo 正在安装FlexProxy CA证书...
powershell -Command "Import-Certificate -FilePath '%s' -CertStoreLocation 'Cert:\LocalMachine\Root'"
if %%ERRORLEVEL%% EQU 0 (
    echo 证书安装成功！
) else (
    echo 证书安装失败，请以管理员身份运行此脚本
)
pause
`, certPath)

	if err := os.WriteFile(scriptPath, []byte(script), 0755); err != nil {
		return "", fmt.Errorf("创建Windows脚本失败: %v", err)
	}

	return scriptPath, nil
}

// generateMacOSScript 生成macOS安装脚本
func (cm *CertificateManager) generateMacOSScript(scriptDir, certPath string) (string, error) {
	scriptPath := filepath.Join(scriptDir, "install-cert.sh")

	script := fmt.Sprintf(`#!/bin/bash
echo "正在安装FlexProxy CA证书..."
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain "%s"
if [ $? -eq 0 ]; then
    echo "证书安装成功！"
else
    echo "证书安装失败，请确保有sudo权限"
fi
`, certPath)

	if err := os.WriteFile(scriptPath, []byte(script), 0755); err != nil {
		return "", fmt.Errorf("创建macOS脚本失败: %v", err)
	}

	return scriptPath, nil
}

// generateLinuxScript 生成Linux安装脚本
func (cm *CertificateManager) generateLinuxScript(scriptDir, certPath string) (string, error) {
	scriptPath := filepath.Join(scriptDir, "install-cert.sh")

	script := fmt.Sprintf(`#!/bin/bash
echo "正在安装FlexProxy CA证书..."

# 检测Linux发行版
if [ -f /etc/debian_version ]; then
    # Ubuntu/Debian
    sudo cp "%s" /usr/local/share/ca-certificates/flexproxy-ca.crt
    sudo update-ca-certificates
elif [ -f /etc/redhat-release ]; then
    # CentOS/RHEL
    sudo cp "%s" /etc/pki/ca-trust/source/anchors/flexproxy-ca.crt
    sudo update-ca-trust
elif [ -f /etc/arch-release ]; then
    # Arch Linux
    sudo cp "%s" /etc/ca-certificates/trust-source/anchors/flexproxy-ca.crt
    sudo trust extract-compat
else
    echo "未知的Linux发行版，请手动安装证书"
    exit 1
fi

if [ $? -eq 0 ]; then
    echo "证书安装成功！"
else
    echo "证书安装失败，请确保有sudo权限"
fi
`, certPath, certPath, certPath)

	if err := os.WriteFile(scriptPath, []byte(script), 0755); err != nil {
		return "", fmt.Errorf("创建Linux脚本失败: %v", err)
	}

	return scriptPath, nil
}

// GetInstallInstructions 获取手动安装说明
func (cm *CertificateManager) GetInstallInstructions() string {
	certPath := cm.GetCACertPath()

	switch runtime.GOOS {
	case "windows":
		return fmt.Sprintf(`Windows证书安装说明：

方法1 - 使用PowerShell（推荐）：
1. 以管理员身份打开PowerShell
2. 运行命令：Import-Certificate -FilePath "%s" -CertStoreLocation "Cert:\LocalMachine\Root"

方法2 - 使用图形界面：
1. 双击证书文件：%s
2. 点击"安装证书"
3. 选择"本地计算机"
4. 选择"将所有的证书都放入下列存储"
5. 点击"浏览"，选择"受信任的根证书颁发机构"
6. 点击"确定"完成安装

方法3 - 使用生成的脚本：
运行：%s
`, certPath, certPath, filepath.Join(cm.certDir, "scripts", "install-cert.bat"))

	case "darwin":
		return fmt.Sprintf(`macOS证书安装说明：

方法1 - 使用命令行（推荐）：
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain "%s"

方法2 - 使用Keychain Access：
1. 打开"钥匙串访问"应用
2. 将证书文件拖拽到"系统"钥匙串
3. 双击证书，展开"信任"部分
4. 将"使用此证书时"设置为"始终信任"

方法3 - 使用生成的脚本：
运行：bash %s
`, certPath, filepath.Join(cm.certDir, "scripts", "install-cert.sh"))

	case "linux":
		return fmt.Sprintf(`Linux证书安装说明：

Ubuntu/Debian：
sudo cp "%s" /usr/local/share/ca-certificates/flexproxy-ca.crt
sudo update-ca-certificates

CentOS/RHEL：
sudo cp "%s" /etc/pki/ca-trust/source/anchors/flexproxy-ca.crt
sudo update-ca-trust

Arch Linux：
sudo cp "%s" /etc/ca-certificates/trust-source/anchors/flexproxy-ca.crt
sudo trust extract-compat

使用生成的脚本：
bash %s
`, certPath, certPath, certPath, filepath.Join(cm.certDir, "scripts", "install-cert.sh"))

	default:
		return "不支持的操作系统"
	}
}
