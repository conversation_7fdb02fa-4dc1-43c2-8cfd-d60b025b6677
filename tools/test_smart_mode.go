package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/common/constants"
	"github.com/flexp/flexp/internal/proxymanager"
	"github.com/flexp/flexp/internal/services"
	"github.com/flexp/flexp/internal/strategy"
)

func main() {
	fmt.Println("=== FlexProxy 智能模式测试 ===")

	// 测试配置验证
	testConfigValidation()

	// 测试代理管理器智能模式
	testProxyManagerSmartMode()

	// 测试代理服务智能模式
	testProxyServiceSmartMode()

	// 测试智能策略
	testSmartStrategy()

	fmt.Println("=== 所有测试完成 ===")
}

func testConfigValidation() {
	fmt.Println("\n--- 测试配置验证 ---")

	// 测试有效的智能模式配置
	validModes := []string{"random", "sequential", "quality", "smart"}
	for _, mode := range validModes {
		config := &common.Config{
			Global: common.GlobalConfig{
				IPRotationMode: mode,
			},
		}
		fmt.Printf("测试模式 '%s': ", mode)
		if validateIPRotationMode(config.Global.IPRotationMode) {
			fmt.Println("✓ 有效")
		} else {
			fmt.Println("✗ 无效")
		}
	}

	// 测试无效配置
	invalidMode := "invalid_mode"
	fmt.Printf("测试无效模式 '%s': ", invalidMode)
	if validateIPRotationMode(invalidMode) {
		fmt.Println("✗ 应该无效但通过了验证")
	} else {
		fmt.Println("✓ 正确识别为无效")
	}
}

func validateIPRotationMode(mode string) bool {
	validModes := []string{"random", "sequential", "quality", "smart"}
	for _, validMode := range validModes {
		if mode == validMode {
			return true
		}
	}
	return false
}

func testProxyManagerSmartMode() {
	fmt.Println("\n--- 测试代理管理器智能模式 ---")

	// 创建测试配置
	config := &common.Config{
		Global: common.GlobalConfig{
			IPRotationMode: constants.StrategySmart,
			Enable:         true,
		},
	}

	// 创建代理管理器
	pm := &proxymanager.ProxyManager{
		Config:  config,
		Proxies: []string{"proxy1:8080", "proxy2:8080", "proxy3:8080"},
	}

	// 测试智能模式代理选择
	fmt.Println("测试智能模式代理选择...")
	proxy, err := pm.GetProxyForDomain("smart", "example.com")
	if err != nil {
		fmt.Printf("✗ 错误: %v\n", err)
	} else {
		fmt.Printf("✓ 选择的代理: %s\n", proxy)
	}

	// 测试多次调用的一致性
	fmt.Println("测试多次调用的一致性...")
	for i := 0; i < 3; i++ {
		proxy, err := pm.GetProxyForDomain("smart", "example.com")
		if err != nil {
			fmt.Printf("✗ 第%d次调用错误: %v\n", i+1, err)
		} else {
			fmt.Printf("第%d次调用选择的代理: %s\n", i+1, proxy)
		}
		time.Sleep(100 * time.Millisecond)
	}
}

func testProxyServiceSmartMode() {
	fmt.Println("\n--- 测试代理服务智能模式 ---")

	// 创建测试配置
	config := &common.Config{
		Global: common.GlobalConfig{
			IPRotationMode: constants.StrategySmart,
		},
	}

	// 创建代理服务（这里简化测试，实际使用中需要通过依赖注入）
	proxies := []string{"proxy1:8080", "proxy2:8080", "proxy3:8080"}
	fmt.Printf("初始化代理服务，代理数量: %d\n", len(proxies))

	// 模拟智能选择逻辑
	fmt.Println("模拟智能选择逻辑...")
	for i := 0; i < 5; i++ {
		// 这里简化实现，实际中会调用服务的GetNextProxy方法
		selectedProxy := proxies[i%len(proxies)]
		fmt.Printf("第%d次选择: %s\n", i+1, selectedProxy)
	}
}

func testSmartStrategy() {
	fmt.Println("\n--- 测试智能策略 ---")

	// 创建智能策略
	strategy := strategy.NewSmartProxyStrategy()
	proxies := []string{"proxy1:8080", "proxy2:8080", "proxy3:8080"}

	// 测试正常选择
	fmt.Println("测试正常代理选择...")
	ctx := context.Background()
	proxy, err := strategy.SelectProxy(ctx, proxies, "example.com")
	if err != nil {
		fmt.Printf("✗ 错误: %v\n", err)
	} else {
		fmt.Printf("✓ 选择的代理: %s\n", proxy)
	}

	// 测试重试场景
	fmt.Println("测试重试场景...")
	retryCtx := context.WithValue(ctx, "retry_action_type", "retry")
	retryCtx = context.WithValue(retryCtx, "action_requires_retry", true)

	proxy, err = strategy.SelectProxy(retryCtx, proxies, "example.com")
	if err != nil {
		fmt.Printf("✗ 重试场景错误: %v\n", err)
	} else {
		fmt.Printf("✓ 重试场景选择的代理: %s\n", proxy)
	}

	// 测试同IP重试场景
	fmt.Println("测试同IP重试场景...")
	retrySameCtx := context.WithValue(ctx, "retry_action_type", "retry_same")
	retrySameCtx = context.WithValue(retrySameCtx, "action_requires_retry", true)

	proxy, err = strategy.SelectProxy(retrySameCtx, proxies, "example.com")
	if err != nil {
		fmt.Printf("✗ 同IP重试场景错误: %v\n", err)
	} else {
		fmt.Printf("✓ 同IP重试场景选择的代理: %s\n", proxy)
	}

	// 测试指标更新
	fmt.Println("测试指标更新...")
	strategy.UpdateMetrics("proxy1:8080", true, 100*time.Millisecond, "example.com")
	strategy.UpdateMetrics("proxy2:8080", false, 500*time.Millisecond, "example.com")
	fmt.Println("✓ 指标更新完成")
}
