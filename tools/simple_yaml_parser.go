package main

import (
	"fmt"
	"io/ioutil"
	"os"

	"gopkg.in/yaml.v3"
)

// SimpleConfig 简化的配置结构，只包含必需字段
type SimpleConfig struct {
	Global struct {
		Enable bool `yaml:"enable"`
	} `yaml:"global"`
	Server struct {
		Host           string `yaml:"host"`
		Port           int    `yaml:"port"`
		HTTPSPort      int    `yaml:"https_port"`
		SOCKSPort      int    `yaml:"socks_port"`
		BufferSize     int    `yaml:"buffer_size"`
		MaxHeaderBytes int    `yaml:"max_header_bytes"`
	} `yaml:"server"`
	Proxy struct {
		Strategy     string `yaml:"strategy"`
		LoadBalancer string `yaml:"load_balancer"`
		PoolSize     int    `yaml:"pool_size"`
	} `yaml:"proxy"`
	Protocols struct {
		Version string `yaml:"version"`
	} `yaml:"protocols"`
	Development struct {
		Runtime struct {
			Mode string `yaml:"mode"`
		} `yaml:"runtime"`
	} `yaml:"development"`
}

func main() {
	configFile := "complete_config.yaml"
	if len(os.Args) > 1 {
		configFile = os.Args[1]
	}

	// 读取 YAML 文件
	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		fmt.Printf("Error reading file %s: %v\n", configFile, err)
		os.Exit(1)
	}

	// 解析 YAML 到简化结构
	var config SimpleConfig
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		fmt.Printf("YAML parsing error in %s: %v\n", configFile, err)
		os.Exit(1)
	}

	fmt.Printf("Successfully parsed %s!\n", configFile)
	fmt.Printf("Global.Enable: %v\n", config.Global.Enable)
	fmt.Printf("Server.Port: %d\n", config.Server.Port)
	fmt.Printf("Server.HTTPSPort: %d\n", config.Server.HTTPSPort)
	fmt.Printf("Server.SOCKSPort: %d\n", config.Server.SOCKSPort)
	fmt.Printf("Server.BufferSize: %d\n", config.Server.BufferSize)
	fmt.Printf("Server.MaxHeaderBytes: %d\n", config.Server.MaxHeaderBytes)
	fmt.Printf("Proxy.Strategy: %s\n", config.Proxy.Strategy)
	fmt.Printf("Proxy.LoadBalancer: %s\n", config.Proxy.LoadBalancer)
	fmt.Printf("Proxy.PoolSize: %d\n", config.Proxy.PoolSize)
	fmt.Printf("Protocols.Version: %s\n", config.Protocols.Version)
	fmt.Printf("Development.Runtime.Mode: %s\n", config.Development.Runtime.Mode)
}
