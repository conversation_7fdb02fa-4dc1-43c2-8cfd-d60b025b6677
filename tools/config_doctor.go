package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/common/errors"
)

func main() {
	fmt.Println("=== FlexProxy 配置诊断工具 ===")
	fmt.Println("此工具将帮助您诊断和修复配置文件问题")
	fmt.Println()

	// 获取配置文件路径
	configFile := "config.yaml"
	if len(os.Args) > 1 {
		configFile = os.Args[1]
	}

	// 转换为绝对路径
	absPath, err := filepath.Abs(configFile)
	if err != nil {
		fmt.Printf("❌ 无法获取配置文件绝对路径: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("正在诊断配置文件: %s\n", absPath)
	fmt.Println(strings.Repeat("=", 60))

	// 步骤1: 检查文件存在性
	fmt.Println("📁 步骤1: 检查配置文件存在性")
	if _, err := os.Stat(absPath); os.IsNotExist(err) {
		fmt.Printf("❌ 配置文件不存在: %s\n", absPath)
		fmt.Println("💡 解决方案:")
		fmt.Println("   - 检查文件路径是否正确")
		fmt.Println("   - 确保配置文件已创建")
		fmt.Println("   - 使用示例配置文件作为模板")
		os.Exit(3)
	}
	fmt.Println("✅ 配置文件存在")

	// 步骤2: 检查文件可读性
	fmt.Println("\n📖 步骤2: 检查文件可读性")
	if _, err := os.Open(absPath); err != nil {
		fmt.Printf("❌ 无法读取配置文件: %v\n", err)
		fmt.Println("💡 解决方案:")
		fmt.Println("   - 检查文件权限")
		fmt.Println("   - 确保当前用户有读取权限")
		os.Exit(3)
	}
	fmt.Println("✅ 配置文件可读")

	// 步骤3: 尝试解析YAML
	fmt.Println("\n🔍 步骤3: 解析YAML格式")
	cfg, err := common.LoadConfigFromYAMLWithoutValidation(absPath)
	if err != nil {
		fmt.Printf("❌ YAML解析失败: %v\n", err)
		fmt.Println("💡 解决方案:")
		fmt.Println("   - 检查YAML语法是否正确")
		fmt.Println("   - 确保缩进使用空格而非制表符")
		fmt.Println("   - 检查引号和特殊字符的使用")
		fmt.Println("   - 使用YAML验证工具检查语法")
		os.Exit(2)
	}
	fmt.Println("✅ YAML格式正确")

	// 步骤4: 执行配置验证
	fmt.Println("\n✅ 步骤4: 执行配置验证")
	if err := common.ValidateConfig(cfg); err != nil {
		fmt.Printf("❌ 配置验证失败: %v\n", err)
		fmt.Println()

		// 提供详细的错误分析和解决方案
		analyzeConfigError(err, cfg)
		os.Exit(2)
	}
	fmt.Println("✅ 配置验证通过")

	// 步骤5: 检查关键依赖
	fmt.Println("\n🔗 步骤5: 检查关键依赖")
	checkDependencies(cfg)

	// 步骤6: 提供配置优化建议
	fmt.Println("\n💡 步骤6: 配置优化建议")
	provideOptimizationSuggestions(cfg)

	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("🎉 配置诊断完成！您的配置文件没有问题。")
}

// analyzeConfigError 分析配置错误并提供解决方案
func analyzeConfigError(err error, cfg *common.Config) {
	fmt.Println("🔧 错误分析和解决方案:")

	if flexErr, ok := err.(*errors.FlexProxyError); ok {
		fmt.Printf("   错误类型: %s\n", flexErr.Type)
		fmt.Printf("   错误代码: %s\n", flexErr.Code)
		fmt.Printf("   错误信息: %s\n", flexErr.Message)

		if flexErr.Details != "" {
			fmt.Printf("   详细信息: %s\n", flexErr.Details)
		}

		// 根据错误类型提供具体的解决方案
		switch flexErr.Type {
		case errors.ErrTypeValidation:
			fmt.Println("\n💡 验证错误解决方案:")
			if strings.Contains(flexErr.Message, "代理文件") {
				fmt.Println("   - 检查 global.proxy_file 字段是否正确设置")
				fmt.Println("   - 确保代理文件存在且可读")
				fmt.Println("   - 代理文件格式应为每行一个代理URL")
			}
			if strings.Contains(flexErr.Message, "端口") {
				fmt.Println("   - 端口号必须在1-65535范围内")
				fmt.Println("   - 避免使用1024以下的系统保留端口")
				fmt.Println("   - 确保端口未被其他程序占用")
			}
			if strings.Contains(flexErr.Message, "认证") {
				fmt.Println("   - 检查 security.auth.type 字段")
				fmt.Println("   - 支持的认证类型: none, basic, bearer, apikey")
			}
		case errors.ErrTypeConfig:
			fmt.Println("\n💡 配置错误解决方案:")
			fmt.Println("   - 检查必需字段是否存在")
			fmt.Println("   - 验证字段值的类型和格式")
			fmt.Println("   - 参考示例配置文件")
		}
	}
}

// checkDependencies 检查关键依赖
func checkDependencies(cfg *common.Config) {
	// 检查代理文件
	if cfg.Global.ProxyFile != "" {
		if _, err := os.Stat(cfg.Global.ProxyFile); os.IsNotExist(err) {
			fmt.Printf("⚠️  代理文件不存在: %s\n", cfg.Global.ProxyFile)
			fmt.Println("   💡 请创建代理文件或修改配置中的路径")
		} else {
			fmt.Printf("✅ 代理文件存在: %s\n", cfg.Global.ProxyFile)
		}
	}

	// 检查端口可用性（简单检查）
	if cfg.Server != nil && cfg.Server.Port > 0 {
		if cfg.Server.Port < 1024 {
			fmt.Printf("⚠️  使用系统保留端口: %d\n", cfg.Server.Port)
			fmt.Println("   💡 建议使用1024以上的端口")
		} else {
			fmt.Printf("✅ 端口配置合理: %d\n", cfg.Server.Port)
		}
	}
}

// provideOptimizationSuggestions 提供配置优化建议
func provideOptimizationSuggestions(cfg *common.Config) {
	suggestions := []string{}

	// DNS配置建议
	if cfg.DNSService != nil && cfg.DNSService.Enabled {
		if cfg.DNSService.Servers == nil || len(cfg.DNSService.Servers) == 0 {
			suggestions = append(suggestions, "考虑配置DNS服务器以提高域名解析性能")
		}
	}

	// 缓存配置建议
	if cfg.Cache != nil && cfg.Cache.Type == "memory" {
		if cfg.Cache.Size < 100 {
			suggestions = append(suggestions, "考虑增加内存缓存大小以提高性能")
		}
	}

	// 监控配置建议
	if cfg.Monitoring == nil || !cfg.Monitoring.Enabled {
		suggestions = append(suggestions, "建议启用监控功能以便观察代理服务状态")
	}

	// 安全配置建议
	if cfg.Security == nil || cfg.Security.Auth == nil || cfg.Security.Auth.Type == "none" {
		suggestions = append(suggestions, "考虑启用认证以提高安全性")
	}

	if len(suggestions) == 0 {
		fmt.Println("✅ 您的配置已经很好了！")
	} else {
		for i, suggestion := range suggestions {
			fmt.Printf("%d. %s\n", i+1, suggestion)
		}
	}
}
