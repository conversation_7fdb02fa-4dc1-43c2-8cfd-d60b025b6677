package main

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/common/errors"
)

func main() {
	fmt.Println("=== FlexProxy 配置验证测试工具 ===")

	// 获取配置文件路径
	configFile := "config.yaml"
	if len(os.Args) > 1 {
		configFile = os.Args[1]
	}

	// 转换为绝对路径
	absPath, err := filepath.Abs(configFile)
	if err != nil {
		fmt.Printf("❌ 无法获取配置文件绝对路径: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("正在测试配置文件: %s\n", absPath)
	fmt.Println()

	// 测试1: 早期配置文件验证
	fmt.Println("1. 执行早期配置文件验证...")
	if err := common.ValidateConfigFile(absPath); err != nil {
		fmt.Printf("❌ 早期验证失败: %v\n", err)
		printErrorDetails(err)
		os.Exit(2)
	}
	fmt.Println("✅ 早期验证通过")

	// 测试2: 完整配置加载和验证
	fmt.Println("\n2. 执行完整配置加载和验证...")
	cfg, err := common.LoadConfigFromYAML(absPath)
	if err != nil {
		fmt.Printf("❌ 配置加载失败: %v\n", err)
		printErrorDetails(err)
		os.Exit(2)
	}
	fmt.Println("✅ 配置加载成功")

	// 测试3: 显示配置摘要
	fmt.Println("\n3. 配置摘要:")
	printConfigSummary(cfg)

	// 测试4: 验证关键配置项
	fmt.Println("\n4. 验证关键配置项:")
	validateKeyConfigurations(cfg)

	fmt.Println("\n=== 所有验证测试通过 ===")
}

// printErrorDetails 打印详细的错误信息
func printErrorDetails(err error) {
	if flexErr, ok := err.(*errors.FlexProxyError); ok {
		fmt.Printf("   错误类型: %s\n", flexErr.Type)
		fmt.Printf("   错误代码: %s\n", flexErr.Code)
		fmt.Printf("   错误信息: %s\n", flexErr.Message)
		if flexErr.Details != "" {
			fmt.Printf("   详细信息: %s\n", flexErr.Details)
		}
		if flexErr.Cause != nil {
			fmt.Printf("   原始错误: %v\n", flexErr.Cause)
		}
	}
}

// printConfigSummary 打印配置摘要
func printConfigSummary(cfg *common.Config) {
	fmt.Printf("   全局启用状态: %v\n", cfg.Global.Enable)
	fmt.Printf("   代理文件路径: %s\n", cfg.Global.ProxyFile)

	if cfg.Server != nil {
		fmt.Printf("   服务器端口: %d\n", cfg.Server.Port)
		fmt.Printf("   服务器主机: %s\n", cfg.Server.Host)
	}

	if cfg.Security != nil && cfg.Security.Auth != nil {
		fmt.Printf("   认证类型: %s\n", cfg.Security.Auth.Type)
		if cfg.Security.Auth.TokenExpiry != "" {
			fmt.Printf("   Token过期时间: %s\n", cfg.Security.Auth.TokenExpiry)
		}
	}

	if cfg.DNSService != nil {
		fmt.Printf("   DNS服务启用: %v\n", cfg.DNSService.Enabled)
		if cfg.DNSService.Servers != nil {
			fmt.Printf("   DNS服务器数量: %d\n", len(cfg.DNSService.Servers))
		}
		fmt.Printf("   DNS超时: %s\n", cfg.DNSService.Timeout)
	}

	if cfg.Cache != nil {
		fmt.Printf("   缓存类型: %s\n", cfg.Cache.Type)
		fmt.Printf("   缓存大小: %d\n", cfg.Cache.Size)
		fmt.Printf("   TTL: %s\n", cfg.Cache.TTL)
	}

	fmt.Printf("   事件规则数量: %d\n", len(cfg.Events))
	fmt.Printf("   动作序列数量: %d\n", len(cfg.Actions))
}

// validateKeyConfigurations 验证关键配置项
func validateKeyConfigurations(cfg *common.Config) {
	// 检查代理文件是否存在
	if _, err := os.Stat(cfg.Global.ProxyFile); os.IsNotExist(err) {
		fmt.Printf("   ⚠️  代理文件不存在: %s\n", cfg.Global.ProxyFile)
	} else {
		fmt.Printf("   ✅ 代理文件存在: %s\n", cfg.Global.ProxyFile)
	}

	// 检查端口配置
	if cfg.Server != nil {
		if cfg.Server.Port >= 1024 && cfg.Server.Port <= 65535 {
			fmt.Printf("   ✅ 服务器端口配置合理: %d\n", cfg.Server.Port)
		} else if cfg.Server.Port < 1024 {
			fmt.Printf("   ⚠️  使用系统保留端口: %d\n", cfg.Server.Port)
		} else {
			fmt.Printf("   ❌ 端口配置无效: %d\n", cfg.Server.Port)
		}
	}

	// 检查认证配置
	if cfg.Security != nil && cfg.Security.Auth != nil {
		if cfg.Security.Auth.Type != "" {
			fmt.Printf("   ✅ 认证类型已配置: %s\n", cfg.Security.Auth.Type)
			if cfg.Security.Auth.TokenExpiry != "" {
				fmt.Printf("   ✅ Token过期时间已配置: %s\n", cfg.Security.Auth.TokenExpiry)
			}
		} else {
			fmt.Printf("   ❌ 认证类型未配置\n")
		}
	}

	// 检查DNS配置
	if cfg.DNSService != nil && cfg.DNSService.Servers != nil && len(cfg.DNSService.Servers) > 0 {
		fmt.Printf("   ✅ DNS服务器配置存在\n")
		for name, server := range cfg.DNSService.Servers {
			if server != "" {
				fmt.Printf("   ✅ DNS服务器[%s]: %s\n", name, server)
			} else {
				fmt.Printf("   ❌ DNS服务器[%s]: 空地址\n", name)
			}
		}
	}
}
