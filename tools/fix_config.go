package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/flexp/flexp/common"
)

func main() {
	fmt.Println("=== FlexProxy 配置修复工具 ===")
	fmt.Println("此工具将尝试自动修复常见的配置问题")
	fmt.Println()

	// 获取配置文件路径
	configFile := "config.yaml"
	if len(os.Args) > 1 {
		configFile = os.Args[1]
	}

	// 转换为绝对路径
	absPath, err := filepath.Abs(configFile)
	if err != nil {
		fmt.Printf("❌ 无法获取配置文件绝对路径: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("正在修复配置文件: %s\n", absPath)
	fmt.Println(strings.Repeat("=", 60))

	// 检查文件是否存在
	if _, err := os.Stat(absPath); os.IsNotExist(err) {
		fmt.Printf("❌ 配置文件不存在: %s\n", absPath)
		fmt.Println("💡 建议:")
		fmt.Println("   - 从示例配置文件复制一份")
		fmt.Println("   - 或者使用 config.yaml 作为模板")
		os.Exit(3)
	}

	// 尝试加载配置
	cfg, err := common.LoadConfigFromYAMLWithoutValidation(absPath)
	if err != nil {
		fmt.Printf("❌ 无法加载配置文件: %v\n", err)
		fmt.Println("💡 这通常是YAML语法错误，需要手动修复")
		os.Exit(2)
	}

	fmt.Println("✅ 配置文件加载成功")

	// 应用自动修复
	fixed := false

	// 修复全局配置
	if !cfg.Global.Enable {
		fmt.Println("🔧 修复: 启用全局服务")
		cfg.Global.Enable = true
		fixed = true
	}

	if cfg.Global.ProxyFile == "" {
		fmt.Println("🔧 修复: 设置默认代理文件路径")
		cfg.Global.ProxyFile = "./proxies.txt"
		fixed = true
	}

	// 修复服务器配置
	if cfg.Server != nil {
		if cfg.Server.Port <= 0 || cfg.Server.Port > 65535 {
			fmt.Println("🔧 修复: 设置默认服务器端口")
			cfg.Server.Port = 8080
			fixed = true
		}
		if cfg.Server.Host == "" {
			fmt.Println("🔧 修复: 设置默认服务器主机")
			cfg.Server.Host = "0.0.0.0"
			fixed = true
		}
	}

	// 修复认证配置
	if cfg.Security != nil && cfg.Security.Auth != nil {
		if cfg.Security.Auth.Type == "" {
			fmt.Println("🔧 修复: 设置默认认证类型")
			cfg.Security.Auth.Type = "none"
			fixed = true
		}
	}

	// 修复缓存配置
	if cfg.Cache != nil {
		if cfg.Cache.Type == "" {
			fmt.Println("🔧 修复: 设置默认缓存类型")
			cfg.Cache.Type = "memory"
			fixed = true
		}
		if cfg.Cache.Size <= 0 {
			fmt.Println("🔧 修复: 设置默认缓存大小")
			cfg.Cache.Size = 1000
			fixed = true
		}
	}

	// 修复监控配置
	if cfg.Monitoring != nil {
		if cfg.Monitoring.Port <= 0 || cfg.Monitoring.Port > 65535 {
			fmt.Println("🔧 修复: 设置默认监控端口")
			cfg.Monitoring.Port = 9090
			fixed = true
		}
		if cfg.Monitoring.Path == "" {
			fmt.Println("🔧 修复: 设置默认监控路径")
			cfg.Monitoring.Path = "/metrics"
			fixed = true
		}
	}

	// 修复DNS配置
	if cfg.DNSService != nil {
		if cfg.DNSService.Retries < 0 {
			fmt.Println("🔧 修复: 设置默认DNS重试次数")
			cfg.DNSService.Retries = 3
			fixed = true
		}
	}

	if !fixed {
		fmt.Println("✅ 配置文件没有需要自动修复的问题")
	} else {
		fmt.Println("\n📝 应用的修复:")
		fmt.Println("   - 已自动修复常见的配置问题")
		fmt.Println("   - 请手动验证修复结果")
		fmt.Println("   - 建议备份原始配置文件")
	}

	// 验证修复后的配置
	fmt.Println("\n🔍 验证修复后的配置...")
	if err := common.ValidateConfig(cfg); err != nil {
		fmt.Printf("⚠️  修复后仍有验证错误: %v\n", err)
		fmt.Println("💡 这些问题需要手动修复:")
		fmt.Println("   - 检查必需字段的值")
		fmt.Println("   - 验证枚举类型的取值")
		fmt.Println("   - 确保数值在有效范围内")
		os.Exit(2)
	}

	fmt.Println("✅ 修复后的配置验证通过")

	// 检查关键依赖
	fmt.Println("\n🔗 检查关键依赖...")
	if cfg.Global.ProxyFile != "" {
		if _, err := os.Stat(cfg.Global.ProxyFile); os.IsNotExist(err) {
			fmt.Printf("⚠️  代理文件不存在: %s\n", cfg.Global.ProxyFile)
			fmt.Println("💡 请创建代理文件或修改配置中的路径")
		} else {
			fmt.Printf("✅ 代理文件存在: %s\n", cfg.Global.ProxyFile)
		}
	}

	fmt.Println("\n" + strings.Repeat("=", 60))
	if fixed {
		fmt.Println("🎉 配置修复完成！请检查修复结果并重新测试。")
		fmt.Println("💡 注意: 此工具只能修复基本问题，复杂配置仍需手动调整。")
	} else {
		fmt.Println("🎉 配置文件状态良好，无需修复。")
	}
}
