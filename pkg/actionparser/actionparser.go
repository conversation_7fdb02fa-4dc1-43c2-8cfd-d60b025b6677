package actionparser

import (
	"fmt"
	"strings"
)

// ActionWithParams 定义带参数的动作
type ActionWithParams struct {
	Name   string   // 动作名称
	Params []string // 动作参数
}

// ParseActionWithParams 解析动作字符串，提取动作名称和参数
// 例如："retry_same(3)" 将返回 {Name: "retry_same", Params: ["3"]}
// func ParseActionWithParams(actionStr string) *ActionWithParams {
// 	actionStr = strings.TrimSpace(actionStr)
// 	if actionStr == "" {
// 		return nil
// 	}

// 	// 检查是否有参数
// 	openParenIndex := strings.Index(actionStr, "(")
// 	if openParenIndex == -1 {
// 		// 没有参数
// 		return &ActionWithParams{Name: actionStr}
// 	}

// 	// 确保有闭合的括号
// 	closeParenIndex := strings.LastIndex(actionStr, ")")
// 	if closeParenIndex == -1 || closeParenIndex < openParenIndex {
// 		// 格式错误，当作没有参数处理
// 		return &ActionWithParams{Name: actionStr}
// 	}

// 	// 提取动作名称和参数
// 	name := actionStr[:openParenIndex]
// 	paramsStr := actionStr[openParenIndex+1 : closeParenIndex]

// 	// 分割参数（支持逗号分隔的多个参数）
// 	var params []string
// 	if paramsStr != "" {
// 		for _, p := range strings.Split(paramsStr, ",") {
// 			p = strings.TrimSpace(p)
// 			if p != "" {
// 				// 如果参数被引号包围，去除引号
// 				if strings.HasPrefix(p, "\"") && strings.HasSuffix(p, "\"") {
// 					p = p[1 : len(p)-1]
// 				}
// 				params = append(params, p)
// 			}
// 		}
// 	}

// 	return &ActionWithParams{Name: name, Params: params}
// }

func ParseActionWithParams(actionStr string) *ActionWithParams {
	actionStr = strings.TrimSpace(actionStr)
	if actionStr == "" {
		return nil
	}

	// 检查是否有参数
	openParenIndex := strings.Index(actionStr, "(")
	if openParenIndex == -1 {
		// 没有参数
		return &ActionWithParams{Name: actionStr}
	}

	// 确保有闭合的括号
	closeParenIndex := strings.LastIndex(actionStr, ")")
	if closeParenIndex == -1 || closeParenIndex < openParenIndex {
		// 括号不匹配，返回整个字符串作为动作名
		return &ActionWithParams{Name: actionStr}
	}

	// 提取动作名称和参数字符串
	name := strings.TrimSpace(actionStr[:openParenIndex])
	paramsStr := actionStr[openParenIndex+1 : closeParenIndex]

	// 解析参数，正确处理逗号分隔的多个参数
	var params []string
	if paramsStr != "" {
		// 分割参数并去除空白
		params = parseParams(paramsStr)
	}

	return &ActionWithParams{
		Name:   name,
		Params: params,
	}
}

// parseParams 解析参数字符串，正确处理可能包含逗号的参数
func parseParams(paramsStr string) []string {
	var params []string
	var currentParam strings.Builder
	paramsStr = strings.TrimSpace(paramsStr)
	inQuotes := false
	quoteChar := byte(0)

	for i := 0; i < len(paramsStr); i++ {
		char := paramsStr[i]

		if (char == '"' || char == '\'') && (quoteChar == 0 || quoteChar == char) {
			if !inQuotes {
				// 开始引号
				inQuotes = true
				quoteChar = char
			} else {
				// 结束引号
				inQuotes = false
				quoteChar = 0
			}
			currentParam.WriteByte(char)
		} else if char == ',' && !inQuotes {
			// 只有在不在引号内时，才将逗号视为分隔符
			param := strings.TrimSpace(currentParam.String())
			if param != "" {
				params = append(params, param)
			}
			currentParam.Reset()
		} else {
			currentParam.WriteByte(char)
		}
	}

	// 添加最后一个参数
	param := strings.TrimSpace(currentParam.String())
	if param != "" {
		params = append(params, param)
	}

	return params
}

// ParseActionStrings 解析动作字符串列表
func ParseActionStrings(actions []string) []string {
	if actions == nil {
		return []string{}
	}

	var result []string
	for _, action := range actions {
		action = strings.TrimSpace(action)
		if action == "" {
			continue
		}
		// 使用ParseActionWithParams解析动作
		actionWithParams := ParseActionWithParams(action)
		if actionWithParams != nil {
			// 如果有参数，重新构建动作字符串
			if len(actionWithParams.Params) > 0 {
				paramStr := strings.Join(actionWithParams.Params, ",")
				result = append(result, fmt.Sprintf("%s(%s)", actionWithParams.Name, paramStr))
			} else {
				result = append(result, actionWithParams.Name)
			}
		}
	}
	return result
}

// ParseActionWithCondition 解析带条件的动作字符串
// 例如："retry_same(3)|retry(5)" 将返回两个动作组
// func ParseActionWithCondition(actionStr string) []*ActionWithParams {
// 	var result []*ActionWithParams

// 	// 按|分割不同的动作组
// 	actionGroups := strings.Split(actionStr, "|")
// 	for _, group := range actionGroups {
// 		group = strings.TrimSpace(group)
// 		if group == "" {
// 			continue
// 		}

// 		// 解析每个动作组中的动作
// 		actionWithParams := ParseActionWithParams(group)
// 		if actionWithParams != nil {
// 			result = append(result, actionWithParams)
// 		}
// 	}

//		return result
//	}
func ParseActionWithCondition(actionStr string) []*ActionWithParams {
	var result []*ActionWithParams

	// 按|分割不同的动作组
	actionGroups := strings.Split(actionStr, "|")
	for _, group := range actionGroups {
		group = strings.TrimSpace(group)
		if group == "" {
			continue
		}

		// 使用状态机来正确分割带括号的动作
		var actions []string
		var currentAction strings.Builder
		parenCount := 0

		for i := 0; i < len(group); i++ {
			ch := group[i]
			switch ch {
			case '(':
				parenCount++
				currentAction.WriteByte(ch)
			case ')':
				parenCount--
				currentAction.WriteByte(ch)
			case ',':
				if parenCount == 0 {
					// 只有在不在括号内时才分割
					if currentAction.Len() > 0 {
						actions = append(actions, currentAction.String())
						currentAction.Reset()
					}
				} else {
					currentAction.WriteByte(ch)
				}
			default:
				currentAction.WriteByte(ch)
			}
		}

		// 添加最后一个动作
		if currentAction.Len() > 0 {
			actions = append(actions, currentAction.String())
		}

		// 解析每个动作
		for _, action := range actions {
			action = strings.TrimSpace(action)
			if action == "" {
				continue
			}
			actionWithParams := ParseActionWithParams(action)
			if actionWithParams != nil {
				result = append(result, actionWithParams)
			}
		}
	}

	return result
}

// MatchActionGroupIndex 根据触发条件匹配动作组索引
// 例如：状态码为302时，应该匹配第二组动作
func MatchActionGroupIndex(triggerType string, triggerValue string, patterns []string) int {
	if triggerType == "" || triggerValue == "" || len(patterns) == 0 {
		return -1
	}

	for i, pattern := range patterns {
		// 根据触发器类型进行不同的匹配
		switch triggerType {
		case "status":
			// 状态码匹配
			codes := strings.Split(pattern, ",")
			for _, code := range codes {
				if code == triggerValue {
					return i
				}
			}
		case "url":
			// URL匹配
			if strings.Contains(triggerValue, pattern) {
				return i
			}
		case "domain":
			// 域名匹配
			if strings.Contains(triggerValue, pattern) {
				return i
			}
		}
	}

	return -1
}
