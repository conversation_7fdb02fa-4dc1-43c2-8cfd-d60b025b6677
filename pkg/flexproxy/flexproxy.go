package flexproxy

import (
	"net"
	"net/http"
	"net/url"

	"github.com/flexp/flexp/pkg/helper/awsurl"
	"github.com/hashicorp/go-retryablehttp"
)

// New 定义 http.Request 本身的 HTTP 客户端请求。
//
// 同时在发送到后端时移除逐跳头部（参见 http://www.w3.org/Protocols/rfc2616/rfc2616-sec13.html），
// 然后添加带有轮换代理 IP 地址值的 X-Forwarded-For 头部值。
func (proxy *Proxy) New(req *http.Request) (*http.Client, error) {
	client := &http.Client{
		CheckRedirect: proxy.redirectPolicy,
		Timeout:       proxy.Timeout,
		Transport:     proxy.Transport,
	}

	// http: Request.RequestURI 不能在客户端请求中设置。
	// http://golang.org/src/pkg/net/http/client.go
	req.RequestURI = ""

	for _, h := range HopHeaders {
		req.Header.Del(h)
	}

	req.Header.Set("X-Forwarded-Proto", req.URL.Scheme)

	// 如果代理地址是 AWS URL，提前返回。
	if awsurl.IsURL(proxy.Address) {
		return client, nil
	}

	proxyURL, err := url.Parse(proxy.Address)
	if err != nil {
		return client, err
	}

	if host, _, err := net.SplitHostPort(proxyURL.Host); err == nil {
		// if prior, ok := req.Header["X-Forwarded-For"]; ok {
		// 	host = strings.Join(prior, ", ") + ", " + host
		// }
		req.Header.Set("X-Forwarded-For", host)
	}

	return client, nil
}

// redirectPolicy 确定请求是否应该被重定向。
//
// 它检查重定向次数是否超过了代理允许的最大值。如果是，它返回 http.ErrUseLastResponse
// 来指示应该使用最后一个响应。否则，它返回 nil 以允许重定向继续。
func (proxy *Proxy) redirectPolicy(req *http.Request, via []*http.Request) error {
	if len(via) >= proxy.MaxRedirects {
		return http.ErrUseLastResponse
	}

	return nil
}

// ToRetryableHTTPClient 将标准 http.Client 转换为 retryablehttp.Client
func ToRetryableHTTPClient(client *http.Client) *retryablehttp.Client {
	retryablehttpClient := retryablehttp.NewClient()
	retryablehttpClient.HTTPClient = client

	return retryablehttpClient
}
